<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teltonika Device Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            text-align: center;
            padding: 20px;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            display: inline-block;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
        }
        th {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Teltonika Device Data</h2>
        <table id="deviceData">
            <thead>
                <tr>
                    <th>Key</th>
                    <th>Value</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

    <script>
        const urlBase = "https://piattaforma.controllone.it/public/test/data/live.json";

        async function fetchData() {
            try {
                const response = await fetch(`${urlBase}?_=${Math.random()}`);
                const data = await response.json();
                const deviceData = data["864275072084729"];
                if (deviceData) {
                    updateTable(deviceData);
                }
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        }

        function updateTable(data) {
            const tbody = document.querySelector("#deviceData tbody");
            tbody.innerHTML = "";
            Object.entries(data).forEach(([key, value]) => {
                const row = `<tr><td>${key}</td><td>${value}</td></tr>`;
                tbody.innerHTML += row;
            });
        }

        fetchData(); // Initial fetch
        setInterval(fetchData, 1000); // Update every second
    </script>
</body>
</html>
