<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teltonika Device History</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            text-align: center;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            display: inline-block;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        th,
        td {
            padding: 10px;
            border: 1px solid #ddd;
        }

        th {
            background: #007bff;
            color: white;
        }

        input {
            padding: 8px;
            margin-bottom: 10px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h2>Teltonika Device History</h2>
        <input type="date" id="datePicker" onchange="fetchData()">
        <table id="deviceData">
            <thead>
                <tr>
                    <th>Key</th>
                    <th>Value</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>

    <script>
        async function fetchData() {
            const dateInput = document.getElementById('datePicker').value;
            if (!dateInput) return;

            const formattedDate = dateInput.split('-').reverse().join('-');
            const url = `https://piattaforma.controllone.it/public/test/data/history/864275072084729/${formattedDate}.json`;

            try {
                const response = await fetch(`${url}?_=${Math.random()}`); // Prevent caching

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const textData = await response.text(); // Read raw response
                if (!textData) {
                    throw new Error("Empty response from server");
                }

                const data = JSON.parse(textData); // Convert to JSON
                updateTable(data);
            } catch (error) {
                console.error("Error fetching data:", error);
                alert("Failed to load data. Please check if the selected date has records.");
            }
        }

        function updateTable(data) {
            const tbody = document.querySelector("#deviceData tbody");
            tbody.innerHTML = ""; // Clear previous data

            data.forEach(record => {
                Object.entries(record).forEach(([key, value]) => {
                    const row = `<tr><td>${key}</td><td>${value}</td></tr>`;
                    tbody.innerHTML += row;
                });
                tbody.innerHTML += `<tr><td colspan="2" style="background:#ddd;"></td></tr>`; // Separator row
            });
        }
    </script>
</body>

</html>