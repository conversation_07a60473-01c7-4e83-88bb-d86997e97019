<svg width="50" height="63" viewBox="0 0 50 63" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_235_12)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M25 0C36.598 0 46 9.40202 46 21C46 22.4157 45.8599 23.7986 45.5929 25.1358C43.7394 39.1032 25.1104 55 25.1104 55C25.1104 55 9.25689 41.4717 5.34456 28.4096C4.47551 26.1054 4 23.6083 4 21C4 9.40202 13.402 0 25 0Z" fill="black"/>
<g clip-path="url(#clip0_235_12)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 20.5C12 16.9196 13.4223 13.4858 15.9541 10.9541C18.4858 8.42232 21.9196 7 25.5 7C29.0804 7 32.5142 8.42232 35.0459 10.9541C37.5777 13.4858 39 16.9196 39 20.5C39 24.0804 37.5777 27.5142 35.0459 30.0459C32.5142 32.5777 29.0804 34 25.5 34C21.9196 34 18.4858 32.5777 15.9541 30.0459C13.4223 27.5142 12 24.0804 12 20.5ZM21 16H30V25H21V16Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_235_12" x="0" y="0" width="50" height="63" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_235_12"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_235_12" result="shape"/>
</filter>
<clipPath id="clip0_235_12">
<rect width="27" height="27" fill="white" transform="translate(12 7)"/>
</clipPath>
</defs>
</svg>
