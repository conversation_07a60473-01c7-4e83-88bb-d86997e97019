import crcmod.predefined
import binascii
from struct import unpack

def calculate_crc(data: bytes) -> bytes:
    # Use CRC-16/IBM
    crc16_func = crcmod.predefined.mkPredefinedCrcFun('crc-16')
    crc_value = crc16_func(data)
    return crc_value.to_bytes(2, byteorder='big')  # Convert to 2-byte big-endian

def create_codec12_command(command: str) -> bytes:
    # Preamble (4 bytes)
    preamble = b'\x00\x00\x00\x00'
    # Codec ID (1 byte)
    codec_id = b'\x0C'
    # Command Quantity 1 (1 byte)
    command_quantity_1 = b'\x01'
    # Command Type (1 byte)
    command_type = b'\x05'
    # Command in ASCII
    command_bytes = command.encode('ascii')
    # Command Size (4 bytes)
    command_size = len(command_bytes).to_bytes(4, byteorder='big')
    # Command Quantity 2 (1 byte)
    command_quantity_2 = b'\x01'

    # Data to calculate CRC
    data_without_crc = (
        codec_id +
        command_quantity_1 +
        command_type +
        command_size +
        command_bytes +
        command_quantity_2
    )

    # Calculate CRC for `data_without_crc`
    crc = calculate_crc(data_without_crc)

    # Teltonika's requirement: Add two leading zero bytes to the CRC
    crc_with_padding = b'\x00\x00' + crc

    # Data Size (4 bytes, big-endian)
    data_size = len(data_without_crc).to_bytes(4, byteorder='big')

    # Final packet structure
    packet = (
        preamble +
        data_size +
        data_without_crc +
        crc_with_padding
    )
    return packet




class CommandResponse:
    def __init__(self, preamble, data_size, codec_id, response_quantity_1, response_type, response_size, response, response_quantity_2, crc):
        self.preamble = preamble
        self.data_size = data_size
        self.codec_id = codec_id
        self.response_quantity_1 = response_quantity_1
        self.response_type = response_type
        self.response_size = response_size
        self.response = response
        self.response_quantity_2 = response_quantity_2
        self.crc = crc

    def __repr__(self):
        return (
            f"CommandResponse(\n"
            f"  Preamble: {self.preamble}\n"
            f"  Data Size: {self.data_size}\n"
            f"  Codec ID: {self.codec_id}\n"
            f"  Response Quantity 1: {self.response_quantity_1}\n"
            f"  Response Type: {self.response_type}\n"
            f"  Response Size: {self.response_size}\n"
            f"  Response: {self.response.decode('ascii', errors='ignore')}\n"
            f"  Response Quantity 2: {self.response_quantity_2}\n"
            f"  CRC: {hex(self.crc)}\n"
            f")"
        )


def decode_command_response(hex_response: str) -> CommandResponse:
    # Clean and validate the hex string
    hex_response = ''.join(c for c in hex_response if c in '0123456789abcdefABCDEF')

    # Convert the hex string to raw bytes
    try:
        response_bytes = binascii.unhexlify(hex_response)
    except binascii.Error as e:
        raise ValueError(f"Invalid hex string: {hex_response}") from e

    # Debugging: Check response length
    print(f"Hex response length: {len(hex_response)}")
    print(f"Response bytes length: {len(response_bytes)}")

    # Parse the response
    preamble = unpack(">I", response_bytes[:4])[0]
    data_size = unpack(">I", response_bytes[4:8])[0]
    codec_id = response_bytes[8]
    response_quantity_1 = response_bytes[9]
    response_type = response_bytes[10]
    response_size = unpack(">I", response_bytes[11:15])[0]

    # Debugging: Check response_size
    print(f"response_size: {response_size}")

    # Handle case where response_size is larger than available data length
    if response_size > len(response_bytes) - 15:
        print(f"Warning: response_size ({response_size}) is larger than available data length. Using available length.")
        response_size = len(response_bytes) - 15  # Adjust the response size to available data length
        return

    response_start = 15
    response_end = response_start + response_size

    # Ensure response_end is within bounds
    if response_end > len(response_bytes):
        raise IndexError(f"response_end ({response_end}) exceeds response_bytes length ({len(response_bytes)})")

    response = response_bytes[response_start:response_end]

    # Ensure response_end + 2 is within bounds for crc calculation
    if response_end + 4 > len(response_bytes):
        raise IndexError(f"CRC data exceeds response_bytes length ({len(response_bytes)})")

    response_quantity_2 = response_bytes[response_end]
    crc = unpack(">H", response_bytes[response_end + 2:response_end + 4])[0]

    return CommandResponse(
        preamble=preamble,
        data_size=data_size,
        codec_id=codec_id,
        response_quantity_1=response_quantity_1,
        response_type=response_type,
        response_size=response_size,
        response=response,
        response_quantity_2=response_quantity_2,
        crc=crc,
    )


# Example Test Case
# hex_response_1 = "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"

# try:
#     decoded_response = decode_command_response(hex_response_1)
#     print(decoded_response)
# except ValueError as e:
#     print(f"Error: {e}")

# # Example command
# command = "setdigout 1"
# encoded_command = create_codec12_command(command)

# # Print command in hexadecimal
# print("Encoded Command (hex):", encoded_command.hex())


# # Example Test Cases
# hex_response_1 = "00000000000000a40c01060000009c5254433a323032342f31322f31342031303a353820496e69743a323032342f31322f31302031323a323420557054696d653a33333938323373205057523a536f66745265736574205253543a34204750533a33205341543a313620545446463a362054544c463a36204e4f4750533a303a302053523a32373936362046473a3020464c3a343120534d533a30205245433a30204d443a342044423a30010000cbb3"
# decoded_response_1 = decode_command_response(hex_response_1)
# print(decoded_response_1)
