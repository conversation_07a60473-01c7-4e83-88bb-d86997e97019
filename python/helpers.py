import datetime
import pytz
import decimal
import struct
from math import radians, cos, sin, asin, sqrt


def time_stamper():
	current_server_time = datetime.datetime.now()
	server_time_stamp = current_server_time.strftime('%H:%M:%S %d-%m-%Y')
	return server_time_stamp

def device_time_stamper(timestamp):
    # Convert the hex timestamp to milliseconds and then to seconds
    timestamp_ms = int(timestamp, 16) / 1000

    # Use timezone-aware datetime for UTC
    timestamp_utc = datetime.datetime.fromtimestamp(timestamp_ms, datetime.timezone.utc)

    # Define the timezone for Italy
    italy_timezone = pytz.timezone('Europe/Rome')

    # Convert UTC to Italy timezone
    timestamp_italy = timestamp_utc.astimezone(italy_timezone)

    # Format both timestamps
    formatted_timestamp_italy = timestamp_italy.strftime("%d/%m/%Y %H:%M:%S")  # Italian format

    # Combine both formatted timestamps
    formatted_timestamp = f"{formatted_timestamp_italy}"

    return formatted_timestamp

def time_stamper_for_json():
    # Define the timezone for Italy
    italy_timezone = pytz.timezone('Europe/Rome')

    # Get the current time in Italy's timezone
    current_italy_time = datetime.datetime.now(italy_timezone)

    # Format the time in the desired Italian format
    italy_time_stamp = current_italy_time.strftime('%d/%m/%Y %H:%M:%S')

    return italy_time_stamp


def record_delay_counter(timestamp):
	timestamp_ms = int(timestamp, 16) / 1000
	current_server_time = datetime.datetime.now().timestamp()
	return f"{int(current_server_time - timestamp_ms)} seconds"

####################################################
###############_PARSE_FUNCTIONS_CODE_###############
####################################################

def parse_data_integer(data):
	return int(data, 16)

def int_multiply_01(data):
	return float(decimal.Decimal(int(data, 16)) * decimal.Decimal('0.1'))

def int_multiply_001(data):
	return float(decimal.Decimal(int(data, 16)) * decimal.Decimal('0.01'))

def int_multiply_0001(data):
	return float(decimal.Decimal(int(data, 16)) * decimal.Decimal('0.001'))

def signed_no_multiply(data): #need more testing of this function
	try:
		binary = bytes.fromhex(data.zfill(8))
		value = struct.unpack(">i", binary)[0]
		return value
	except Exception as e:
		print(f"unexpected value received in function '{data}' error: '{e}' will leave unparsed value!")
		return f"0x{data}"

def parse_ibutton(data):
    """
    Special parser for iButton ID (parameter 78)
    Returns the iButton ID as a hex string without '0x' prefix
    """
    try:
        # Return the raw hex string without '0x' prefix
        return data.zfill(16)  # Ensure 16 characters (8 bytes)
    except Exception as e:
        print(f"Error parsing iButton ID: {e}")
        return f"0x{data}"

parse_functions_dictionary = { #this must simply be updated with new AVL IDs and their functions

	240: parse_data_integer,
	239: parse_data_integer,
	80: parse_data_integer,
	21: parse_data_integer,
	200: parse_data_integer,
	69: parse_data_integer,
	181: int_multiply_01,
	182: int_multiply_01,
	66: int_multiply_0001,
	24: parse_data_integer,
	205: parse_data_integer,
	206: parse_data_integer,
	67: int_multiply_0001,
	68: int_multiply_0001,
	241: parse_data_integer,
	299: parse_data_integer,
	16: parse_data_integer,
	1: parse_data_integer,
	9: parse_data_integer,
	179: parse_data_integer,
	12: int_multiply_0001,
	13: int_multiply_001,
	17: signed_no_multiply,
	18: signed_no_multiply,
	19: signed_no_multiply,
	11: parse_data_integer,
	10: parse_data_integer,
	2: parse_data_integer,
	3: parse_data_integer,
	6: int_multiply_0001,
	180: parse_data_integer,
	113: parse_data_integer,
	48: parse_data_integer,
	246: parse_data_integer,
	247: parse_data_integer,
	252: parse_data_integer,
	318: parse_data_integer,
	255: parse_data_integer,
	249: parse_data_integer,
	199: parse_data_integer,
	250: parse_data_integer,
	89: parse_data_integer,
	84: int_multiply_01,
	37: parse_data_integer,
	34: int_multiply_01,
	87: parse_data_integer,
	135: parse_data_integer,
	110: int_multiply_01,
	60: int_multiply_001,
	33: int_multiply_01,
	216: parse_data_integer,
	83: int_multiply_01,
    192: parse_data_integer,
    193: parse_data_integer,
    191: parse_data_integer,
    183: signed_no_multiply,
    1148: signed_no_multiply,
    78: parse_ibutton,
    86: parse_data_integer,
    201: parse_data_integer,
    248: signed_no_multiply,

    # geofence events
    155: signed_no_multiply,
    156: signed_no_multiply,
    157: signed_no_multiply,
    158: signed_no_multiply,
    159: signed_no_multiply,
}

def sorting_hat(key, value):
	if key in parse_functions_dictionary:
		parse_function = parse_functions_dictionary[key]
		return parse_function(value)
	else:
		return f"0x{value}"

# Convert degrees to radians
def haversine_distance(lat1, lon1, lat2, lon2):
    R = 6371000  # Radius of Earth in meters
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat / 2)**2 + cos(lat1) * cos(lat2) * sin(dlon / 2)**2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))

    return R * c

# Get current timestamp in Italy Timezone
def get_italy_time():
    italy_tz = pytz.timezone("Europe/Rome")
    return datetime.datetime.now(italy_tz).strftime("%Y-%m-%d %H:%M:%S")


####################################################
###############_Coordinates_Function_###############
####################################################

def coordinate_formater(hex_coordinate): #may return too large longitude need to test this more
	coordinate = int(hex_coordinate, 16)
	dec_coordinate = coordinate / 10000000
	if coordinate & (1 << 31):
		dec_coordinate *= -1
	else:
		pass
	return dec_coordinate