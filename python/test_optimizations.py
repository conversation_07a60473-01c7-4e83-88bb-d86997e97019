#!/usr/bin/env python3
"""
Test script to verify the optimizations in the GPS parser.
This script simulates multiple device connections to test performance.
"""

import socket
import time
import threading
import random
from concurrent.futures import ThreadPoolExecutor

# Test configuration
TEST_HOST = '127.0.0.1'
TEST_PORT = 2020
NUM_DEVICES = 10  # Simulate 10 devices
MESSAGES_PER_DEVICE = 5
MESSAGE_INTERVAL = 0.1  # 100ms between messages

# Sample IMEI and GPS data
SAMPLE_IMEI = "352592573030525"
SAMPLE_GPS_PACKET = "0000000000000350080d00000192643342b000057dff171b028a12005d00f80e0015000c05ef01f0011503c800450105b5000ab6000742352a43101a44000002f1000056c210003525c2000000019264336da800057dd9a81b027f53005d00ed0d0014000c05ef01f0011503c800450105b5000cb600074234df43101b44000002f1000056c2100035261300000001926433719000057dd7b31b027f53005d00fc0d000f000c05ef01f0011503c800450105b5000cb600074234cf43101b44000002f1000056c2100035261700000001926433b01000057dbdba1b027a710062010a0e000c000c05ef01f0011503c800450105b5000cb6000742350143101b44000002f1000056c2100035264e00000001926433bbc800057db8411b027a61006501140d000e000c05ef01f0011503c800450105b5000ab600074234d543101b44000002f1000056c2100035265900000001926433bfb000057db61c1b027b07006501240d0010000c05ef01f0011503c800450105b5000ab600074234d643101b44000002f1000056c2100035265e00000001926433c78000057db2341b027d4f0067013c0d0012000c05ef01f0011503c800450105b5000cb600074234e243101b44000002f1000056c2100035266800000001926433cb6800057db00d1b02801c0067014b0d0016000c05ef01f0011503c800450105b5000ab600074234e743101b44000002f1000056c2100035267100000001926433d72000057dac151b028756006901550c001a000c05ef01f0011503c800450105b5000eb600084234e743101b44000002f1000056c2100035268700000001926434060000057d9d801b02a516006e01610d0017000c05ef01f0011503c800450105b5000ab600084234e943101b44000002f1000056c210003526e10000000192643409e800057d9e8a1b02a7a0006d00090d0018000c05ef01f0011502c800450105b5000db6000842336543101b44000002f1000056c210003526e8000000019264340dd000057da02b1b02aa3b006d00170d001a000c05ef01f0011502c800450105b5000db600084233af43101b44000002f1000056c210003526f10000000192643415a000057da53f1b02ae87006c002a0d001b000c05ef01f0011502c800450105b5000ab600084234a443101844000002f1000056c21000352701000d0000849e"

def create_test_imei(device_id):
    """Create a unique IMEI for testing."""
    return f"86001234567890{device_id:02d}"

def create_test_gps_packet():
    """Create a test GPS packet with random coordinates."""
    # This is a simplified version - in real testing you'd want proper codec 8E packets
    base_packet = "0000000000000350080d00000192643342b000057dff171b028a12005d00f80e0015000c05ef01f0011503c800450105b5000ab6000742352a43101a44000002f1000056c210003525c2000000019264336da800057dd9a81b027f53005d00ed0d0014000c05ef01f0011503c800450105b5000cb600074234df43101b44000002f1000056c2100035261300000001926433719000057dd7b31b027f53005d00fc0d000f000c05ef01f0011503c800450105b5000cb600074234cf43101b44000002f1000056c2100035261700000001926433b01000057dbdba1b027a710062010a0e000c000c05ef01f0011503c800450105b5000cb6000742350143101b44000002f1000056c2100035264e00000001926433bbc800057db8411b027a61006501140d000e000c05ef01f0011503c800450105b5000ab600074234d543101b44000002f1000056c2100035265900000001926433bfb000057db61c1b027b07006501240d0010000c05ef01f0011503c800450105b5000ab600074234d643101b44000002f1000056c2100035265e00000001926433c78000057db2341b027d4f0067013c0d0012000c05ef01f0011503c800450105b5000cb600074234e243101b44000002f1000056c2100035266800000001926433cb6800057db00d1b02801c0067014b0d0016000c05ef01f0011503c800450105b5000ab600074234e743101b44000002f1000056c2100035267100000001926433d72000057dac151b028756006901550c001a000c05ef01f0011503c800450105b5000eb600084234e743101b44000002f1000056c2100035268700000001926434060000057d9d801b02a516006e01610d0017000c05ef01f0011503c800450105b5000ab600084234e943101b44000002f1000056c210003526e10000000192643409e800057d9e8a1b02a7a0006d00090d0018000c05ef01f0011502c800450105b5000db6000842336543101b44000002f1000056c210003526e8000000019264340dd000057da02b1b02aa3b006d00170d001a000c05ef01f0011502c800450105b5000db600084233af43101b44000002f1000056c210003526f10000000192643415a000057da53f1b02ae87006c002a0d001b000c05ef01f0011502c800450105b5000ab600084234a443101844000002f1000056c21000352701000d0000849e"
    return base_packet

def test_device_connection(device_id):
    """Test a single device connection."""
    imei = create_test_imei(device_id)
    results = {
        'device_id': device_id,
        'imei': imei,
        'messages_sent': 0,
        'acks_received': 0,
        'avg_response_time': 0,
        'errors': 0,
        'response_times': []
    }
    
    try:
        # Connect to the server
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)  # 10 second timeout
        sock.connect((TEST_HOST, TEST_PORT))
        
        # Send IMEI
        imei_packet = f"000F{imei.encode().hex()}"
        sock.sendall(bytes.fromhex(imei_packet))
        
        # Wait for IMEI acknowledgment
        ack = sock.recv(1)
        if ack != b'\x01':
            print(f"Device {device_id}: IMEI not acknowledged")
            results['errors'] += 1
            return results
            
        print(f"Device {device_id}: IMEI acknowledged")
        
        # Send GPS messages
        for msg_num in range(MESSAGES_PER_DEVICE):
            try:
                start_time = time.time()
                
                # Send GPS packet
                gps_packet = create_test_gps_packet()
                sock.sendall(bytes.fromhex(gps_packet))
                results['messages_sent'] += 1
                
                # Wait for acknowledgment
                ack_data = sock.recv(4)
                end_time = time.time()
                
                if len(ack_data) == 4:
                    results['acks_received'] += 1
                    response_time = (end_time - start_time) * 1000  # Convert to milliseconds
                    results['response_times'].append(response_time)
                    print(f"Device {device_id}: Message {msg_num+1} ACK in {response_time:.2f}ms")
                else:
                    print(f"Device {device_id}: Invalid ACK for message {msg_num+1}")
                    results['errors'] += 1
                
                # Wait before next message
                time.sleep(MESSAGE_INTERVAL)
                
            except Exception as e:
                print(f"Device {device_id}: Error sending message {msg_num+1}: {e}")
                results['errors'] += 1
        
        sock.close()
        
    except Exception as e:
        print(f"Device {device_id}: Connection error: {e}")
        results['errors'] += 1
    
    # Calculate average response time
    if results['response_times']:
        results['avg_response_time'] = sum(results['response_times']) / len(results['response_times'])
    
    return results

def run_performance_test():
    """Run the performance test with multiple devices."""
    print(f"Starting performance test with {NUM_DEVICES} devices...")
    print(f"Each device will send {MESSAGES_PER_DEVICE} messages")
    print(f"Target server: {TEST_HOST}:{TEST_PORT}")
    print("-" * 50)
    
    start_time = time.time()
    
    # Use ThreadPoolExecutor to simulate concurrent devices
    with ThreadPoolExecutor(max_workers=NUM_DEVICES) as executor:
        futures = [executor.submit(test_device_connection, i) for i in range(NUM_DEVICES)]
        results = [future.result() for future in futures]
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Analyze results
    print("\n" + "=" * 50)
    print("PERFORMANCE TEST RESULTS")
    print("=" * 50)
    
    total_messages = sum(r['messages_sent'] for r in results)
    total_acks = sum(r['acks_received'] for r in results)
    total_errors = sum(r['errors'] for r in results)
    all_response_times = []
    
    for result in results:
        all_response_times.extend(result['response_times'])
    
    print(f"Total test time: {total_time:.2f} seconds")
    print(f"Total messages sent: {total_messages}")
    print(f"Total ACKs received: {total_acks}")
    print(f"Total errors: {total_errors}")
    print(f"Success rate: {(total_acks/total_messages)*100:.1f}%" if total_messages > 0 else "N/A")
    
    if all_response_times:
        avg_response = sum(all_response_times) / len(all_response_times)
        min_response = min(all_response_times)
        max_response = max(all_response_times)
        
        print(f"\nResponse Time Analysis:")
        print(f"Average response time: {avg_response:.2f}ms")
        print(f"Minimum response time: {min_response:.2f}ms")
        print(f"Maximum response time: {max_response:.2f}ms")
        
        # Performance benchmarks
        print(f"\nPerformance Assessment:")
        if avg_response < 50:
            print("✅ EXCELLENT: Average response time < 50ms")
        elif avg_response < 100:
            print("✅ GOOD: Average response time < 100ms")
        elif avg_response < 200:
            print("⚠️  ACCEPTABLE: Average response time < 200ms")
        else:
            print("❌ POOR: Average response time > 200ms")
    
    print("\nPer-device breakdown:")
    for result in results:
        print(f"Device {result['device_id']:2d}: {result['acks_received']}/{result['messages_sent']} ACKs, "
              f"Avg: {result['avg_response_time']:.1f}ms, Errors: {result['errors']}")

if __name__ == "__main__":
    print("GPS Parser Performance Test")
    print("Make sure the GPS parser server is running on port 2020")
    input("Press Enter to start the test...")
    
    try:
        run_performance_test()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Test failed: {e}")
