<?php

use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\DashboardController;
use App\Http\Controllers\API\VehicleController;
use App\Http\Controllers\API\RouteTrackingController;
use App\Http\Controllers\DeviceController;
use App\Http\Middleware\SetUserLocale;
use Illuminate\Support\Facades\Route;

Route::middleware(SetUserLocale::class)->group(function () {
    Route::controller(AuthController::class)->group(function () {
        Route::post('login', 'login');
    });

    Route::middleware(['auth:sanctum'])->group(function () {
        Route::controller(AuthController::class)->group(function () {
            Route::get('profile', 'getProfile');
            Route::get('logout', 'logout');
            Route::post('update-fcm-token', 'updateFcmToken');
            Route::post('update-language', 'updateLanguage');
            Route::get('notifications', 'getNotifications');
        });

        Route::controller(DashboardController::class)->group(function () {
            Route::get('dashboard/stats', 'getStats');
        });

        Route::controller(VehicleController::class)->group(function () {
            Route::get('vehicles', 'index');
            Route::get('vehicles/{id}', 'show');
            Route::get('vehicles/{vehicleId}/events', 'getEvents');
        });
    });



    Route::controller(RouteTrackingController::class)->group(function () {
        Route::post('update-route', 'updateRoute');
        Route::post('complete-route', 'completeRoute');
    });

    Route::controller(DeviceController::class)->group(function () {
        Route::post('log-device-event', 'logDeviceEvent');
        Route::post('log-geofence-event', 'logDeviceGeofenceEvent');
        Route::post('save-command-response', 'saveCommandResponse');
    });
});
