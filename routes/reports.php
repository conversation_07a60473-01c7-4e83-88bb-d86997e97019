<?php

use App\Http\Controllers\ReportDownloadController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Report Routes
|--------------------------------------------------------------------------
|
| Routes for handling report downloads and previews
|
*/

Route::middleware(['auth', 'verified'])->group(function () {
    // Report download and preview routes
    Route::get('/reports/{report}/download', [ReportDownloadController::class, 'download'])
        ->name('reports.download');
    
    Route::get('/reports/{report}/preview', [ReportDownloadController::class, 'preview'])
        ->name('reports.preview');
});
