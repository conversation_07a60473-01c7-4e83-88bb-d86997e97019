<?php

return [
    'dashboard' => 'Dashboard',

    'notifications' => 'Notifications',
    'no_notifications_yet' => 'No notifications yet',
    'platform_login' => 'Platform Login',
    'enter_credentials_to_login' => 'Enter your credentials to login',
    'email' => 'Email',
    'enter_your_email' => 'Enter your email',
    'password' => 'Password',
    'forgot_password' => 'Forgot Password?',
    'remember_me' => 'Remember me',
    'login' => 'Login',

    'monitor_manage_optimize_fleet' => 'Monitor, manage, and optimize your fleet in real time.',
    'total_fleet_size' => 'Total Fleet size',
    'vehicles_in_motion' => 'Vehicles in Motion',
    'vehicles_stopped' => 'Vehicles Stopped',
    'license_plate' => 'License Plate',
    'driver_name' => 'Driver Name',
    'status' => 'Status',
    'speed' => 'Speed',
    'notifications' => 'Notifications',
    'moving' => 'Moving',
    'stopped' => 'Stopped',
    'user_management' => 'User Management',
    'vehicle_management' => 'Vehicle Management',
    'fleet_management' => 'Fleet Management',
    'remote_control' => 'Remote Control',
    'geofencing' => 'Geofencing',
    'reporting' => 'Reporting',
    'logout' => 'Logout',
    'admin' => 'Admin',
    'settings' => 'Settings',
    'sign_out' => 'Sign out',
    'add_edit_assign_roles' => 'Add, edit, and assign roles to users for seamless fleet operations.',
    'search' => 'Search',
    'add_user' => 'Add User',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'name' => 'Name',
    'role' => 'Role',
    'added_at' => 'Added At',
    'actions' => 'Actions',
    'user_details' => 'User details',
    'created_at' => 'Created At',
    'edit_user' => 'Edit User',
    'manager' => 'Manager',
    'confirm_delete_record' => 'Are you sure you want to delete this record?',
    'delete_warning' => 'Once deleted, this action cannot be undone.',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'save' => 'Save',
    'maintain_track_records' => 'Maintain and track detailed records of your fleet vehicles.',
    'model' => 'Model',
    'type' => 'Type',
    'icon' => 'Icon',
    'add_vehicle' => 'Add Vehicle',
    'year_of_registration' => 'Year of registration',
    'car' => 'Car',
    'truck' => 'Truck',
    'motorcycle' => 'Motorcycle',
    'bus' => 'Bus',
    'van' => 'Van',
    'suv' => 'SUV',
    'other' => 'Other',
    'vehicles_list' => 'Vehicles List',
    'all' => 'All',
    'stopped' => 'Stopped',
    'parked' => 'Parked',
    'trucks' => 'Trucks',
    'vehicles' => 'Vehicles',
    'unlocked' => 'Unlocked',
    'pin_entry' => 'PIN Entry',
    'vehicle_details' => 'Vehicle Details',
    'enter_pin' => 'Enter PIN',
    'change_pin' => 'Change Pin',
    'enable_local_lock' => 'Enable local lock control via i-Button.',
    'save_changes' => 'Save Changes',
    'existing_geofences' => 'Existing Geofences',
    'generate_export_reports' => 'Generate and export detailed vehicle activity reports with customizable filters.',
    'select_vehicles' => 'Select Vehicles',
    'select' => 'Select',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'departure_time' => 'Departure Time',
    'address' => 'Address',
    'distance_traveled' => 'Distance Traveled',
    'duration' => 'Duration',
    'stop_time' => 'Stop Time',
    'geofence_information' => 'Geofence Information',
    'view' => 'View',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'pdf_export' => 'PDF Export',
    'excel_export' => 'Excel Export',
    'no_user_found' => 'No user found.',
    'no_vehicle_found' => 'No vehicle found.',
    'edit_vehicle' => 'Edit Vehicle',
    'updated_at' => 'Updated At',
    'lock_status' => 'Lock Status',
    'vehicle_list' => 'Vehicle List',


    'manage_settings' => 'Manage preferences, security settings, and server configurations for a personalized experience.',
    'user' => 'User',
    'permissions' => 'Permissions',
    'zones' => 'Zones',
    'imei' => 'IMEI',
    'device_imei' => 'Device IMEI',
    'driver_management' => 'Driver Management',
    'manage_drivers' => 'Manage Drivers',
    'add_driver' => 'Add Driver',
    'no_driver_found' => 'No driver found.',
    'edit_driver' => 'Edit Driver',
    'phone_number' => 'Phone Number',
    'driver' => 'Driver',
    'select_driver' => 'Select a driver',
    'lock_unlock' => 'Lock/Unlock',
    'submit' => 'Submit',
    'add_geofence' => 'Add Geofence',
    'edit_geofence' => 'Edit Geofence',
    'location' => 'Location',
    'notification_settings' => 'Notification Settings',
    'push_notifications' => 'Push Notifications',
    'email_notifications' => 'Email Notifications',
    'confirm' => 'Confirm',
    'add_new' => 'Add New',
    'zones' => 'Zones',


    'reset_password' => 'Reset Password',
    'enter_email_to_reset_password' => 'Enter your email to reset your password',
    'enter_otp_to_reset_password' => 'Enter the OTP sent to your email to reset your password',
    'resend_otp' => 'Resend OTP',
    'enter_otp' => 'Enter OTP',
    'enter_new_password' => 'Enter your new password',
    'password_confirmation' => 'Password Confirmation',
    'forgot_password_email' => 'Forgot Password Email',
    'forgot_password_otp_sent' => 'Forgot Password OTP Sent',
    'incorrect_email' => 'Incorrect email',
    'otp_verified_success' => 'OTP verified successfully',
    'otp_invalid_expired' => 'OTP is invalid or expired',
    'otp_resent_success' => 'OTP resent successfully',
    'password_update_success' => 'Password updated successfully',


    'forgot_password_request' => 'We received a forgot password request for email :email. To reset your password, please use the following OTP:',
    'forgot_password_message' => 'We understand that sometimes things slip your mind. Don\'t worry, we\'ve got you covered. You\'re just one step away from regaining access to your account.',

    'contact_support' => 'For any issues, feel free to contact our support team at',
    'best_regards' => 'Best regards,',
    'all_rights_reserved' => 'All rights reserved',


    'total_fuel_used' => 'Total fuel used',
    'average_consumption' => 'Average consumption',
    'available_drivers' => 'Available drivers',
    'vehicles_in_workshop' => 'Vehicles in workshop',


    'bike' => 'Bike',
    'cycle' => 'Cycle',
    'scooter' => 'Scooter',
    'tractor' => 'Tractor',
    'ambulance' => 'Ambulance',
    'boat' => 'Boat',
    'mileage' => 'Mileage',
    'enter' => 'Enter',
    'employment_date' => 'Employment Date',
    'license_number' => 'License Number',
    'ibutton_code' => 'Ibutton Code',
    'plant_type' => 'Plant Type',
    'calendar' => 'Calendar',

    'departure_time' => 'Departure Time',
    'arrival_time' => 'Arrival Time',
    'distance_traveled' => 'Distance Traveled',
    'trip_duration' => 'Trip Duration',
    'current_status' => 'Current Status',
    'speed' => 'Speed',
    'geofence_details' => 'Geofence Details',
    'fuel_consumption' => 'Fuel Consumption',
    'trip_timeline' => 'Trip Timeline',
    'ignition_on' => 'Ignition On',
    'vehicle_events' => 'Vehicle Events',
    'no_vehicle_events_found' => 'No Vehicle Events Found',
    'add_event' => 'Add Event',
    'edit_event' => 'Edit Event',

    'event_type' => 'Event Type',
    'event_description' => 'Event Description',
    'description' => 'Description',
    'start_time' => 'Start Time',
    'end_time' => 'End Time',
    'other_event_type' => 'Other Event Type',
    'under_maintenance' => 'Under Maintenance',
    'maintenance_status' => 'Maintenance Status',
    'has_alert' => 'Has Alert',
    'select_users' => 'Select Users',
    'no_search_found' => 'No search results found',
    'search_users' => 'Search Users',
    'pending' => 'Pending',
    'in_progress' => 'In Progress',
    'completed' => 'Completed',
    'service' => 'Service',
    'tire_change' => 'Tire Change',
    'insurance_expiry' => 'Insurance Expiry',
    'registration_expiry' => 'Registration Expiry',
    'inspection' => 'Inspection',
    'fuel_refill' => 'Fuel Refill',
    'cleaning_washing' => 'Cleaning/Washing',
    'battery_check' => 'Battery Check',
    'brake_check' => 'Brake Check',
    'other_maintenance' => 'Other Maintenance',
    'under_maintenance' => 'Under Maintenance',
    'documentation_renewal' => 'Documentation Renewal',
    'license_expiry' => 'License Expiry',
    'tax_payment' => 'Tax Payment',
    'policy_update' => 'Policy Update',
    'deployment' => 'Deployment',
    'reserved' => 'Reserved',
    'decommissioned' => 'Decommissioned',


    // TODO: pending translation
    'vehicle_users' => 'Vehicle Users',
    'vehicle' => 'Vehicle',
    'assigned_at' => 'Assigned At',
    'add_vehicle_user' => 'Add Vehicle User',
    'attachments' => 'Attachments',
    'documents_attached' => ':documents has been attached',
    'added_attachments' => 'Added Attachments',
    'starting_point' => 'Starting Point',
    'arrival_point' => 'Arrival Point',
    'travel_distance' => 'Travel Distance',
    'report_type' => 'Report Type',
    'fuel' => 'Fuel',
    'routes' => 'Routes',
    'departure' => 'Departure',
    'destination' => 'Destination',
    'play' => 'Play',
    'pause' => 'Pause',
    'anomaly' => 'Anomaly',

    'vehicles_with_anomalous' => 'Vehicles with anomalous fuel consumption',

    'driver_routes' => 'Driver Routes',

    'vehicle_under_maintenance' => 'Vehicle is under maintenance',
    'vehicle_maintenance_in_progress' => 'Vehicle maintenance is in progress',

    'add_route' => 'Add Route',
    'edit_route' => 'Edit Route',
    'ongoing' => 'Ongoing',
    'start_point' => 'Start Point',
    'end_point' => 'End Point',
    'stops' => 'Stops',
    'distance' => 'Distance',
    'remarks' => 'Remarks',
    'date' => 'Date',
    'cancelled' => 'Cancelled',
    'repeat_daily' => 'Repeat Daily',
    'enter_remarks' => 'Enter Remarks',
    'add_stop' => 'Add Stop',
    'remove' => 'Remove',
    'enter_stop' => 'Enter Stop',
    'no_record_found' => 'No Record Found',
    'ending_point' => 'Ending Point',
    'yes' => 'Yes',
    'no' => 'No',


    'day(s)' => 'day(s)',
    'hour(s)' => 'hour(s)',
    'minute(s)' => 'minute(s)',
    'liters' => 'Liters',
    'history_mode' => 'History Mode',
    'live_mode' => 'Live Mode',

    'search_for_a_location' => 'Search for a location',
    'vehicle_routes' => 'Vehicle Routes',
    'asigned_vehicls' => 'Assigned vehicles',
    'search_vehicle' => 'Search Vehicle',
    'user_vehicles' => 'User Vehicles',


    'alarms' => 'Alarms',
    'alarm' => 'Alarm',
    'select_vehicle' => 'Search a Vehicle',
    'select_geofence' => 'Search a Geofence',
    'geofence_exit' => 'Geofence Exit',
    'geofence_in' => 'Geofence In',
    'geofence_out' => 'Geofence Out',
    'towing' => 'Towing',
    'signal_lost' => 'Signal lost',
    'alarm_type' => 'Alarm Type',
    'occurred_at' => 'Occurred At',
    'show_details' => 'Show Details',
    'alarms_details' => 'Monitor real-time alerts for vehicle activities, ensuring safety and compliance with instant notifications on geofence exits, speed alerts, and more.',


    // Alarm Types
    'jamming'      => 'Jamming Detected',
    'towing'       => 'Towing Detected',
    'crash'        => 'Crash Detected',
    'signal_lost'  => 'Signal Lost',
    'unplug'       => 'Device Unplugged',

    // Jamming
    'jamming_1'    => 'Jamming Started',
    'jamming_0'    => 'Jamming Stopped',

    // Towing
    'towing_1'     => 'Towing Detected',
    'towing_0'     => 'Vehicle Stopped',

    // Crash
    'crash_1'      => 'Real crash detected (calibrated device)',
    'crash_2'      => 'Limited crash trace (uncalibrated device)',
    'crash_3'      => 'Limited crash trace (calibrated device)',
    'crash_4'      => 'Full crash trace (uncalibrated device)',
    'crash_5'      => 'Full crash trace (calibrated device)',
    'crash_6'      => 'Real crash detected (uncalibrated device)',

    // Signal Lost (GNSS Jamming)
    'signal_lost_0' => 'GPS signal is stable, no interference detected.',
    'signal_lost_1' => 'Interference detected: GPS accuracy may be compromised.',
    'signal_lost_2' => 'GPS signal lost due to strong interference.',

    // Unplug
    'unplug_1'     => 'Battery Disconnected',
    'unplug_0'     => 'Battery Connected',

    'unknown_event' => 'Unknown Event',

    'geofence_exit_event' => 'Exited geofence :geofence',
    'geofence_entry_event' => 'Entered geofence :geofence',



    // Jamming Events
    "jamming_started" => "Jamming started for vehicle :vehicle",
    "jamming_ended" => "Jamming ended for vehicle :vehicle",
    "jamming_started_message" => "A jamming attempt has been detected on vehicle :vehicle at :location.",
    "jamming_ended_message" => "Jamming interference has ended for vehicle :vehicle at :location.",

    // Crash Detection Events
    "crash_detected" => "Crash detected for vehicle :vehicle",
    "crash_detected_calibrated" => "A calibrated crash has been detected for vehicle :vehicle at :location.",
    "limited_crash_unCalibrated" => "A limited uncalibrated crash has been detected for vehicle :vehicle at :location.",
    "limited_crash_calibrated" => "A limited calibrated crash has been detected for vehicle :vehicle at :location.",
    "full_crash_unCalibrated" => "A full uncalibrated crash has been detected for vehicle :vehicle at :location.",
    "full_crash_calibrated" => "A full calibrated crash has been detected for vehicle :vehicle at :location.",
    "crash_detected_unCalibrated" => "A crash has been detected for vehicle :vehicle at :location, but it is uncalibrated.",
    "crash_detected_generic" => "A crash event has been recorded for vehicle :vehicle at :location.",

    // Towing Events
    "towing_alarm" => "Towing alarm for vehicle :vehicle",
    "towing_detected" => "Towing has been detected for vehicle :vehicle at :location.",
    "vehicle_stopped" => "Vehicle :vehicle has stopped moving at :location.",

    // Unplug Events
    "battery_status" => "Battery status changed for vehicle :vehicle",
    "battery_disconnected" => "Battery has been disconnected from vehicle :vehicle at :location.",
    "battery_present" => "Battery is present in vehicle :vehicle at :location.",

    // GNSS Jamming Events
    "gnss_interference_alarm" => "GNSS interference detected for vehicle :vehicle",
    "gnss_signal_stable" => "GNSS signal is stable for vehicle :vehicle at :location.",
    "gnss_interference_detected" => "GNSS interference detected for vehicle :vehicle at :location.",
    "gnss_signal_lost" => "GNSS signal lost for vehicle :vehicle at :location.",
    "gnss_interference_unknown" => "Unknown GNSS interference detected for vehicle :vehicle at :location.",

    // Geofence Events
    "geofence_entry" => "Vehicle :vehicle entered geofence :geofence",
    "geofence_exit" => "Vehicle :vehicle exited geofence :geofence",
    "geofence_entry_message" => "Vehicle :vehicle has entered the geofence area :geofence at :location.",
    "geofence_exit_message" => "Vehicle :vehicle has left the geofence area :geofence at :location.",

    "roles_management" => "Roles Management",
    "add_edit_assign_roles" => "Add/Edit Roles & assign Permissions",
    "add_role" => "Add Role",
    "edit_role" => "Edit Role",
    "manage_roles" => "Manage Roles",


    'dashboard_view' => 'View Dashboard',
    'user_view' => 'View Users',
    'user_add' => 'Add User',
    'user_edit' => 'Edit User',
    'user_delete' => 'Delete User',
    'user_vehicle_assignment' => 'Assign Vehicle to User',

    'vehicle_view' => 'View Vehicles',
    'vehicle_add' => 'Add Vehicle',
    'vehicle_edit' => 'Edit Vehicle',
    'vehicle_delete' => 'Delete Vehicle',
    'vehicle_events_management' => 'Manage Vehicle Events',
    'vehicle_route_management' => 'Manage Vehicle Routes',
    'vehicle_users' => 'Manage Vehicle Users',

    'driver_view' => 'View Drivers',
    'driver_add' => 'Add Driver',
    'driver_edit' => 'Edit Driver',
    'driver_delete' => 'Delete Driver',
    'all_drivers_access' => 'Access all Drivers',

    'fleet_view' => 'View Fleet',
    'fleet_view_history_mode' => 'View Fleet in History Mode',

    'remote_control' => 'Remote Control Access',
    'remote_control_pin_manage' => 'Manage Remote Control PIN',

    'geofence_view' => 'View Geofences',
    'geofence_add' => 'Add Geofence',
    'geofence_edit' => 'Edit Geofence',
    'geofence_delete' => 'Delete Geofence',
    'geofence_vehicle_assignment' => 'Assign Vehicle to Geofence',

    'reporting_view' => 'View Reports',
    'reporting_export' => 'Export Reports',

    'notifications_view' => 'View Notifications',

    'roles_management' => 'Manage Roles & Permissions',
    'all_vehicles_access' => 'All Vehicle Access',
    'engine_control'    => 'Engine Control',
    'current_odometer_reading' => 'Current Odometer Reading',
    'fuel_level' => 'Fuel Level',

    'fuel_level_good' => 'Fuel level is good',
    'fuel_level_warning' => 'Fuel level is getting low',
    'fuel_level_critical' => 'Fuel level is critical!',

    'route_fuel_consumption' => 'Route Fuel Consumption',
    'total_fuel_consumption' => 'Total Fuel Consumption',
    'daily_distance' => 'Daily Distance',
    'today_travel_distance' => 'Today Travel Distance',
    'total_distance_traveled' => 'Total Distance Traveled',
    'total_odometer' => 'Total Odometer',
    'today_stops' => 'Today\'s Stops',
    'total_time' => 'Total Time',
    'total_stops' => 'Total Stops',

    'final_odometer' => 'Final Odometer',
    'final_fuel' => 'Final Fuel',
    'final_distance' => 'Final Distance',
    'final_time' => 'Final Time',
    'final_stops' => 'Final Stops',
    'total_distance' => 'Total Distance',
    'total_route_statistics' => 'Total Route Statistics',
    'distance_from_previous' => 'Distance from Previous',
    'completed_at' => 'Completed At',
    'current_fuel' => 'Current Fuel',
    'current_distance' => 'Current Distance',
    'initial_odometer' => 'Initial Odometer',
    'initial_fuel' => 'Initial Fuel',
    'current_odometer' => 'Current Odometer',
    'stop_point' => 'Stop Point',

    'end' => 'End',
    'trip_started' => 'Trip Started',
    'trip_ended' => 'Trip Ended',
    'exported_at' => 'Exported At',
    'report' => 'Report',
    'generate_reports' => 'Generate Reports',

    'data_summary' => 'Data Summary',
    'total_duration' => 'Total Duration',
    'total_distance' => 'Total Distance',
    'fuel_consumption' => 'Fuel Consumption',
    'stop_timeline' => 'Stop Timeline',
    'duration' => 'Duration',
    'odometer' => 'Odometer',
    'period' => 'Period',
    'long_stop' => 'Long Stop',
    'short_stop' => 'Short Stop',
    'point_info' => 'Point Info',
    'time' => 'Time',

    'travel_duration' => 'Travel Duration',
    'stop_duration' => 'Stop Duration',
    'trip_distance' => 'Trip Distance',
    'trip_fuel_consumption' => 'Trip Fuel Consumption',

    'geofence_events' => 'Geofence Events',
    'geofence' => 'Geofence',
    'departure_address' => 'Departure Address',
    'arrival_address' => 'Arrival Address',
    'fuel_report_summary' => 'Fuel Report Summary',
    'current_fuel_level' => 'Current Fuel Level',
    'no_fuel_data_available' => 'No Fuel Data Available',
    'fuel_used' => 'Fuel Used',
    'stop' => 'Stop',
    'total_trips' => 'Total Trips',

    'fuel_report' => 'Fuel Report',

    // Indicator translations
    'ignition_status' => 'Ignition Status',
    'movement_status' => 'Movement Status',
    'battery_status' => 'Battery Status',
    'signal_strength' => 'Signal Strength',
    'geofence_status' => 'Geofence Status',
    'on' => 'On',
    'off' => 'Off',
    'inside' => 'Inside',
    'outside' => 'Outside',

    'avg_consumption' => 'Average Consumption',
    'start_location' => 'Start Location',
    'end_location' => 'End Location',
    'trip_details' => 'Trip Details',
    'today_fuel_consumption' => 'Today Fuel Consumption',
    'initial_fuel_level' => 'Initial Fuel Level',
    'final_fuel_level' => 'Final Fuel Level',
    'vehicle_events_view' => 'View Vehicle Events',
    'all_vehicle_events_view' => 'View All Vehicle Events',
    'add_user_role' => 'Add User Role',
    'driver_assigned_to_another_vehicle' => 'Driver is already assigned to another vehicle',
    'initial_fuel_used' => 'Initial Fuel Used',
    'final_fuel_used' => 'Final Fuel Used',
    'timeline' => 'Timeline',
    'refueling_amount' => 'Refueling Amount',

    'trip' => 'Trip',

    'CAN_vehicles' => 'CAN Vehicles',

    'sync_device' => 'Sync Device',
    'zone' => 'Zone',
    'all_geofences_access' => 'Access all Geofences',
    'manage_vehicle_event_users' => 'Manage Vehicle Event Users',

    'period_type' => 'Period Type',
    'daily' => 'Daily',
    'monthly' => 'Monthly',
    'custom_range' => 'Custom Range',
    'reporting_type' => 'Reporting Type',
    'year' => 'Year',
    'month' => 'Month',
    'max_range_31_days' => 'Maximum range is 31 days',
    'selected_range' => 'Selected Range',
    'days' => 'days',
    'daily_breakdown' => 'Daily Breakdown',
    'trips' => 'Trips',
    'export_history' => 'Export History',
    'file_type' => 'File Type',
    'file_size' => 'File Size',
    'processing_time' => 'Processing Time',
    'download' => 'Download',
    'preview' => 'Preview',
    'delete' => 'Delete',
    'failed' => 'Failed',
    'processing' => 'Processing',
    'are_you_sure' => 'Are you sure you want to delete this export?',
    'no_export_history' => 'No export history',
    'total_days' => 'Total Days',
    'daily_fuel_breakdown' => 'Daily Fuel Breakdown',
    'total_trip_duration' => 'Total Trip Duration',
    'total_stop_duration' => 'Total Stop Duration',
    'average_daily_fuel' => 'Average Daily Fuel',
    'fuel_efficiency' => 'Fuel Efficiency',
    'export_reports_will_appear_here' => 'Exported reports will appear here.',
    'monthly_report_queued' => 'Monthly report has been queued for processing. You can track the progress in the export history below.',
    'check_export_history_for_progress' => 'Check the export history below to track the progress of your report.',
    'auto_refresh_5s' => 'Auto-refresh every 5 seconds',
    'export_deleted_successfully' => 'Export deleted successfully',
    'error_deleting_export' => 'Error deleting export',
    'alarm_report' => 'Alarm Report',
];
