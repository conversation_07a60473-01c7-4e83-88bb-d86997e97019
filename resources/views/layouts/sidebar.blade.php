<div x-data="{ sidebarIsOpen: false, isMinimized: true }" class="relative flex flex-col w-full md:flex-row">
    <!-- This allows screen readers to skip the sidebar and go directly to the main content. -->
    <a class="sr-only" href="#main-content">skip to the main content</a>

    <!-- dark overlay for when the sidebar is open on smaller screens  -->
    <div x-cloak x-show="sidebarIsOpen" class="fixed inset-0 z-20 bg-neutral-950/30 backdrop-blur-sm md:hidden"
        aria-hidden="true" x-on:click="sidebarIsOpen = false" x-transition.opacity></div>

    <nav x-cloak
        class="fixed left-0 z-30 flex flex-col p-4 transition-all duration-300 border-r h-svh shrink-0 border-neutral-300 bg-white/90 dark:bg-slate-900 dark:border-slate-600 md:translate-x-0 md:relative"
        :class="isMinimized ? 'md:w-20' : 'md:w-72'"
        x-bind:class="sidebarIsOpen ? 'translate-x-0 w-72' : '-translate-x-72'" aria-label="sidebar navigation"
        @click.outside="isMinimized = true">
        <div class="flex items-center justify-between">
            <!-- logo  -->
            <a x-bind:class="isMinimized ? 'md:hidden' : 'block'" href="{{ route('fleet-management') }}"
                class="ml-2 text-2xl font-bold transition-all duration-300 w-fit">
                <span class="sr-only">homepage</span>
                <img class="h-8 dark:hidden" src="{{ asset('assets/images/logo.svg') }}" alt="logo">
                <img class="hidden h-8 dark:block" src="{{ asset('assets/images/logo-light.png') }}" alt="logo">

            </a>

            <a x-bind:class="isMinimized ? 'md:block hidden' : 'hidden'" href="{{ route('fleet-management') }}"
                class="ml-2 text-2xl font-bold transition-all duration-300 w-fit">
                <span class="sr-only">homepage</span>

                <img class="h-8 dark:hidden" src="{{ asset('assets/images/logo-icon.png') }}" alt="controllone logo">

                <img class="hidden h-8 dark:block" src="{{ asset('assets/images/logo-white.png') }}" alt="logo">
            </a>

            <!-- Close Sidebar on Mobile -->
            <div class="lg:hidden">
                <button type="button"
                    class="absolute top-0 right-0 flex items-center justify-center w-10 h-10 hover:text-slate-600 dark:text-slate-500 dark:hover:text-slate-400"
                    x-on:click="sidebarIsOpen = false;">
                    <svg class="inline-block w-5 h-5 -mx-1 hi-solid hi-x" fill="currentColor" viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clip-rule="evenodd" />
                    </svg>
                </button>
            </div>

            <button @click="isMinimized = !isMinimized" x-bind:class="isMinimized ? 'rotate-0' : 'rotate-180'"
                class="absolute hidden transition-all duration-300 top-2 -right-4 lg:block">
                <img class="size-8" src="{{ asset('assets/images/icons/right-charvon.svg') }}" alt="charvon-right">
            </button>
        </div>



        <!-- sidebar links  -->
        <div class="flex flex-col h-full gap-2 mt-10 overflow-y-auto">

            @can('fleet_view')
                <a href="{{ route('fleet-management') }}"
                    class="{{ request()->routeIs('fleet-management') ? 'bg-primary text-white' : 'hover:bg-black/5 hover:text-neutral-900 text-neutral-600 dark:text-white/60 hover:dark:bg-white/5 hover:dark:text-white' }} flex items-center rounded-md gap-2 ps-3.5 px-3 py-2.5 text-sm font-medium underline-offset-2  focus-visible:underline outline-none transition-all duration-300  ">
                    <svg class="size-5" version="1.1" id="_x32_" xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512" xml:space="preserve"
                        fill="currentColor">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <style type="text/css">
                                .st0 {
                                    fill: currentColor;
                                }
                            </style>
                            <g>
                                <path class="st0"
                                    d="M174.468,203.704c-11.183,0-20.24,9.066-20.24,20.249c0,11.173,9.057,20.248,20.24,20.248 c11.187,0,20.248-9.075,20.248-20.248C194.717,212.77,185.655,203.704,174.468,203.704z">
                                </path>
                                <path class="st0"
                                    d="M405.969,62.122c-82.83-82.83-217.108-82.83-299.938,0c-82.813,82.822-82.813,217.12,0,299.924L255.994,512 l149.976-149.953C488.781,279.243,488.781,144.944,405.969,62.122z M205.979,298.28c0,5.173-4.199,9.372-9.378,9.372h-25.897 c-5.174,0-9.373-4.199-9.373-9.372v-19.292h44.648V298.28z M350.661,298.28c0,5.173-4.195,9.372-9.373,9.372H315.4 c-5.187,0-9.382-4.199-9.382-9.372v-19.292h44.643V298.28z M337.519,271.003H174.468c-25.981,0-47.037-21.074-47.037-47.05 c0-20.047,12.544-37.159,30.21-43.932l13.173-47.709c7.111-25.748,30.522-43.581,57.24-43.581h55.888 c26.701,0,50.129,17.833,57.245,43.581l13.164,47.709c17.67,6.782,30.215,23.885,30.215,43.932 C384.566,249.93,363.5,271.003,337.519,271.003z">
                                </path>
                                <path class="st0"
                                    d="M308.433,137.1c-3.799-13.748-16.418-23.358-30.689-23.358h-43.493c-14.275,0-26.89,9.611-30.689,23.376 l-4.792,17.358c-0.342,1.23-0.083,2.539,0.686,3.55c0.768,1.01,1.968,1.608,3.237,1.608h106.61c1.27,0,2.469-0.598,3.237-1.608 c0.778-1.01,1.028-2.32,0.681-3.55L308.433,137.1z">
                                </path>
                                <path class="st0"
                                    d="M337.519,203.704c-11.178,0-20.248,9.066-20.248,20.249c0,11.173,9.07,20.248,20.248,20.248 c11.187,0,20.253-9.075,20.253-20.248C357.772,212.77,348.706,203.704,337.519,203.704z">
                                </path>
                            </g>
                        </g>
                    </svg>

                    <span x-bind:class="isMinimized ? 'md:hidden' : 'block'"
                        class="transition-all duration-300">@lang('translations.fleet_management')</span>
                </a>
            @endcan

            @can('dashboard_view')
                <a href="{{ route('dashboard') }}"
                    class="{{ request()->routeIs('dashboard') ? 'bg-primary text-white' : 'hover:bg-black/5 hover:text-neutral-900 text-neutral-600 dark:text-white/60 hover:dark:bg-white/5 hover:dark:text-white' }} flex items-center rounded-md gap-2 ps-3.5 px-3 py-2.5 text-sm font-medium underline-offset-2  focus-visible:underline outline-none transition-all duration-300  ">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-5">
                        <path
                            d="M11.47 3.841a.75.75 0 0 1 1.06 0l8.69 8.69a.75.75 0 1 0 1.06-1.061l-8.689-8.69a2.25 2.25 0 0 0-3.182 0l-8.69 8.69a.75.75 0 1 0 1.061 1.06l8.69-8.689Z" />
                        <path
                            d="m12 5.432 8.159 8.159c.03.03.06.058.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 0 1-.75-.75v-4.5a.75.75 0 0 0-.75-.75h-3a.75.75 0 0 0-.75.75V21a.75.75 0 0 1-.75.75H5.625a1.875 1.875 0 0 1-1.875-1.875v-6.198a2.29 2.29 0 0 0 .091-.086L12 5.432Z" />
                    </svg>

                    <span x-bind:class="isMinimized ? 'md:hidden' : 'block'"
                        class="transition-all duration-300">@lang('translations.dashboard')</span>
                </a>
            @endcan

            @can('user_view')
                <a href="{{ route('users') }}"
                    class="{{ request()->routeIs('users') ? 'bg-primary text-white' : 'hover:bg-black/5 hover:text-neutral-900 text-neutral-600 dark:text-white/60 hover:dark:bg-white/5 hover:dark:text-white' }} flex items-center rounded-md gap-2 ps-3.5 px-3 py-2.5 text-sm font-medium underline-offset-2  focus-visible:underline outline-none transition-all duration-300  ">
                    <svg class="size-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path
                                d="M15.5 7.5C15.5 9.433 13.933 11 12 11C10.067 11 8.5 9.433 8.5 7.5C8.5 5.567 10.067 4 12 4C13.933 4 15.5 5.567 15.5 7.5Z"
                                fill="currentColor"></path>
                            <path
                                d="M18 16.5C18 18.433 15.3137 20 12 20C8.68629 20 6 18.433 6 16.5C6 14.567 8.68629 13 12 13C15.3137 13 18 14.567 18 16.5Z"
                                fill="currentColor"></path>
                            <path
                                d="M7.12205 5C7.29951 5 7.47276 5.01741 7.64005 5.05056C7.23249 5.77446 7 6.61008 7 7.5C7 8.36825 7.22131 9.18482 7.61059 9.89636C7.45245 9.92583 7.28912 9.94126 7.12205 9.94126C5.70763 9.94126 4.56102 8.83512 4.56102 7.47063C4.56102 6.10614 5.70763 5 7.12205 5Z"
                                fill="currentColor"></path>
                            <path
                                d="M5.44734 18.986C4.87942 18.3071 4.5 17.474 4.5 16.5C4.5 15.5558 4.85657 14.744 5.39578 14.0767C3.4911 14.2245 2 15.2662 2 16.5294C2 17.8044 3.5173 18.8538 5.44734 18.986Z"
                                fill="currentColor"></path>
                            <path
                                d="M16.9999 7.5C16.9999 8.36825 16.7786 9.18482 16.3893 9.89636C16.5475 9.92583 16.7108 9.94126 16.8779 9.94126C18.2923 9.94126 19.4389 8.83512 19.4389 7.47063C19.4389 6.10614 18.2923 5 16.8779 5C16.7004 5 16.5272 5.01741 16.3599 5.05056C16.7674 5.77446 16.9999 6.61008 16.9999 7.5Z"
                                fill="currentColor"></path>
                            <path
                                d="M18.5526 18.986C20.4826 18.8538 21.9999 17.8044 21.9999 16.5294C21.9999 15.2662 20.5088 14.2245 18.6041 14.0767C19.1433 14.744 19.4999 15.5558 19.4999 16.5C19.4999 17.474 19.1205 18.3071 18.5526 18.986Z"
                                fill="currentColor"></path>
                        </g>
                    </svg>

                    <span x-bind:class="isMinimized ? 'md:hidden' : 'block'"
                        class="transition-all duration-300">@lang('translations.user_management')</span>
                </a>
            @endcan

            @can('driver_view')
                <a href="{{ route('drivers') }}"
                    class="{{ request()->routeIs('drivers') ? 'bg-primary text-white' : 'hover:bg-black/5 hover:text-neutral-900 text-neutral-600 dark:text-white/60 hover:dark:bg-white/5 hover:dark:text-white' }} flex items-center rounded-md gap-2 ps-3.5 px-3 py-2.5 text-sm font-medium underline-offset-2  focus-visible:underline outline-none transition-all duration-300  ">
                    <svg class="size-5" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M15.0549 39H23V31.0549C18.8284 31.5161 15.5161 34.8284 15.0549 39ZM25 31.0549V39H32.9451C32.4839 34.8284 29.1716 31.5161 25 31.0549ZM32.9452 41C32.8055 42.2647 32.4041 43.4491 31.7966 44.4993L33.5278 45.5007C34.4643 43.8818 35 42.0019 35 40C35 33.9249 30.0751 29 24 29C17.9249 29 13 33.9249 13 40C13 42.0019 13.5357 43.8818 14.4722 45.5007L16.2034 44.4993C15.5959 43.4491 15.1945 42.2647 15.0548 41H32.9452Z"
                                fill="currentColor"></path>
                            <path
                                d="M27 40C27 41.6569 25.6569 43 24 43C22.3431 43 21 41.6569 21 40C21 38.3431 22.3431 37 24 37C25.6569 37 27 38.3431 27 40Z"
                                fill="currentColor"></path>
                            <path
                                d="M31.5846 36.8449C31.2987 35.778 31.9319 34.6813 32.9988 34.3954L34.9307 33.8778C35.9976 33.5919 37.0943 34.2251 37.3801 35.292L38.4154 39.1557C38.7013 40.2227 38.0681 41.3193 37.0012 41.6052L35.0694 42.1229C34.0024 42.4087 32.9058 41.7756 32.6199 40.7086L31.5846 36.8449Z"
                                fill="currentColor"></path>
                            <path
                                d="M10.6199 35.2912C10.9058 34.2243 12.0024 33.5911 13.0694 33.877L15.0012 34.3947C16.0681 34.6805 16.7013 35.7772 16.4154 36.8441L15.3802 40.7078C15.0943 41.7748 13.9976 42.4079 12.9307 42.1221L10.9988 41.6044C9.93188 41.3185 9.29871 40.2219 9.5846 39.1549L10.6199 35.2912Z"
                                fill="currentColor"></path>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M15 17V14H17V17C17 20.866 20.134 24 24 24C27.866 24 31 20.866 31 17V14H33V17C33 21.9706 28.9706 26 24 26C19.0294 26 15 21.9706 15 17Z"
                                fill="currentColor"></path>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M13.7943 11H34.2494C34.3213 10.8165 34.3989 10.6002 34.4762 10.3526L34.4883 10.3139C34.758 9.45061 35 8.67596 35 7.09677C35 6.29597 34.4797 5.62166 33.791 5.08848C33.0932 4.54829 32.1402 4.08579 31.0787 3.7088C28.9529 2.95383 26.2719 2.5 24 2.5C21.7281 2.5 19.0471 2.95383 16.9213 3.7088C15.8598 4.08579 14.9068 4.54829 14.209 5.08848C13.5203 5.62166 13 6.29597 13 7.09677C13 8.56283 13.2452 9.33549 13.4971 10.1295L13.4971 10.1295C13.5206 10.2035 13.5441 10.2776 13.5675 10.3525C13.6448 10.6001 13.7224 10.8164 13.7943 11ZM20 8C20 7.44772 20.4477 7 21 7H27C27.5523 7 28 7.44772 28 8C28 8.55228 27.5523 9 27 9H21C20.4477 9 20 8.55228 20 8Z"
                                fill="currentColor"></path>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M14.0214 13.4106C14.0668 13.1728 14.2845 13 14.5386 13H33.4614C33.7155 13 33.9332 13.1728 33.9786 13.4106L33.9788 13.4118L33.9791 13.413L33.9796 13.4159L33.9808 13.4227L33.9837 13.4409C33.9858 13.455 33.9882 13.4731 33.9905 13.4948C33.9951 13.5383 33.9993 13.5967 33.9999 13.6678C34.0011 13.8101 33.988 14.0048 33.9341 14.2341C33.8251 14.6976 33.5533 15.2868 32.9308 15.8594C31.6967 16.9946 29.1612 18 24 18C18.8388 18 16.3033 16.9946 15.0692 15.8594C14.4467 15.2868 14.1749 14.6976 14.0659 14.2341C14.012 14.0048 13.9989 13.8101 14.0001 13.6678C14.0007 13.5967 14.0049 13.5383 14.0095 13.4948C14.0118 13.4731 14.0142 13.455 14.0163 13.4409L14.0192 13.4227L14.0204 13.4159L14.0209 13.413L14.0212 13.4118L14.0214 13.4106Z"
                                fill="currentColor"></path>
                        </g>
                    </svg>

                    <span x-bind:class="isMinimized ? 'md:hidden' : 'block'"
                        class="transition-all duration-300">@lang('translations.user_management')</span>
                </a>
            @endcan

            @can('vehicle_view')
                <a href="{{ route('vehicles') }}"
                    class="{{ request()->routeIs('vehicles') ? 'bg-primary text-white' : 'hover:bg-black/5 hover:text-neutral-900 text-neutral-600 dark:text-white/60 hover:dark:bg-white/5 hover:dark:text-white' }} flex items-center rounded-md gap-2 ps-3.5 px-3 py-2.5 text-sm font-medium underline-offset-2  focus-visible:underline outline-none transition-all duration-300  ">
                    <svg class="size-5" fill="currentColor" viewBox="0 0 256 256" id="Flat"
                        xmlns="http://www.w3.org/2000/svg">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path
                                d="M247.99682,119.94092c-.00073-.084-.00952-.168-.01269-.252-.00855-.22216-.02271-.44287-.04907-.66015-.01148-.09424-.02808-.188-.04322-.28223-.03393-.21631-.07568-.4292-.12695-.63965-.02051-.08447-.0415-.16845-.06494-.25293q-.10035-.36108-.23364-.708c-.0149-.03858-.02344-.07862-.03882-.11719l-.04956-.12354-.01075-.02734-13.928-34.82031A15.92368,15.92368,0,0,0,218.58374,72h-34.584V64a8.00039,8.00039,0,0,0-8-8h-152a16.01833,16.01833,0,0,0-16,16V184a16.01833,16.01833,0,0,0,16,16h13.0127a32.00444,32.00444,0,0,0,61.97461,0h58.02539a32.00444,32.00444,0,0,0,61.97461,0h13.01269a16.01833,16.01833,0,0,0,16-16V120C247.99975,119.98,247.99682,119.96094,247.99682,119.94092ZM183.99975,88h34.584l9.59961,24h-44.1836Zm-160-16h144v64h-144Zm44,136a16,16,0,1,1,16-16A16.01833,16.01833,0,0,1,67.99975,208Zm120,0a16,16,0,1,1,16-16A16.01833,16.01833,0,0,1,187.99975,208Z">
                            </path>
                        </g>
                    </svg>

                    <span x-bind:class="isMinimized ? 'md:hidden' : 'block'"
                        class="transition-all duration-300">@lang('translations.vehicle_management')</span>
                </a>
            @endcan


            @can('remote_control')
                <a href="{{ route('remote-control') }}"
                    class="{{ request()->routeIs('remote-control') ? 'bg-primary text-white' : 'hover:bg-black/5 hover:text-neutral-900 text-neutral-600 dark:text-white/60 hover:dark:bg-white/5 hover:dark:text-white' }} flex items-center rounded-md gap-2 ps-3.5 px-3 py-2.5 text-sm font-medium underline-offset-2  focus-visible:underline outline-none transition-all duration-300  ">
                    <svg class="size-5" viewBox="0 -2 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <title>wifi [currentColor]</title>
                            <desc>Created with Sketch.</desc>
                            <defs> </defs>
                            <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <g id="Dribbble-Light-Preview" transform="translate(-60.000000, -3681.000000)"
                                    fill="currentColor">
                                    <g id="icons" transform="translate(56.000000, 160.000000)">
                                        <path
                                            d="M11.9795939,3535.00003 C11.9795939,3536.00002 12.8837256,3537 14,3537 C15.1162744,3537 16.0204061,3536.00002 16.0204061,3535.00003 C16.0204061,3532.00008 11.9795939,3532.00008 11.9795939,3535.00003 M9.71370846,3530.7571 L11.1431458,3532.17208 C12.7180523,3530.6121 15.2819477,3530.6121 16.8568542,3532.17208 L18.2862915,3530.7571 C15.9183756,3528.41413 12.0816244,3528.41413 9.71370846,3530.7571 M4,3525.10019 L5.42842711,3526.51516 C10.1551672,3521.83624 17.8448328,3521.83624 22.5715729,3526.51516 L24,3525.10019 C18.4772199,3519.63327 9.52278008,3519.63327 4,3525.10019 M21.1431458,3527.92914 L19.7147187,3529.34312 C16.5638953,3526.22417 11.4361047,3526.22417 8.28528134,3529.34312 L6.85685423,3527.92914 C10.8016971,3524.0242 17.1983029,3524.0242 21.1431458,3527.92914"
                                            id="wifi-[currentColor]"> </path>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </svg>

                    <span x-bind:class="isMinimized ? 'md:hidden' : 'block'"
                        class="transition-all duration-300">@lang('translations.remote_control')</span>
                </a>
            @endcan

            @can('geofence_view')
                <a href="{{ route('geofencing') }}"
                    class="{{ request()->routeIs('geofencing') ? 'bg-primary text-white' : 'hover:bg-black/5 hover:text-neutral-900 text-neutral-600 dark:text-white/60 hover:dark:bg-white/5 hover:dark:text-white' }} flex items-center rounded-md gap-2 ps-3.5 px-3 py-2.5 text-sm font-medium underline-offset-2  focus-visible:underline outline-none transition-all duration-300  ">
                    <svg class="size-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <path
                                d="M9.14844 7.48828C8.58844 7.48828 8.14844 7.93828 8.14844 8.48828C8.14844 9.03828 8.59844 9.48828 9.14844 9.48828C9.69844 9.48828 10.1484 9.03828 10.1484 8.48828C10.1484 7.93828 9.69844 7.48828 9.14844 7.48828Z"
                                fill="currentColor"></path>
                            <path
                                d="M21.46 5.04C20.62 3.09 18.77 2 16.19 2H7.81C4.6 2 2 4.6 2 7.81V16.19C2 18.77 3.09 20.62 5.04 21.46C5.23 21.54 5.45 21.49 5.59 21.35L21.35 5.59C21.5 5.44 21.55 5.22 21.46 5.04ZM10.53 12.24C10.14 12.62 9.63 12.8 9.12 12.8C8.61 12.8 8.1 12.61 7.71 12.24C6.69 11.28 5.57 9.75 6 7.93C6.38 6.28 7.84 5.54 9.12 5.54C10.4 5.54 11.86 6.28 12.24 7.94C12.66 9.75 11.54 11.28 10.53 12.24Z"
                                fill="currentColor"></path>
                            <path
                                d="M19.4689 20.5295C19.6889 20.7495 19.6589 21.1095 19.3889 21.2595C18.5089 21.7495 17.4389 21.9995 16.1889 21.9995H7.80892C7.51892 21.9995 7.39892 21.6595 7.59892 21.4595L13.6389 15.4195C13.8389 15.2195 14.1489 15.2195 14.3489 15.4195L19.4689 20.5295Z"
                                fill="currentColor"></path>
                            <path
                                d="M22.0017 7.80892V16.1889C22.0017 17.4389 21.7517 18.5189 21.2617 19.3889C21.1117 19.6589 20.7517 19.6789 20.5317 19.4689L15.4117 14.3489C15.2117 14.1489 15.2117 13.8389 15.4117 13.6389L21.4517 7.59892C21.6617 7.39892 22.0017 7.51892 22.0017 7.80892Z"
                                fill="currentColor"></path>
                        </g>
                    </svg>

                    <span x-bind:class="isMinimized ? 'md:hidden' : 'block'"
                        class="transition-all duration-300">@lang('translations.geofencing')</span>
                </a>
            @endcan

            @can('reporting_view')
                <a href="{{ route('reporting') }}"
                    class="{{ request()->routeIs('reporting') ? 'bg-primary text-white' : 'hover:bg-black/5 hover:text-neutral-900 text-neutral-600 dark:text-white/60 hover:dark:bg-white/5 hover:dark:text-white' }} flex items-center rounded-md gap-2 ps-3.5 px-3 py-2.5 text-sm font-medium underline-offset-2  focus-visible:underline outline-none transition-all duration-300  ">
                    <svg fill="currentColor" class="size-5 ms-[1px]" version="1.1" id="Layer_1"
                        xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                        viewBox="0 0 512.002 512.002" xml:space="preserve">
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                        <g id="SVGRepo_iconCarrier">
                            <g>
                                <g>
                                    <g>
                                        <path
                                            d="M512,55.468c0-21.171-17.229-38.4-38.4-38.4H38.4c-21.171,0-38.4,17.229-38.4,38.4v251.733h512V55.468z M85.333,85.334 H102.4c4.71,0,8.533,3.823,8.533,8.533s-3.823,8.533-8.533,8.533H85.333c-4.71,0-8.533-3.823-8.533-8.533 S80.623,85.334,85.333,85.334z M59.733,273.068c0,4.71-3.823,8.533-8.533,8.533s-8.533-3.823-8.533-8.533V85.334 c0-4.71,3.823-8.533,8.533-8.533s8.533,3.823,8.533,8.533V273.068z M119.467,273.068H85.333c-4.71,0-8.533-3.823-8.533-8.533 s3.823-8.533,8.533-8.533h34.133c4.71,0,8.533,3.823,8.533,8.533S124.177,273.068,119.467,273.068z M76.8,230.401 c0-4.71,3.823-8.533,8.533-8.533H102.4c4.71,0,8.533,3.823,8.533,8.533c0,4.71-3.823,8.533-8.533,8.533H85.333 C80.623,238.934,76.8,235.111,76.8,230.401z M119.467,204.801H85.333c-4.71,0-8.533-3.823-8.533-8.533s3.823-8.533,8.533-8.533 h34.133c4.71,0,8.533,3.823,8.533,8.533S124.177,204.801,119.467,204.801z M76.8,162.134c0-4.71,3.823-8.533,8.533-8.533H102.4 c4.71,0,8.533,3.823,8.533,8.533c0,4.71-3.823,8.533-8.533,8.533H85.333C80.623,170.668,76.8,166.845,76.8,162.134z M119.467,136.534H85.333c-4.71,0-8.533-3.823-8.533-8.533c0-4.71,3.823-8.533,8.533-8.533h34.133 c4.71,0,8.533,3.823,8.533,8.533C128,132.711,124.177,136.534,119.467,136.534z M469.333,170.668c0,4.71-3.823,8.533-8.533,8.533 s-8.533-3.823-8.533-8.533v-39.134L343.953,239.847c-1.664,1.664-3.849,2.5-6.033,2.5c-2.185,0-4.369-0.836-6.033-2.5 l-70.511-70.511l-92.962,100.975c-1.681,1.835-3.977,2.756-6.281,2.756c-2.065,0-4.139-0.742-5.777-2.253 c-3.465-3.191-3.695-8.593-0.503-12.058l98.987-107.52c0.043-0.051,0.111-0.068,0.154-0.111s0.051-0.102,0.094-0.145 c0.375-0.375,0.845-0.546,1.263-0.836c0.469-0.324,0.896-0.717,1.408-0.939c0.529-0.23,1.075-0.273,1.63-0.393 c0.521-0.102,1.024-0.29,1.553-0.299c0.555-0.008,1.084,0.154,1.621,0.247s1.075,0.111,1.587,0.307 c0.521,0.196,0.956,0.563,1.434,0.87c0.435,0.273,0.922,0.435,1.314,0.794c0.051,0.043,0.068,0.102,0.111,0.145 s0.102,0.06,0.145,0.102l70.767,70.767l102.281-102.281h-39.134c-4.71,0-8.533-3.823-8.533-8.533c0-4.71,3.823-8.533,8.533-8.533 H460.8c1.109,0,2.219,0.23,3.26,0.657c2.091,0.87,3.746,2.526,4.617,4.617c0.427,1.041,0.657,2.15,0.657,3.26V170.668z">
                                        </path>
                                        <path
                                            d="M0.002,324.268v29.867c0,21.171,17.22,38.4,38.4,38.4h164.002l-12.8,51.2h-1.869c-23.526,0-42.667,19.14-42.667,42.667 c0,4.71,3.823,8.533,8.533,8.533h204.8c4.71,0,8.533-3.823,8.533-8.533c0-23.526-19.14-42.667-42.667-42.667H322.4l-12.8-51.2 h164.002c21.171,0,38.4-17.229,38.4-38.4v-29.867H0.002z M207.2,443.734l12.8-51.2h72.004l12.8,51.2H207.2z">
                                        </path>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </svg>
                    <span x-bind:class="isMinimized ? 'md:hidden' : 'block'"
                        class="transition-all duration-300">@lang('translations.reporting')</span>
                </a>
            @endcan

            {{-- <a href="{{ route('alarms') }}"
                class="{{ request()->routeIs('alarms') ? 'bg-primary text-white' : 'hover:bg-black/5 hover:text-neutral-900 text-neutral-600 dark:text-white/60 hover:dark:bg-white/5 hover:dark:text-white' }} flex items-center rounded-md gap-2 ps-3.5 px-3 py-2.5 text-sm font-medium underline-offset-2  focus-visible:underline outline-none transition-all duration-300">
                <svg class="size-5" version="1.1" id="_x32_" xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512" xml:space="preserve"
                    fill="#000000">
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                    <g id="SVGRepo_iconCarrier">
                        <style type="text/css">
                            .st0 {
                                fill: currentColor;
                            }
                        </style>
                        <g>
                            <path class="st0"
                                d="M193.499,459.298c5.237,30.54,31.518,52.702,62.49,52.702c30.98,0,57.269-22.162,62.506-52.702l0.32-1.86 H193.179L193.499,459.298z">
                            </path>
                            <path class="st0"
                                d="M469.782,371.98c-5.126-5.128-10.349-9.464-15.402-13.661c-21.252-17.648-39.608-32.888-39.608-96.168v-50.194 c0-73.808-51.858-138.572-123.61-154.81c2.876-5.64,4.334-11.568,4.334-17.655C295.496,17.718,277.777,0,255.995,0 c-21.776,0-39.492,17.718-39.492,39.492c0,6.091,1.456,12.018,4.334,17.655c-71.755,16.238-123.61,81.002-123.61,154.81v50.194 c0,63.28-18.356,78.521-39.608,96.168c-5.052,4.196-10.276,8.533-15.402,13.661l-0.466,0.466v49.798h428.496v-49.798 L469.782,371.98z">
                            </path>
                        </g>
                    </g>
                </svg>



                <span x-bind:class="isMinimized ? 'md:hidden' : 'block'"
                    class="transition-all duration-300">@lang('translations.alarms')</span>
            </a> --}}


            <a href="{{ route('settings') }}"
                class="{{ request()->routeIs('settings') ? 'bg-primary text-white' : 'hover:bg-black/5 hover:text-neutral-900 text-neutral-600 dark:text-white/60 hover:dark:bg-white/5 hover:dark:text-white' }} flex items-center rounded-md gap-2 ps-3.5 px-3 py-2.5 text-sm font-medium underline-offset-2  focus-visible:underline outline-none transition-all duration-300 mt-auto">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-5">
                    <path fill-rule="evenodd"
                        d="M11.078 2.25c-.917 0-1.699.663-1.85 1.567L9.05 4.889c-.02.12-.115.26-.297.348a7.493 7.493 0 0 0-.986.57c-.166.115-.334.126-.45.083L6.3 5.508a1.875 1.875 0 0 0-2.282.819l-.922 1.597a1.875 1.875 0 0 0 .432 2.385l.84.692c.095.078.17.229.154.43a7.598 7.598 0 0 0 0 1.139c.015.2-.059.352-.153.43l-.841.692a1.875 1.875 0 0 0-.432 2.385l.922 1.597a1.875 1.875 0 0 0 2.282.818l1.019-.382c.115-.043.283-.031.45.082.312.214.641.405.985.57.182.088.277.228.297.35l.178 1.071c.151.904.933 1.567 1.85 1.567h1.844c.916 0 1.699-.663 1.85-1.567l.178-1.072c.02-.12.114-.26.297-.349.344-.165.673-.356.985-.57.167-.114.335-.125.45-.082l1.02.382a1.875 1.875 0 0 0 2.28-.819l.923-1.597a1.875 1.875 0 0 0-.432-2.385l-.84-.692c-.095-.078-.17-.229-.154-.43a7.614 7.614 0 0 0 0-1.139c-.016-.2.059-.352.153-.43l.84-.692c.708-.582.891-1.59.433-2.385l-.922-1.597a1.875 1.875 0 0 0-2.282-.818l-1.02.382c-.114.043-.282.031-.449-.083a7.49 7.49 0 0 0-.985-.57c-.183-.087-.277-.227-.297-.348l-.179-1.072a1.875 1.875 0 0 0-1.85-1.567h-1.843ZM12 15.75a3.75 3.75 0 1 0 0-7.5 3.75 3.75 0 0 0 0 7.5Z"
                        clip-rule="evenodd" />
                </svg>

                <span x-bind:class="isMinimized ? 'md:hidden' : 'block'"
                    class="transition-all duration-300">@lang('translations.settings')</span>
            </a>


            <a href="{{ route('logout') }}"
                class="{{ request()->routeIs('logout') ? 'bg-primary text-white' : 'hover:bg-black/5 hover:text-neutral-900 text-neutral-600 dark:text-white/60 hover:dark:bg-white/5 hover:dark:text-white' }} flex items-center rounded-md gap-2 px-3 ps-3.5 py-2.5 text-sm font-medium underline-offset-2  focus-visible:underline outline-none transition-all duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-5">
                    <path fill-rule="evenodd"
                        d="M16.5 3.75a1.5 1.5 0 0 1 1.5 1.5v13.5a1.5 1.5 0 0 1-1.5 1.5h-6a1.5 1.5 0 0 1-1.5-1.5V15a.75.75 0 0 0-1.5 0v3.75a3 3 0 0 0 3 3h6a3 3 0 0 0 3-3V5.25a3 3 0 0 0-3-3h-6a3 3 0 0 0-3 3V9A.75.75 0 1 0 9 9V5.25a1.5 1.5 0 0 1 1.5-1.5h6ZM5.78 8.47a.75.75 0 0 0-1.06 0l-3 3a.75.75 0 0 0 0 1.06l3 3a.75.75 0 0 0 1.06-1.06l-1.72-1.72H15a.75.75 0 0 0 0-1.5H4.06l1.72-1.72a.75.75 0 0 0 0-1.06Z"
                        clip-rule="evenodd" />
                </svg>

                <span x-bind:class="isMinimized ? 'md:hidden' : 'block'"
                    class="transition-all duration-300">@lang('translations.logout')</span>
            </a>

            <div x-bind:class="isMinimized ? 'md:hidden' : 'block'"
                class="transition-all duration-300 px-3 pt-2.5 border-t border-t-gray-400 dark:border-t-gray-600 text-xs text-slate-600 dark:text-slate-400">
                © {{ date('Y') }} ControlOne.it
            </div>
        </div>
    </nav>

    <!-- top navbar & main content  -->
    <main class="w-full overflow-y-auto h-svh">
        <!-- top navbar  -->
        <nav class="sticky top-0 z-10 flex items-center justify-between px-4 py-2 border-b border-neutral-300 bg-neutral-50 dark:bg-slate-900 dark:border-slate-700"
            aria-label="top navibation bar">

            <!-- Left Section -->
            <div class="flex items-center gap-2 md:hidden">
                <!-- Toggle Sidebar on Mobile -->
                <button x-on:click="sidebarIsOpen = true;" type="button"
                    class="inline-flex items-center justify-center gap-2 rounded border border-slate-300 bg-white px-2 py-1.5 font-semibold leading-6 text-slate-800 shadow-sm hover:border-slate-300 hover:bg-slate-100 hover:text-slate-800 hover:shadow outline-none focus:ring focus:ring-slate-500/25 active:border-white active:bg-white active:shadow-none dark:bg-slate-950 dark:border-slate-500 dark:text-slate-500"
                    x-on:click="mobileSidebarOpen = true">
                    <svg class="inline-block w-5 h-5 hi-solid hi-menu-alt-1" fill="currentColor" viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd"
                            d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                            clip-rule="evenodd" />
                    </svg>
                </button>
                <!-- END Toggle Sidebar on Mobile -->
            </div>


            <div>

            </div>

            <a href="@can('fleet_view')
                {{ route('fleet-management') }}
            @endcan"
                class="md:hidden">
                <img class="h-10 dark:hidden" src="{{ asset('assets/images/logo-icon.png') }}"
                    alt="controllone logo">

                <img class="hidden h-10 dark:block" src="{{ asset('assets/images/logo-white.png') }}"
                    alt="logo">

            </a>


            <div class="flex items-center justify-end gap-2 md:gap-4">
                {{-- notifications --}}
                @can('notifications_view')
                    <livewire:components.notifications>
                    @endcan


                    {{-- language switcher --}}
                    <div class="flex items-center text-slate-700 dark:text-slate-400">
                        <livewire:components.lang-switcher />
                    </div>

                    <!-- Profile Menu  -->
                    <div x-data="{ userDropdownIsOpen: false }" class="relative shrink-0"
                        x-on:keydown.esc.window="userDropdownIsOpen = false">
                        <button type="button"
                            class="flex items-center gap-2 text-left rounded-md cursor-pointer w-fit md:p-2 text-neutral-600 hover:bg-black/5 hover:text-neutral-900 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-black"
                            x-bind:class="userDropdownIsOpen ? 'bg-black/10' : ''" aria-haspopup="true"
                            x-on:click="userDropdownIsOpen = ! userDropdownIsOpen"
                            x-bind:aria-expanded="userDropdownIsOpen">
                            <img src="https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_1280.png"
                                class="object-cover rounded-full size-8" alt="avatar" aria-hidden="true" />
                        </button>

                        <!-- menu -->
                        <div x-cloak x-show="userDropdownIsOpen"
                            class="absolute right-0 z-20 w-48 bg-white border divide-y rounded-md dark:bg-slate-900 top-14 h-fit divide-neutral-300 dark:divide-slate-500 border-neutral-300 dark:border-slate-500"
                            role="menu" x-on:click.outside="userDropdownIsOpen = false"
                            x-on:keydown.down.prevent="$focus.wrap().next()"
                            x-on:keydown.up.prevent="$focus.wrap().previous()" x-transition=""
                            x-trap="userDropdownIsOpen">


                            <div class="flex flex-col py-1.5">
                                <a href="{{ route('settings') }}"
                                    class="flex items-center gap-2 px-2 py-1.5 text-sm font-medium text-neutral-600 underline-offset-2 hover:bg-black/5 hover:text-neutral-900 focus-visible:underline outline-none  dark:text-slate-400"
                                    role="menuitem">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                                        class="size-5 shrink-0" aria-hidden="true">
                                        <path fill-rule="evenodd"
                                            d="M7.84 1.804A1 1 0 0 1 8.82 1h2.36a1 1 0 0 1 .98.804l.331 1.652a6.993 6.993 0 0 1 1.929 1.115l1.598-.54a1 1 0 0 1 1.186.447l1.18 2.044a1 1 0 0 1-.205 1.251l-1.267 1.113a7.047 7.047 0 0 1 0 2.228l1.267 1.113a1 1 0 0 1 .206 1.25l-1.18 2.045a1 1 0 0 1-1.187.447l-1.598-.54a6.993 6.993 0 0 1-1.929 1.115l-.33 1.652a1 1 0 0 1-.98.804H8.82a1 1 0 0 1-.98-.804l-.331-1.652a6.993 6.993 0 0 1-1.929-1.115l-1.598.54a1 1 0 0 1-1.186-.447l-1.18-2.044a1 1 0 0 1 .205-1.251l1.267-1.114a7.05 7.05 0 0 1 0-2.227L1.821 7.773a1 1 0 0 1-.206-1.25l1.18-2.045a1 1 0 0 1 1.187-.447l1.598.54A6.992 6.992 0 0 1 7.51 3.456l.33-1.652ZM10 13a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    <span>@lang('translations.settings')</span>
                                </a>
                            </div>

                            <div class="flex flex-col py-1.5">
                                <a href="{{ route('logout') }}"
                                    class="flex items-center gap-2 px-2 py-1.5 text-sm font-medium text-neutral-600 underline-offset-2 hover:bg-black/5 hover:text-neutral-900 focus-visible:underline outline-none  dark:text-slate-400"
                                    role="menuitem">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                                        class="size-5 shrink-0" aria-hidden="true">
                                        <path fill-rule="evenodd"
                                            d="M3 4.25A2.25 2.25 0 0 1 5.25 2h5.5A2.25 2.25 0 0 1 13 4.25v2a.75.75 0 0 1-1.5 0v-2a.75.75 0 0 0-.75-.75h-5.5a.75.75 0 0 0-.75.75v11.5c0 .414.336.75.75.75h5.5a.75.75 0 0 0 .75-.75v-2a.75.75 0 0 1 1.5 0v2A2.25 2.25 0 0 1 10.75 18h-5.5A2.25 2.25 0 0 1 3 15.75V4.25Z"
                                            clip-rule="evenodd" />
                                        <path fill-rule="evenodd"
                                            d="M6 10a.75.75 0 0 1 .75-.75h9.546l-1.048-.943a.75.75 0 1 1 1.004-1.114l2.5 2.25a.75.75 0 0 1 0 1.114l-2.5 2.25a.75.75 0 1 1-1.004-1.114l1.048-.943H6.75A.75.75 0 0 1 6 10Z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    <span>@lang('translations.sign_out')</span>
                                </a>
                            </div>
                        </div>
                    </div>
            </div>
        </nav>
