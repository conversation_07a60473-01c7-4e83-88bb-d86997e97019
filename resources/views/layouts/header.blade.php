<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{ $title ?? 'Dashboard - ControllOne' }}</title>

    <meta name="description"
        content="ControllOne has been at the forefront of GPS tracking and fleet management solutions for over 15 years, serving companies of all sizes and industries. Our mission is to provide unparalleled insight and control, enabling companies to optimize their operations, reduce costs and improve safety." />

    <meta name="keywords" content="controllone, fleet managment, GPS tracking device, vehicle tracking" />

    <link rel="canonical" href="https://www.controllone.it" />

    <meta property="og:title" content="ControllOne">
    <meta property="og:site_name" content="ControllOne">
    <meta property="og:url" content="https://www.controllone.it">
    <meta property="og:description"
        content="ControllOne has been at the forefront of GPS tracking and fleet management solutions for over 15 years, serving companies of all sizes and industries. Our mission is to provide unparalleled insight and control, enabling companies to optimize their operations, reduce costs and improve safety.">
    <meta property="og:type" content="business">
    <meta property="og:image" content="{{ asset('assets/images/favicon.png') }}">

    <!-- favicon -->
    <link rel="shortcut icon" href="{{ asset('assets/images/favicon.png') }}" type="image/png">

    <!-- fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
        rel="stylesheet">

    <!-- css -->
    <link rel="stylesheet" href="{{ asset('assets/css/style.css') }}?r=12342342" />
    {{-- <link rel="stylesheet" href="{{ asset('assets/css/tailwind.css') }}?r={{ rand(1111, 9999) }}" /> --}}

    @vite('resources/css/app.css')



    @stack('styles')
</head>

<body x-data="{
    darkMode: localStorage.getItem('darkMode') === 'true',
    toggleDarkMode() {
        this.darkMode = !this.darkMode;
        localStorage.setItem('darkMode', this.darkMode);
        document.documentElement.classList.toggle('dark', this.darkMode);
        console.log('Dark mode:', localStorage.getItem('darkMode'));
    }
}" x-init="document.documentElement.classList.toggle('dark', darkMode)"
    class="font-roboto flex flex-shrink-0 bg-[#F7F8FA] dark:bg-slate-900 overflow-x-hidden">

    <livewire:components.notification />
