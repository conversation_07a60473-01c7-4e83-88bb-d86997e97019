<main class="bg-slate-50 dark:bg-slate-900">
    <div class="grid text-sm md:grid-cols-2 md:h-screen">

        <img class="hidden object-cover object-center w-full h-full md:block"
            src="{{ asset('assets/images/login.jpeg') }}" alt="Login">

        <div class="p-10 md:p-16">
            <div class="flex items-center justify-between gap-3">
                <img class="h-8 dark:hidden" src="{{ asset('assets/images/logo.svg') }}" alt="logo">
                <img class="hidden h-8 dark:block" src="{{ asset('assets/images/logo-light.png') }}" alt="logo">
                <div class="text-slate-600 dark:text-slate-300">
                    <livewire:components.lang-switcher />
                </div>
            </div>

            <img class="object-cover object-center w-full mt-8 md:h-full md:hidden rounded-2xl"
                src="{{ asset('assets/images/login.jpeg') }}" alt="Login">


            <div class="flex flex-col items-center justify-center mt-10 md:mt-5 md:h-full">
                <div class="flex-shrink-0 w-full">
                    <h1 class="text-2xl font-semibold font-poppins text-slate-700 dark:text-slate-100">
                        @lang('translations.platform_login')
                    </h1>

                    <p class="mt-2 text-sm text-slate-500 dark:text-slate-300">

                        @lang('translations.enter_credentials_to_login')
                    </p>
                    <div class="mt-5">
                        <label for="email"
                            class="text-sm text-slate-500 dark:text-slate-300">@lang('translations.email')</label>
                        <input type="email" wire:model="email" id="email"
                            class="w-full px-3 py-3 mt-1 transition-all duration-300 border rounded-lg outline-none border-slate-200 focus:border-primary dark:bg-slate-800 dark:text-slate-300 dark:border-slate-500 dark:focus:border-primary"
                            placeholder="@lang('translations.enter_your_email')" />
                        @error('email')
                            <span class="text-xs text-red-500">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="mt-4">
                        <label for="password"
                            class="mt-3 text-sm text-slate-500 dark:text-slate-300">@lang('translations.password')</label>

                        <div class="relative" x-data="{ showPassword: false }">
                            <input type="password" x-bind:type="showPassword ? 'text' : 'password'" id="password"
                                wire:model.blur="password"
                                class="block w-full border-extraLightGray border placeholder:text-lightGray placeholder:text-sm placeholder:font-light rounded-lg mt-2 px-3 py-3 outline-none focus:border-primary focus:border-[1.3px] transition-all duration-300 dark:bg-slate-800 dark:text-slate-300 dark:border-slate-500 dark:focus:border-primary"
                                placeholder="********">

                            <!-- password show hide button -->
                            <div class="absolute z-10 cursor-pointer top-3 right-5"
                                @click="showPassword = !showPassword">
                                <img x-show="!showPassword" id="show-icon" class="size-5"
                                    src="{{ asset('assets/images/eye.svg') }}" alt="Show password">
                                <img x-show="showPassword" id="hide-icon" class="size-5"
                                    src="{{ asset('assets/images/eye-closed.svg') }}" alt="Hide password">
                            </div>
                        </div>
                        @error('password')
                            <div class="mt-2 text-xs text-red-500">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    <p class="mt-2 text-slate-500 text-end dark:text-slate-400"><a
                            href="{{ route('forgot-password') }}">@lang('translations.forgot_password')</a></p>

                    <div class="mt-1">
                        <label>
                            <input type="checkbox" wire:model="remember_me" id="remember_me"
                                class="mr-2 accent-primary">
                            <span class="text-sm text-slate-500 dark:text-slate-400">@lang('translations.remember_me')</span>
                        </label>
                    </div>

                    <div>
                        <button wire:click="login"
                            class="w-full px-3 py-3 mt-5 text-white transition-all duration-300 rounded-lg outline-none bg-primary hover:bg-primaryDark focus:ring-2 focus:ring-primary focus:ring-offset-2">
                            <span wire:loading.remove wire:target="login">
                                @lang('translations.login')
                            </span>
                            <div wire:loading wire:target="login">
                                <div class="w-6 h-6 dot-spinner">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>

                        </button>
                    </div>
                </div>

            </div>
        </div>

    </div>
</main>
