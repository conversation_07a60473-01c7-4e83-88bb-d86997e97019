<div x-data="{ open: false }" class="relative z-[11111] items-center gap-2 text-inherit inline-flex">
    <svg @click="open = !open" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.2"
        stroke="currentColor" class="cursor-pointer size-6">
        <path stroke-linecap="round" stroke-linejoin="round"
            d="M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418" />
    </svg>


    <!-- Notifications Dropdown -->
    <div x-show="open" x-cloak @click.outside="open = false" x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform scale-90 -translate-y-10"
        x-transition:enter-end="opacity-100 transform scale-100 translate-y-0"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100 transform scale-100 translate-y-0"
        x-transition:leave-end="opacity-0 transform scale-90 -translate-y-10"
        class="absolute overflow-y-auto text-gray-700 bg-white rounded-lg shadow min-w-max lg:mx-10 ms-5 me-3 lg:top-10 top-10 -right-5 dark:bg-slate-900 dark:text-gray-300">

        @foreach ($languages as $shortCode => $language)
            <div class="p-3 border-b border-b-gray-200 dark:border-slate-500 cursor-pointer hover:bg-primary hover:text-white @if ($currentLanguage == $shortCode) bg-primary text-white @endif"
                wire:click="changeLanguage('{{ $shortCode }}'); open = false;">
                <div class="flex items-center">
                    <img class="w-4" src="{{ asset('assets/images/flags/' . $language['flag']) }}"
                        alt="{{ $language['name'] }} flag">
                    <div class="ms-3">
                        <h3 class="font-medium text-sm @if ($currentLanguage == $shortCode) font-semibold @endif">
                            {{ $language['name'] }}</h3>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</div>
