<div x-data="{ open: false }" class="relative flex items-center gap-2">
    <button
        class="p-1.5 rounded-full border border-slate-300 relative text-gray-700 dark:text-slate-400 dark:border-slate-400"
        @click="open = !open;">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
            class="size-5">
            <path stroke-linecap="round" stroke-linejoin="round"
                d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0" />
        </svg>
    </button>

    <!-- Blink Indicator -->
    {{-- <span class="absolute w-2 h-2 rounded-full -top-1 -right-1 bg-primary blink-indicator"></span>
    </button> --}}


    <!-- Notifications Dropdown -->
    <div x-show="open" x-cloak @click.outside="open = false" x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform scale-90 -translate-y-10"
        x-transition:enter-end="opacity-100 transform scale-100 translate-y-0"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100 transform scale-100 translate-y-0"
        x-transition:leave-end="opacity-0 transform scale-90 -translate-y-10"
        class="absolute lg:w-96 w-72 min-w-fit max-h-[30rem] overflow-y-auto lg:mx-10 ms-5 me-3 lg:top-10 top-20 -right-16 md:-right-8 bg-white dark:bg-slate-900 dark:text-white rounded-lg shadow min-h-[30rem] border border-secondary dark:border-slate-500">
        <div class="p-4 border-b border-b-secondary dark:border-slate-500">
            <h2 class="mb-2 text-lg font-medium tracking-wide font-openSans">
                {{ __('translations.notifications') }}
            </h2>
        </div>

        @if ($notifications->isEmpty())
            <div class="p-5">
                <p class="font-medium text-textPrimary">
                    {{ __('translations.no_notifications_yet') }}
                </p>
            </div>
        @else
            @foreach ($notifications as $notification)
                <div class="p-5 border-b border-b-secondary dark:border-slate-500">
                    <div class="flex">
                        <div
                            class="flex items-center justify-center flex-shrink-0 w-8 h-8 overflow-hidden rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0M3.124 7.5A8.969 8.969 0 0 1 5.292 3m13.416 0a8.969 8.969 0 0 1 2.168 4.5" />
                            </svg>
                        </div>

                        <div class="ms-2">
                            <h3 class="text-sm font-semibold">
                                {{ $notification['title'] ?? '' }}
                            </h3>
                            <p class="mt-2 text-xs font-medium text-slate-600 dark:text-slate-400 dark:font-normal">
                                {{ $notification['body'] ?? '' }}
                            </p>
                            <div class="mt-2 text-xs font-light text-slate-500 dark:text-slate-400">
                                {{ $notification['created_at'] ?? '' }}
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        @endif

    </div>




</div>
