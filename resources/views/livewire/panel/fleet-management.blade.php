@push('styles')
    <style>
        #seek-container {
            width: 100%;
            background: #F54619;
            padding: 10px;
            display: flex;
            align-items: center;
        }

        #seekbar {
            width: 100%;
        }

        #seekbar {
            flex-grow: 1;
            margin: 0 10px;
            -webkit-appearance: none;
            height: 5px;
            background: #d3d3d3;
            outline: none;
            opacity: 0.7;
            transition: opacity .2s;
            width: 100%;
        }

        #seekbar:hover {
            opacity: 1;
        }

        #seekbar::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 15px;
            height: 15px;
            background: #fff;
            cursor: pointer;
            border-radius: 50%;
        }

        #seekbar::-moz-range-thumb {
            width: 15px;
            height: 15px;
            background: #F54619;
            cursor: pointer;
            border-radius: 50%;
        }

        #currentTime {
            color: white;
            font-size: 14px;
        }


        #map-btn,
        #close-geofence-btn {
            cursor: pointer;
            background: #f44336;
            color: white;
            border: none;

            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1111;

            right: 50px;
            width: fit-content;
            display: flex;
            align-items: center;
            border-radius: 6px;
            font-weight: bold;
            font-size: 11px;
            padding: 4px 5px;
            gap: 3px;

        }



        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 25px;
            cursor: pointer;
            margin: 0px 30px;
        }

        .toggle-switch input[type="checkbox"] {
            display: none;
        }

        .toggle-switch-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ddd;
            border-radius: 20px;
            box-shadow: inset 0 0 0 2px #ccc;
            transition: background-color 0.3s ease-in-out;
        }

        .toggle-switch-handle {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 25px;
            height: 25px;
            background-color: #fff;
            border-radius: 50%;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease-in-out;
        }

        .toggle-switch::before {
            content: "";
            position: absolute;
            top: -25px;
            right: -35px;
            font-size: 12px;
            font-weight: bold;
            color: #aaa;
            text-shadow: 1px 1px #fff;
            transition: color 0.3s ease-in-out;
        }

        .toggle-switch input[type="checkbox"]:checked+.toggle-switch-handle {
            transform: translateX(30px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2), 0 0 0 3px #F54619;
        }

        .toggle-switch input[type="checkbox"]:checked+.toggle-switch-background {
            background-color: #F54619;
            box-shadow: inset 0 0 0 2px #F54619;
        }

        .toggle-switch input[type="checkbox"]:checked+.toggle-switch:before {
            content: "On";
            color: #F54619;
            right: -15px;
        }

        .toggle-switch input[type="checkbox"]:checked+.toggle-switch-background .toggle-switch-handle {
            transform: translateX(30px);
        }
    </style>
@endpush

<div wire:poll.5s="loadLiveData"
    class="relative flex flex-col-reverse h-auto min-h-[80vh] text-sm md:h-[90%] overflow-y-scroll md:flex-row grow">

    @can('fleet_view')
        <div x-data="{ open: true, showModal: false }" x-bind:class="open == true ? 'w-full md:w-[28%]' : 'w-fit'"
            class="absolute top-0 bottom-0 left-0 right-0 md:relative z-[999999999]">
            <!-- Floating Toggle Button -->
            <button @click="open = !open" x-data="{ showTooltip: false }" @mouseenter="showTooltip = true"
                @mouseleave="showTooltip = false"
                class="absolute z-50 block p-2 text-white transition-all duration-300 rounded-full shadow-md md:bottom-5 top-20 md:top-auto bg-primary hover:bg-primaryDark"
                x-bind:class="open ? 'md:left-[100%] left-[90%]' : 'left-0'">
                <svg x-bind:class="open ? 'rotate-0' : 'rotate-180'" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                </svg>

                <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                    x-transition:enter-start="opacity-0 transform scale-95"
                    x-transition:enter-end="opacity-100 transform scale-100"
                    x-transition:leave="transition ease-in duration-150"
                    x-transition:leave-start="opacity-100 transform scale-100"
                    x-transition:leave-end="opacity-0 transform scale-95"
                    class="absolute left-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                    style="display: none;">
                    @lang('translations.vehicle_list')
                </div>
            </button>
            {{-- sidebar --}}
            <div class="2xl:p-5 p-3 bg-white dark:bg-slate-800 shadow-[5px_0px_5px_rgba(0,0,0,0.1)] overflow-y-scroll h-full w-full"
                x-bind:class="open == true ? 'block' : 'hidden'">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-slate-800 dark:text-slate-100">
                        @lang('translations.vehicles_list')
                    </h2>

                    <!-- Add loading states -->
                    <div wire:loading wire:target="loadLiveData" class="inset-0 z-50 flex items-center justify-center">
                        <div class="w-8 h-8 border-b-2 rounded-full animate-spin border-primary"></div>
                    </div>
                </div>

                <label
                    class="flex items-center mt-3 text-sm border rounded-sm ps-2 border-slate-300 focus-within:border-slate-400 text-slate-700 dark:text-slate-300 dark:border-slate-500 dark:focus-within:border-slate-400">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="size-5">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                    </svg>
                    <input type="text" wire:model.live.debounce.500ms="search"
                        class="px-2 py-2 bg-transparent border-none outline-none" placeholder="Search">
                </label>

                <div class="flex flex-wrap gap-2 mt-3 text-xs">
                    <!-- All Tab -->
                    <button wire:click="setFilter('all')"
                        class="px-3 py-2 text-gray-500 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-300 @if ($filter == 'all') ring-2 ring-red-300 border-primary text-primary @endif">
                        @lang('translations.all')
                    </button>

                    <!-- Moving Tab -->
                    <button wire:click="setFilter('moving')"
                        class="flex items-center px-3 py-2 space-x-2 text-gray-800 border border-gray-300 rounded-lg dark:text-slate-300 hover:border-green-500 hover:text-green-500 focus:outline-none focus:ring-2 focus:ring-green-300  @if ($filter == 'moving') border-green-300 ring-2 ring-green-300 @endif">
                        <span class="bg-green-500 rounded-full size-2"></span>
                        <span>@lang('translations.moving')</span>
                    </button>

                    <!-- Stopped Tab -->
                    <button wire:click="setFilter('stopped')"
                        class="flex items-center px-3 py-2 space-x-2 text-gray-800 border border-gray-300 rounded-lg dark:text-slate-300 hover:border-yellow-500 hover:text-yellow-500 focus:outline-none focus:ring-2 focus:ring-yellow-300  @if ($filter == 'stopped') ring-2 ring-yellow-300 border-yellow-300 @endif">
                        <span class="bg-yellow-500 rounded-full size-2"></span>
                        <span>@lang('translations.stopped')</span>
                    </button>

                    <!-- Parked Tab -->
                    <button wire:click="setFilter('parked')"
                        class="flex items-center px-3 py-2 space-x-2 text-gray-800 border border-gray-300 rounded-lg dark:text-slate-300 hover:border-red-500 hover:text-red-500 focus:outline-none focus:ring-2 focus:ring-red-300  @if ($filter == 'parked') ring-2 ring-red-300 border-red-300 @endif">
                        <span class="bg-red-500 rounded-full size-2"></span>
                        <span>@lang('translations.parked')</span>
                    </button>
                </div>

                <div class="mt-3 text-gray-500 dark:text-gray-300">
                    @lang('translations.vehicles') ({{ array_sum(array_map(fn($vehicles) => count($vehicles), $devices)) }})
                </div>

                <div x-data="{
                    vehicleDetails: $wire.entangle('vehicleDetails').live,
                    selectedImei: null,
                    init() {
                        this.$watch('vehicleDetails', (value) => {
                            if (value) {
                                this.selectedImei = value.imei;
                                this.showModal = true;
                            }
                        });
                    }
                }"
                    x-on:update-selected-vehicle.window="
    if ($event.detail.imei === selectedImei) {
        vehicleDetails = $event.detail;
    }
">
                    @php
                        $index = 0;
                    @endphp
                    @foreach ($devices as $type => $vehicles)
                        <div wire:key="devices-{{ ++$index }}" x-data="{ show: true }">
                            <h3 @click="show =!show"
                                class="flex items-center gap-2 mt-3 text-base font-semibold cursor-pointer select-none dark:text-gray-100">
                                <svg width="12" height="13" class="transition-all duration-300"
                                    :class="show ? 'rotate-0' : 'rotate-180'" viewBox="0 0 12 13" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6 2.89999L0 9.29999H12L6 2.89999Z" fill="currentColor" />
                                </svg>
                                @lang('translations.' . $type)
                                <img class="size-5"
                                    src="{{ asset('assets/images/icons/' . ($type ?? 'default') . '.svg') }}"
                                    alt="icon">
                            </h3>
                            <div x-show="show" x-cloak class="mt-3 space-y-2">
                                @foreach ($vehicles as $vehicle)
                                    <!-- List Item -->
                                    <div wire:key="vehicles-{{ $vehicle->id ?? 0 }}"
                                        wire:click="updateVehicleDetails({{ $vehicle }})"
                                        @click="if (window.innerWidth < 600) open = false;focusOnMarker('{{ $vehicle->imei }}', {{ $vehicle->latitude }}, {{ $vehicle->longitude }}, '{{ $vehicle->icon }}');"
                                        class="flex items-end justify-between p-3 pb-4 transition-all duration-300 border-b border-gray-200 rounded-t-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-900 gap-2 flex-wrap @if (isset($vehicleDetails['imei']) && $vehicleDetails['imei'] == $vehicle->imei) bg-primary/10 border-l-4 border-l-primary @endif">

                                        <!-- Left Section -->
                                        <div class="flex items-center space-x-2 2xl:space-x-4">
                                            <!-- Status Dot -->
                                            @isset($vehicle->movement_status)
                                                @if ($vehicle->movement_status == 1 && $vehicle->speed > 0)
                                                    <span class="bg-green-500 rounded-full size-2"></span>
                                                @elseif ($vehicle->movement_status == 0 && $vehicle->ignition_status == 1)
                                                    <span class="bg-yellow-500 rounded-full size-2"></span>
                                                @else
                                                    <span class="bg-red-500 rounded-full size-2"></span>
                                                @endif
                                            @endisset
                                            <!-- Content -->
                                            <div>
                                                <p
                                                    class="flex items-center gap-2 text-sm font-semibold text-gray-900 2xl:text-base dark:text-gray-200">
                                                    {{ $vehicle->license_plate }}
                                                </p>
                                                @if ($vehicle?->driver?->name)
                                                    <div
                                                        class="flex items-center mt-1 text-gray-600 2xl:space-x-1 dark:text-slate-300">
                                                        <span class="inline-flex items-center">
                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                class="size-3 2xl:size-4" fill="currentColor"
                                                                viewBox="0 0 24 24">
                                                                <path
                                                                    d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-3.31 0-6 2.69-6 6h12c0-3.31-2.69-6-6-6z">
                                                                </path>
                                                            </svg>
                                                        </span>
                                                        <span class="text-xs">{{ $vehicle->driver?->name }}</span>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        <!-- Right Section -->
                                        <p
                                            class="2xl:text-[10px] text-[8px] text-gray-500 dark:text-slate-400 text-end w-full shrink-0 lg:w-fit lg:shrink">
                                            {{ isset($vehicle->timestamp) ? parseFlexibleTimestamp($vehicle->timestamp)->diffForHumans() : '' }}
                                        </p>
                                    </div>
                                @endforeach

                            </div>
                        </div>
                    @endforeach

                    {{-- vehicle detail modal --}}


                </div>

            </div>

            <div x-show="showModal" x-cloak
                class="fixed bottom-0 md:top-0 right-0 z-[9999999] flex items-end overflow-y-scroll left-0 sm:left-auto md:max-w-[420px] md:my-auto max-h-fit">
                <div
                    class="relative h-fit min-h-40 p-5 overflow-y-scroll bg-white rounded-lg  dark:bg-slate-700 md:min-w-60 min-w-40 w-full max-w-lg mx-auto shadow-[-5px_-5px_5px_rgba(0,0,0,0.1)] md:my-auto max-h-[90vh]">
                    <button @click="showModal = false;clearMapElements();emptySelected();$wire.set('vehicleDetails',null)"
                        class="absolute top-3 right-3 dark:text-slate-400 text-slate-600">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>


                    <div>

                        <div class="flex flex-col gap-3 md:gap-5">
                            <div x-data="{ show: true }">
                                <h3 @click="show = !show"
                                    class="flex items-center gap-2 text-base font-semibold cursor-pointer md:text-xl dark:text-slate-100 text-slate-800">
                                    <svg width="12" height="13" class="transition-all duration-300"
                                        :class="show ? 'rotate-0' : 'rotate-180'" viewBox="0 0 12 13" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path d="M6 2.89999L0 9.29999H12L6 2.89999Z" fill="currentColor" />
                                    </svg>

                                    {{ $vehicleDetails['license_plate'] ?? '' }}
                                </h3>

                                <div x-show="show" x-cloak>

                                    <span class="mt-2 text-sm dark:text-slate-300">
                                        {{ $vehicleDetails['model'] ?? '' }}
                                    </span>



                                    @if (isset($vehicleDetails['driver']) && isset($vehicleDetails['driver']['name']))
                                        <div class="flex items-center mt-1 space-x-1 text-gray-600 dark:text-slate-300">
                                            <span class="inline-flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4"
                                                    fill="currentColor" viewBox="0 0 24 24">
                                                    <path
                                                        d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-3.31 0-6 2.69-6 6h12c0-3.31-2.69-6-6-6z">
                                                    </path>
                                                </svg>
                                            </span>
                                            <span class="text-xs">
                                                {{ $vehicleDetails['driver']['name'] ?? '' }}
                                            </span>
                                        </div>
                                    @endif

                                    <div x-data="{
                                        speed: {{ $vehicleDetails['speed'] ?? 0 }},
                                        maxSpeed: 200,
                                        getRotation() {
                                            const angle = -135 + (this.speed / this.maxSpeed) * 270;
                                            return Math.max(-135, Math.min(135, angle));
                                        },
                                        getSpeedColor() {
                                            return this.speed > 120 ? 'text-red-500' : (this.speed > 80 ? 'text-yellow-500' : 'text-primary');
                                        }
                                    }" x-on:speed-updated.window="speed = $event.detail">



                                        <div
                                            class="w-full p-4 mt-4 border shadow-lg dark:border-slate-700 bg-gray-50 dark:shadow-2xl dark:bg-slate-800 rounded-xl">


                                            <div class="grid grid-cols-3 gap-4">
                                                <!-- Daily Distance -->
                                                <div
                                                    class="p-2 transition-all duration-300 border rounded-lg shadow-sm md:p-3 bg-gray-50 dark:bg-slate-800 dark:border-slate-700">

                                                    <div class="flex items-baseline">
                                                        <span class="text-lg font-bold text-slate-800 dark:text-slate-200">
                                                            {{ number_format($vehicleDetails['daily_distance'] ?? 0, 1) }}
                                                        </span>
                                                        <span
                                                            class="ml-1 text-sm text-gray-500 dark:text-gray-400">km</span>
                                                    </div>
                                                    <div
                                                        class="md:mt-2 mt-1 md:text-[11px] text-[9px] text-gray-500 dark:text-gray-400">
                                                        @lang('translations.today_travel_distance')
                                                    </div>
                                                </div>
                                                <!-- Speed Meter -->
                                                <div class="relative w-24 h-24 mx-auto" wire:ignore>
                                                    <!-- Background Circle -->
                                                    <div
                                                        class="absolute inset-0 border-[12px] border-gray-200 rounded-full dark:border-gray-700/30">
                                                    </div>

                                                    <!-- Progress Circle -->
                                                    <div class="absolute inset-0 transition-all duration-300"
                                                        :style="`background: conic-gradient(var(--primary-color) ${(speed / maxSpeed) * 270}deg, transparent 0deg); transform: rotate(-135deg); border-radius: 50%;`">
                                                    </div>


                                                    <!-- Speed Display -->
                                                    <div
                                                        class="absolute inset-0 z-10 flex flex-col items-center justify-center">
                                                        <span class="text-2xl font-bold transition-colors duration-300"
                                                            :class="getSpeedColor()" x-text="speed"></span>
                                                        <span class="mt-1 text-xs font-medium text-gray-400">km/h</span>
                                                    </div>

                                                    <!-- Speed Indicator -->
                                                    <div class="absolute inset-0 transition-transform duration-300"
                                                        :style="`transform: rotate(${getRotation()}deg)`">
                                                        <div
                                                            class="absolute w-1 h-8 origin-bottom transform -translate-x-1/2 rounded-full shadow-lg bg-primary left-1/2 bottom-1/2">
                                                        </div>
                                                    </div>
                                                    <!-- Speed Markers -->
                                                    <template x-for="i in 19" :key="i">
                                                        <div class="absolute inset-0"
                                                            :style="`transform: rotate(${(i - 1) * 15 - 135}deg)`">
                                                            <div :class="(i - 1) % 3 === 0 ? 'h-3 w-1' : 'h-2 w-0.5'"
                                                                class="absolute top-0 transition-colors duration-300 transform -translate-x-1/2 left-1/2"
                                                                :class="(i - 1) * 15 <= (speed / maxSpeed) * 270 ?
                                                                    'bg-primary/60 dark:bg-primary/60' :
                                                                    'bg-gray-300 dark:bg-gray-600'">
                                                            </div>
                                                        </div>
                                                    </template>
                                                    <!-- Speed Labels -->
                                                    <div class="absolute inset-0">
                                                        <!-- 0 km/h -->
                                                        <span
                                                            class="absolute text-[8px] font-medium text-gray-500 -translate-x-1/2 -translate-y-1/2 dark:text-gray-400"
                                                            style="left: 15%; top: 76%">0</span>
                                                        <!-- 50 km/h -->
                                                        <span
                                                            class="absolute text-[8px] font-medium text-gray-500 -translate-x-1/2 -translate-y-1/2 dark:text-gray-400"
                                                            style="left: 8%; top: 30%">50</span>
                                                        <!-- 100 km/h -->
                                                        <span
                                                            class="absolute text-[8px] font-medium text-gray-500 -translate-x-1/2 -translate-y-1/2 dark:text-gray-400"
                                                            style="left: 50%; top: 5%">100</span>
                                                        <!-- 150 km/h -->
                                                        <span
                                                            class="absolute text-[8px] font-medium text-gray-500 -translate-x-1/2 -translate-y-1/2 dark:text-gray-400"
                                                            style="left: 90%; top: 30%">150</span>
                                                        <!-- 200 km/h -->
                                                        <span
                                                            class="absolute text-[8px] font-medium text-gray-500 -translate-x-1/2 -translate-y-1/2 dark:text-gray-400"
                                                            style="left: 85%; top: 75%">200</span>
                                                    </div>
                                                </div>

                                                <!-- Total Odometer -->
                                                <div
                                                    class="p-2 transition-all duration-300 border rounded-lg shadow-sm md:p-3 bg-gray-50 dark:bg-slate-800 dark:border-slate-700">

                                                    <div class="flex items-baseline">
                                                        <span class="text-lg font-bold text-slate-800 dark:text-slate-200">
                                                            {{ number_format($vehicleDetails['odometer'] ?? 0, 0) }}
                                                        </span>
                                                        <span
                                                            class="ml-1 text-sm text-gray-500 dark:text-gray-400">km</span>
                                                    </div>
                                                    <div
                                                        class="md:mt-2 mt-1 md:text-[11px] text-[9px] text-gray-500 dark:text-gray-400">
                                                        @lang('translations.total_distance_traveled')
                                                    </div>
                                                </div>
                                            </div>


                                            @if (isset($vehicleDetails['fuel_level_percentage']) && !empty($vehicleDetails['fuel_level_percentage']))
                                                <div x-data="{
                                                    fuelLevel: {{ $vehicleDetails['fuel_level_percentage'] ?? 0 }},
                                                    getFuelColor() {
                                                        return this.fuelLevel > 50 ? 'bg-green-500' : (this.fuelLevel > 20 ? 'bg-yellow-500' : 'bg-red-500');
                                                    },
                                                    getFuelTextColor() {
                                                        return this.fuelLevel > 50 ? 'text-green-600 dark:text-green-400' :
                                                            (this.fuelLevel > 20 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400');
                                                    },
                                                    getFuelMessage() {
                                                        if (this.fuelLevel > 50) return '@lang('translations.fuel_level_good')';
                                                        if (this.fuelLevel > 20) return '@lang('translations.fuel_level_warning')';
                                                        return '@lang('translations.fuel_level_critical')';
                                                    }
                                                }"
                                                    x-on:fuel-level-updated.window="fuelLevel = $event.detail"
                                                    class="flex flex-col gap-2 mt-3">
                                                    <div class="flex items-center justify-between">
                                                        <h4 class="text-slate-600 dark:text-slate-300">
                                                            @lang('translations.fuel_level')
                                                        </h4>
                                                        <p class="font-medium dark:text-slate-100"
                                                            x-text="fuelLevel + '%'">
                                                        </p>
                                                    </div>

                                                    <div
                                                        class="relative w-full h-4 overflow-hidden bg-gray-200 rounded-full dark:bg-slate-700">
                                                        <div class="absolute top-0 left-0 h-full transition-all duration-300 rounded-full"
                                                            :class="getFuelColor()" :style="`width: ${fuelLevel}%`">
                                                        </div>

                                                        <div class="absolute transition-all duration-300 transform -translate-y-1/2 -top-5"
                                                            :style="`left: calc(${fuelLevel}% - 12px)`">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6"
                                                                :class="fuelLevel > 50 ? 'text-green-600' : (fuelLevel > 20 ?
                                                                    'text-yellow-600' : 'text-red-600')"
                                                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    stroke-width="2"
                                                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                            </svg>
                                                        </div>
                                                    </div>

                                                    <div
                                                        class="flex justify-between px-1 text-xs text-slate-500 dark:text-slate-400">
                                                        <span>E</span>
                                                        <span>1/4</span>
                                                        <span>1/2</span>
                                                        <span>3/4</span>
                                                        <span>F</span>
                                                    </div>

                                                    <div class="text-center dark:text-slate-100">
                                                        <div class="mt-1 text-sm" :class="getFuelTextColor()"
                                                            x-text="getFuelMessage()"></div>
                                                    </div>
                                                </div>
                                            @endif

                                        </div>
                                    </div>

                                </div>


                            </div>

                            <div class="flex flex-wrap items-center justify-center w-full gap-3 mt-3">
                                @if (isset($vehicleDetails['indicators']['ignition']['available']) &&
                                        $vehicleDetails['indicators']['ignition']['available']
                                )
                                    <!-- Ignition Indicator -->
                                    <div x-data="{ tooltip: false }" @mouseenter="tooltip = true"
                                        @mouseleave="tooltip = false" class="relative">
                                        <div
                                            class="flex items-center px-3 py-2 bg-white rounded-lg shadow-sm dark:bg-slate-800">
                                            <x-icons.ignition :color="$vehicleDetails['indicators']['ignition']['value'] == 1
                                                ? '#22c55e'
                                                : '#f02424'" />
                                        </div>
                                        <div x-show="tooltip" x-transition:enter="transition ease-out duration-200"
                                            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                                            x-transition:leave="transition ease-in duration-150"
                                            x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                                            class="absolute z-10 px-3 py-1 mb-2 text-sm text-white bg-gray-900 rounded shadow-lg bottom-full whitespace-nowrap"
                                            style="left: 50%; transform: translateX(-50%);">
                                            @lang('translations.ignition_status'):
                                            {{ $vehicleDetails['indicators']['ignition']['value'] == 1 ? __('translations.on') : __('translations.off') }}
                                        </div>
                                    </div>
                                @endif

                                @if (isset($vehicleDetails['indicators']['parked']['available']) && $vehicleDetails['indicators']['parked']['available'])
                                    <!-- Parked Indicator -->
                                    @php
                                        $isParked =
                                            $vehicleDetails['indicators']['parked']['movement'] == 0 ||
                                            $vehicleDetails['indicators']['parked']['speed'] == 0;
                                    @endphp
                                    <div x-data="{ tooltip: false }" @mouseenter="tooltip = true"
                                        @mouseleave="tooltip = false" class="relative">
                                        <div
                                            class="flex items-center px-3 py-2 bg-white rounded-lg shadow-sm dark:bg-slate-800">
                                            @if (!$isParked)
                                                <x-icons.steering :color="$isParked ? '#f02424' : '#22c55e'" />
                                            @else
                                                <x-icons.parked :color="$isParked ? '#f02424' : '#22c55e'" />
                                            @endif
                                        </div>
                                        <div x-show="tooltip" x-transition:enter="transition ease-out duration-200"
                                            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                                            x-transition:leave="transition ease-in duration-150"
                                            x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                                            class="absolute z-10 px-3 py-1 mb-2 text-sm text-white bg-gray-900 rounded shadow-lg bottom-full whitespace-nowrap"
                                            style="left: 50%; transform: translateX(-50%);">
                                            @lang('translations.movement_status'):
                                            {{ $isParked ? __('translations.parked') : __('translations.moving') }}
                                        </div>
                                    </div>
                                @endif

                                {{-- @if (isset($vehicleDetails['indicators']['battery']['available']) && $vehicleDetails['indicators']['battery']['available'])
                                    <!-- Battery Indicator -->
                                    @php
                                        $batteryVoltage = $vehicleDetails['indicators']['battery']['voltage'] ?? 0;
                                        $batteryLevel = 0;
                                        $batteryColor = '#ef4444'; // Red for low battery

                                        if ($batteryVoltage > 0) {
                                            // Typical car battery is 12-14V
                                            if ($batteryVoltage >= 13) {
                                                $batteryLevel = 100;
                                                $batteryColor = '#22c55e'; // Green for good
                                            } elseif ($batteryVoltage >= 12) {
                                                $batteryLevel = 75;
                                                $batteryColor = '#22c55e'; // Green for good
                                            } elseif ($batteryVoltage >= 11) {
                                                $batteryLevel = 50;
                                                $batteryColor = '#f02424'; // Yellow for medium
                                            } elseif ($batteryVoltage >= 10) {
                                                $batteryLevel = 25;
                                                $batteryColor = '#ef4444'; // Red for low
                                            }
                                        }
                                    @endphp
                                    <div x-data="{ tooltip: false }" @mouseenter="tooltip = true"
                                        @mouseleave="tooltip = false" class="relative">
                                        <div
                                            class="flex items-center px-3 py-2 bg-white rounded-lg shadow-sm dark:bg-slate-800">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                class="flex-shrink-0 w-auto h-6" fill="{{ $batteryColor }}">
                                                <path
                                                    d="M15.67 4H14V2h-4v2H8.33C7.6 4 7 4.6 7 5.33v15.33C7 21.4 7.6 22 8.33 22h7.33c.74 0 1.34-.6 1.34-1.33V5.33C17 4.6 16.4 4 15.67 4z" />
                                                @if ($batteryLevel <= 25)
                                                    <path d="M7 10.5h10v3H7z" fill="#fff" />
                                                @elseif($batteryLevel <= 50)
                                                    <path d="M7 9h10v6H7z" fill="#fff" />
                                                @elseif($batteryLevel <= 75)
                                                    <path d="M7 7.5h10v9H7z" fill="#fff" />
                                                @endif
                                            </svg>
                                        </div>
                                        <div x-show="tooltip" x-transition:enter="transition ease-out duration-200"
                                            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                                            x-transition:leave="transition ease-in duration-150"
                                            x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                                            class="absolute z-10 px-3 py-1 mb-2 text-sm text-white bg-gray-900 rounded shadow-lg bottom-full whitespace-nowrap"
                                            style="left: 50%; transform: translateX(-50%);">
                                            @lang('translations.battery_status'): {{ number_format($batteryVoltage, 1) }}V
                                        </div>
                                    </div>
                                @endif --}}

                                @if (isset($vehicleDetails['indicators']['signal']['available']) && $vehicleDetails['indicators']['signal']['available'])
                                    <!-- Signal Strength Indicator -->
                                    @php
                                        $signalValue = $vehicleDetails['indicators']['signal']['value'] ?? 0;
                                        $signalValue = min(5, max(0, (int) $signalValue)); // Ensure value is between 0-5

                                        // Define colors based on signal strength
                                        if ($signalValue >= 4) {
                                            $signalColor = 'bg-emerald-500'; // Green for excellent
                                        } elseif ($signalValue >= 3) {
                                            $signalColor = 'bg-green-500'; // Green for good
                                        } elseif ($signalValue >= 2) {
                                            $signalColor = 'bg-amber-500'; // Yellow for medium
                                        } else {
                                            $signalColor = 'bg-red-500'; // Red for low
                                        }
                                    @endphp
                                    <div x-data="{ tooltip: false }" @mouseenter="tooltip = true"
                                        @mouseleave="tooltip = false" class="relative">
                                        <div
                                            class="flex items-center gap-1 px-3 py-2 bg-white rounded-lg shadow-sm dark:bg-slate-800">
                                            <div class="flex items-end h-6 gap-[2px]">
                                                <!-- Signal Bar 1 (Always visible if signal > 0) -->
                                                <div
                                                    class="w-1 h-1 rounded-sm {{ $signalValue >= 1 ? $signalColor : 'bg-gray-300 dark:bg-gray-600' }}">
                                                </div>

                                                <!-- Signal Bar 2 -->
                                                <div
                                                    class="w-1 h-2 rounded-sm {{ $signalValue >= 2 ? $signalColor : 'bg-gray-300 dark:bg-gray-600' }}">
                                                </div>

                                                <!-- Signal Bar 3 -->
                                                <div
                                                    class="w-1 h-3 rounded-sm {{ $signalValue >= 3 ? $signalColor : 'bg-gray-300 dark:bg-gray-600' }}">
                                                </div>

                                                <!-- Signal Bar 4 -->
                                                <div
                                                    class="w-1 h-4 rounded-sm {{ $signalValue >= 4 ? $signalColor : 'bg-gray-300 dark:bg-gray-600' }}">
                                                </div>

                                                <!-- Signal Bar 5 -->
                                                <div
                                                    class="w-1 h-5 rounded-sm {{ $signalValue >= 5 ? $signalColor : 'bg-gray-300 dark:bg-gray-600' }}">
                                                </div>
                                            </div>
                                        </div>
                                        <div x-show="tooltip" x-transition:enter="transition ease-out duration-200"
                                            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                                            x-transition:leave="transition ease-in duration-150"
                                            x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                                            class="absolute z-10 px-3 py-1 mb-2 text-sm text-white bg-gray-900 rounded shadow-lg bottom-full whitespace-nowrap"
                                            style="left: 50%; transform: translateX(-50%);">
                                            @lang('translations.signal_strength'): {{ $signalValue }}/5
                                        </div>
                                    </div>
                                @endif

                                @if (!empty($vehicleDetails['geofence_status']))
                                    <!-- Geofence Status Indicator -->
                                    @php
                                        $insideAnyGeofence = false;
                                        foreach ($vehicleDetails['geofence_status'] as $status) {
                                            if ($status['inside']) {
                                                $insideAnyGeofence = true;
                                                break;
                                            }
                                        }
                                        $geofenceColor = $insideAnyGeofence ? '#22c55e' : '#ef4444';
                                    @endphp
                                    <div x-data="{ tooltip: false }" @mouseenter="tooltip = true"
                                        @mouseleave="tooltip = false" class="relative">
                                        <div
                                            class="flex items-center px-3 py-2 bg-white rounded-lg shadow-sm dark:bg-slate-800">
                                            <x-icons.geofence :color="$geofenceColor" />
                                        </div>
                                        <div x-show="tooltip" x-transition:enter="transition ease-out duration-200"
                                            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                                            x-transition:leave="transition ease-in duration-150"
                                            x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                                            class="absolute z-10 px-3 py-1 mb-2 text-sm text-white bg-gray-900 rounded shadow-lg bottom-full whitespace-nowrap"
                                            style="left: 50%; transform: translateX(-50%);">
                                            @lang('translations.geofence_status'):
                                            {{ $insideAnyGeofence ? __('translations.inside') : __('translations.outside') }}
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <div>
                                @can('fleet_view_history_mode')
                                    <div class="flex items-center justify-center mt-3">
                                        <span class="text-sm font-medium dark:text-slate-300">
                                            {{ __('translations.history_mode') }}
                                        </span>
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="liveModeToggle" wire:model.live="liveMode" checked>
                                            <div class="toggle-switch-background">
                                                <div class="toggle-switch-handle"></div>
                                            </div>
                                        </label>
                                        <span class="text-sm font-medium dark:text-slate-300">
                                            {{ __('translations.live_mode') }}
                                        </span>
                                    </div>
                                    <div class="flex items-center justify-center w-full my-3">
                                        {{-- history mode spinner --}}
                                        <div wire:loading wire:target="liveMode"
                                            class="w-8 h-8 border-b-2 rounded-full animate-spin border-primary"></div>

                                        <div wire:loading wire:target="selectedDate"
                                            class="w-8 h-8 border-b-2 rounded-full animate-spin border-primary"></div>
                                    </div>

                                    @if (!$liveMode)
                                        <div wire:ignore id="historyControls" class="mt-3">
                                            <div class="flex flex-col items-center justify-center gap-4 md:flex-nowrap">
                                                <select wire:model.live="selectedDate" id="dateDropdown"
                                                    class="peer py-2 px-3 block w-fit min-w-36 border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:bg-slate-700 dark:text-white dark:border-slate-500">
                                                    <option value=""></option>
                                                    @if (!empty($dates))
                                                        @foreach ($dates as $date)
                                                            <option wire:key="date-{{ $date }}"
                                                                value="{{ $date }}">
                                                                {{ $date }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>

                                                <div class="flex items-center gap-2">
                                                    <div id="seek-container">
                                                        <span id="currentTime" class="md:whitespace-nowrap">

                                                        </span>
                                                        <input type="range" id="seekbar" min="0" max="100"
                                                            value="0" oninput="seekToPosition(this.value)">
                                                    </div>

                                                    <button onclick="playPause()"
                                                        class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white transition duration-300 ease-in-out transform rounded-lg shadow-lg bg-primary hover:bg-primaryDark hover:scale-105">

                                                        <svg id="playIcon" xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" fill="currentColor" class="size-5 me-2">
                                                            <path fill-rule="evenodd"
                                                                d="M4.5 5.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L7.28 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653Z"
                                                                clip-rule="evenodd" />
                                                        </svg>

                                                        <svg id="pauseIcon" xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" fill="currentColor"
                                                            class="hidden size-6 me-2">
                                                            <path fill-rule="evenodd"
                                                                d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12ZM9 8.25a.75.75 0 0 0-.75.75v6c0 .414.336.75.75.75h.75a.75.75 0 0 0 .75-.75V9a.75.75 0 0 0-.75-.75H9Zm5.25 0a.75.75 0 0 0-.75.75v6c0 .414.336.75.75.75H15a.75.75 0 0 0 .75-.75V9a.75.75 0 0 0-.75-.75h-.75Z"
                                                                clip-rule="evenodd" />
                                                        </svg>

                                                        <span id="buttonText">{{ __('translations.play') }}<span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                @endcan

                            </div>


                        </div>

                        <div x-data="{ show: false }">

                            <h2 @click="show = !show"
                                class="flex items-center gap-2 mt-4 mb-4 font-semibold text-gray-800 cursor-pointer md:text-xl md:mt-8 dark:text-slate-100">
                                <svg width="12" height="13" class="transition-all duration-300"
                                    :class="show ? 'rotate-0' : 'rotate-180'" viewBox="0 0 12 13" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6 2.89999L0 9.29999H12L6 2.89999Z" fill="currentColor" />
                                </svg>
                                @lang('translations.show_details')

                            </h2>


                            <div x-show="show" x-cloak class="grid grid-cols-1 mt-5 text-sm gap-x-2 gap-y-3">
                                <div wire:ignore class="flex flex-col gap-2">

                                    <h4 class="text-slate-600 dark:text-slate-300">
                                        @lang('translations.address')

                                    </h4>

                                    <p id="address" class="font-medium dark:text-slate-100 text-wrap ">
                                        {{ $vehicleDetails['address'] ?? '' }}

                                    </p>
                                </div>

                                @if (isset($vehicleDetails['stops']))
                                    <div x-data="{ show: $wire.entangle('showStops').live }"
                                        class="p-4 mt-4 bg-white rounded-lg shadow-sm dark:bg-slate-800">
                                        <h3 x-on:click="show = !show"
                                            class="flex items-center gap-2 mb-4 text-lg font-medium cursor-pointer text-slate-800 dark:text-slate-200">
                                            {{-- up icon --}}
                                            <svg width="12" height="13" class="transition-all duration-300"
                                                :class="show ? 'rotate-0' : 'rotate-180'" viewBox="0 0 12 13"
                                                fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M6 2.89999L0 9.29999H12L6 2.89999Z" fill="currentColor" />
                                            </svg>
                                            @lang('translations.today_stops')
                                        </h3>

                                        <div class="p-3 mb-4 rounded-md bg-slate-50 dark:bg-slate-700">
                                            <div class="grid grid-cols-2 gap-2">
                                                <div class="flex items-center gap-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="size-5 text-primary"
                                                        viewBox="0 0 24 24" fill="currentColor">
                                                        <path
                                                            d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
                                                    </svg>
                                                    <p class="text-sm text-slate-600 dark:text-slate-300">
                                                        @lang('translations.stops'): <span
                                                            class="font-medium">{{ $vehicleDetails['total_stops'] }}</span>
                                                    </p>
                                                </div>
                                                <div class="flex items-center gap-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                        fill="currentColor" class="size-5 text-primary">
                                                        <path fill-rule="evenodd"
                                                            d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 6a.75.75 0 0 0-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 0 0 0-1.5h-3.75V6Z"
                                                            clip-rule="evenodd" />
                                                    </svg>

                                                    <p class="text-sm text-slate-600 dark:text-slate-300">
                                                        @lang('translations.duration'): <span
                                                            class="font-medium">{{ floor($vehicleDetails['total_stop_time'] / 60) }}h
                                                            {{ $vehicleDetails['total_stop_time'] % 60 }}m</span>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        <div x-show="show" class="space-y-2">
                                            @foreach ($vehicleDetails['stops'] as $index => $stop)
                                                <div wire:key="stop-{{ $index }}"
                                                    class="p-4 transition-all duration-200 border rounded-lg shadow-sm border-slate-200 hover:border-primary/30 hover:shadow-md dark:border-slate-600 dark:hover:border-primary/30 dark:hover:bg-slate-700/50">
                                                    <div class="relative flex gap-3">

                                                        <div
                                                            class="absolute flex flex-col items-center gap-2 -top-1 left-2 bottom-4">
                                                            <div class="w-0.5 h-full dark:bg-slate-600/50 bg-slate-200">
                                                            </div>
                                                            <div
                                                                class="absolute flex flex-col items-center justify-center flex-shrink-0 w-full h-full text-center">
                                                                <span
                                                                    class="px-2 py-1 text-[10px] leading-3 font-medium rounded-full text-slate-600 bg-slate-100 dark:bg-slate-700 dark:text-slate-300">
                                                                    @if (isset($stop['trip_distance']) && $stop['trip_distance'])
                                                                        {{ $stop['trip_distance'] ?? '' }}
                                                                    @endif
                                                                </span>
                                                            </div>
                                                        </div>

                                                        <!-- Location marker and address -->
                                                        <div class="flex gap-3 ps-10">
                                                            <div class="flex flex-col gap-1">
                                                                <p
                                                                    class="text-sm font-medium text-slate-700 dark:text-slate-200">
                                                                    {{ $stop['location']['address'] ?? '' }}
                                                                </p>

                                                                <!-- Time and duration info -->
                                                                <div class="flex items-center gap-3 mt-2">
                                                                    <div
                                                                        class="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                                                                        <svg xmlns="http://www.w3.org/2000/svg"
                                                                            class="size-4" fill="none"
                                                                            viewBox="0 0 24 24" stroke="currentColor">
                                                                            <path stroke-linecap="round"
                                                                                stroke-linejoin="round" stroke-width="2"
                                                                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                        </svg>
                                                                        <span>
                                                                            {{ parseFlexibleTimestamp($stop['start_time'])->format('H:i') }}
                                                                            -
                                                                            {!! $stop['end_time'] === 'Ongoing'
                                                                                ? '<span class="px-2 py-1 text-xs font-medium rounded-full text-primary bg-primary/10">' .
                                                                                    __('translations.ongoing') .
                                                                                    '</span>'
                                                                                : parseFlexibleTimestamp($stop['end_time'])->format('H:i') !!}
                                                                        </span>
                                                                    </div>
                                                                    <span
                                                                        class="px-3 py-1 text-xs font-medium rounded-full ms-auto text-primary bg-primary/10 dark:bg-primary/5">
                                                                        {{ $stop['duration'] }}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif



                                @if (isset($vehicleDetails['daily_fuel']) && $vehicleDetails['daily_fuel'] > 0)
                                    <div class="flex justify-between gap-2">

                                        <h4 class="text-slate-600 dark:text-slate-300">
                                            @lang('translations.today_fuel_consumption')

                                        </h4>

                                        <p class="font-medium dark:text-slate-100 text-end">
                                            {{ $vehicleDetails['daily_fuel'] ?? '' }} l
                                        </p>
                                    </div>
                                @endif



                                @if (isset($vehicleDetails['fuel_level']) && $vehicleDetails['fuel_level'] > 0)
                                    <div class="flex justify-between gap-2">

                                        <h4 class="text-slate-600 dark:text-slate-300">
                                            @lang('translations.fuel_level')

                                        </h4>

                                        <p class="font-medium dark:text-slate-100 text-end">
                                            {{ $vehicleDetails['fuel_level'] ?? '' }} l
                                        </p>
                                    </div>
                                @endif

                                @if (isset($vehicleDetails['average_fuel']) && $vehicleDetails['average_fuel'] > 0)
                                    <div class="flex justify-between gap-2">

                                        <h4 class="text-slate-600 dark:text-slate-300">
                                            @lang('translations.average_consumption')

                                        </h4>

                                        <p class="font-medium dark:text-slate-100 text-end">
                                            {{ $vehicleDetails['average_fuel'] ?? '' }} l/100km
                                        </p>
                                    </div>
                                @endif


                                <div class="flex justify-between gap-2">

                                    {{-- @php
                                        $distance = $vehicleDetails['odometer'] ?? 0;
                                        $vehicleType = $vehicleDetails['type'] ?? null;
                                        $mileage = 0;

                                        // Define mileage values based on vehicle type
                                        switch ($vehicleType) {
                                            case 'truck':
                                                $mileage = 3; // 3 km/l for heavy vehicles
                                                break;
                                            case 'bus':
                                                $mileage = 3; // 3 km/l for heavy vehicles
                                                break;
                                            case 'tractor':
                                                $mileage = 3; // 3 km/l for heavy vehicles
                                                break;

                                            case 'car':
                                                $mileage = 11; // 3 km/l for heavy vehicles
                                                break;
                                            case 'bike':
                                                $mileage = 11; // 3 km/l for heavy vehicles
                                                break;
                                            case 'scooter':
                                                $mileage = 11; // 3 km/l for heavy vehicles
                                                break;
                                            case 'van':
                                                $mileage = 11; // 3 km/l for heavy vehicles
                                                break;
                                            case 'ambulance':
                                                $mileage = 11; // 11 km/l for light vehicles
                                                break;

                                            case 'cycle':
                                                $mileage = 0; // No fuel required or undefined mileage
                                                break;
                                            case 'boat':
                                                $mileage = 0; // No fuel required or undefined mileage
                                                break;
                                            case 'other':
                                                $mileage = 0; // No fuel required or undefined mileage
                                                break;

                                            default:
                                                $mileage = 0; // Fallback for undefined vehicle types
                                                break;
                                        }

                                        // Calculate fuel consumption
                                        $fuelConsumption = $mileage > 0 ? round($distance / $mileage, 2) : 0;
                                    @endphp

                                    @if ($fuelConsumption)
                                        <h4 class="text-slate-600 dark:text-slate-300">
                                            @lang('translations.fuel_consumption')

                                        </h4>
                                        <p class="font-medium dark:text-slate-100 text-end">
                                            {{ $fuelConsumption }} @lang('translations.liters')
                                        </p>
                                    @endif --}}

                                </div>

                                @if (isset($vehicleDetails['fuel_used']) && $vehicleDetails['fuel_used'] > 0)
                                    <div class="flex justify-between gap-2">

                                        <h4 class="text-slate-600 dark:text-slate-300">
                                            @lang('translations.total_fuel_consumption')

                                        </h4>

                                        <p class="font-medium dark:text-slate-100 text-end">
                                            {{ $vehicleDetails['fuel_used'] ?? '' }} l
                                        </p>
                                    </div>
                                @endif



                                @if (isset($vehicleDetails['current_route']) && !empty($vehicleDetails['current_route']))
                                    <div class="flex justify-between gap-2">

                                        <h4 class="text-slate-600 dark:text-slate-300">
                                            @lang('translations.departure_time')
                                        </h4>

                                        <p class="font-medium dark:text-slate-100 text-end">
                                            {{ $vehicleDetails['current_route']['departure_time'] ?? '' }}
                                        </p>
                                    </div>

                                    <div class="flex justify-between gap-2">

                                        <h4 class="text-slate-600 dark:text-slate-300">
                                            @lang('translations.arrival_time')

                                        </h4>

                                        <p class="font-medium dark:text-slate-100 text-end">
                                            @php
                                                $departureTime =
                                                    $vehicleDetails['current_route']['departure_time'] ?? null;
                                                $duration = $vehicleDetails['current_route']['duration'] ?? null;

                                                if ($departureTime && $duration) {
                                                    // Parse the departure time
                                                    $departure = \Carbon\Carbon::parse($departureTime);

                                                    // Ensure duration is a number
                                                    $durationInMinutes = (int) $duration;

                                                    // Add the duration to calculate arrival time
                                                    $arrival = $departure->addMinutes($durationInMinutes);

                                                    // Display the arrival time in HH:mm format
                                                    echo $arrival->format('H:i');
                                                } else {
                                                    echo 'N/A'; // Fallback if data is missing
                                                }
                                            @endphp

                                        </p>

                                    </div>

                                    <div class="flex justify-between gap-2">

                                        <h4 class="text-slate-600 dark:text-slate-300">
                                            @lang('translations.distance_traveled')
                                        </h4>

                                        <p class="font-medium dark:text-slate-100 text-end">
                                            {{ isset($vehicleDetails['current_route']['distance']) ? $vehicleDetails['current_route']['distance'] . ' km' : '' }}
                                        </p>
                                    </div>

                                    <div class="flex justify-between gap-2">

                                        <h4 class="text-slate-600 dark:text-slate-300">
                                            @lang('translations.trip_duration')


                                        </h4>

                                        <p class="font-medium dark:text-slate-100 text-end">
                                            @if (isset($vehicleDetails['current_route']['duration']))
                                                @formatDuration($vehicleDetails['current_route']['duration'] ?? 0)
                                            @endif
                                        </p>
                                    </div>
                                    <div class="flex justify-between gap-2">


                                        <h4 class="text-slate-600 dark:text-slate-300">
                                            @lang('translations.route_fuel_consumption')

                                        </h4>

                                        <p class="font-medium dark:text-slate-100 text-end">
                                            @php
                                                $distance = $vehicleDetails['current_route']['distance'] ?? 0;
                                                $vehicleType = $vehicleDetails['type'] ?? null;
                                                $mileage = 0;

                                                // Define mileage values based on vehicle type
                                                switch ($vehicleType) {
                                                    case 'truck':
                                                        $mileage = 3; // 3 km/l for heavy vehicles
                                                        break;
                                                    case 'bus':
                                                        $mileage = 3; // 3 km/l for heavy vehicles
                                                        break;
                                                    case 'tractor':
                                                        $mileage = 3; // 3 km/l for heavy vehicles
                                                        break;

                                                    case 'car':
                                                        $mileage = 11; // 3 km/l for heavy vehicles
                                                        break;
                                                    case 'bike':
                                                        $mileage = 11; // 3 km/l for heavy vehicles
                                                        break;
                                                    case 'scooter':
                                                        $mileage = 11; // 3 km/l for heavy vehicles
                                                        break;
                                                    case 'van':
                                                        $mileage = 11; // 3 km/l for heavy vehicles
                                                        break;
                                                    case 'ambulance':
                                                        $mileage = 11; // 11 km/l for light vehicles
                                                        break;

                                                    case 'cycle':
                                                        $mileage = 0; // No fuel required or undefined mileage
                                                        break;
                                                    case 'boat':
                                                        $mileage = 0; // No fuel required or undefined mileage
                                                        break;
                                                    case 'other':
                                                        $mileage = 0; // No fuel required or undefined mileage
                                                        break;

                                                    default:
                                                        $mileage = 0; // Fallback for undefined vehicle types
                                                        break;
                                                }

                                                // Calculate fuel consumption
                                                $fuelConsumption =
                                                    $mileage > 0 ? round($distance / $mileage, 2) : 'N/A';
                                            @endphp

                                            {{ $fuelConsumption }} @lang('translations.liters')
                                        </p>
                                    </div>
                                @endif


                            </div>

                        </div>


                        {{-- @if (!isset($can_lock_unlock))
                            <div x-data="{ show: false }" class="mt-8 space-y-4">

                                <h3 @click="show = !show"
                                    class="flex items-center gap-2 text-base font-semibold cursor-pointer md:text-xl dark:text-slate-100 text-slate-800">
                                    <svg width="12" height="13" class="transition-all duration-300"
                                        :class="show ? 'rotate-0' : 'rotate-180'" viewBox="0 0 12 13" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path d="M6 2.89999L0 9.29999H12L6 2.89999Z" fill="currentColor" />
                                    </svg>

                                    @lang('translations.engine_control')

                                </h3>

                                <div x-show="show" class="p-4 bg-gray-100 rounded-lg dark:bg-slate-800">
                                    <!-- Current Engine Status -->
                                    <div class="flex items-center gap-3 mb-4">
                                        <div class="flex items-center gap-2">
                                            <span class="rounded-full size-3"
                                                :class="{{ 'true' }} ? 'bg-red-500' : 'bg-green-500'">
                                            </span>
                                            <span class="text-sm dark:text-slate-200">
                                                Engine Status: <span
                                                    class="font-semibold">{{ 'true' ? 'Immobilized' : 'Active' }}</span>
                                            </span>
                                        </div>
                                        @if ('true')
                                            <svg xmlns="http://www.w3.org/2000/svg" class="text-red-500 size-5"
                                                viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                                            </svg>
                                        @endif
                                    </div>

                                    <!-- Engine Control Buttons -->
                                    <div class="flex gap-3">
                                        <button wire:click="immobilizeEngine"
                                            class="flex items-center px-4 py-2 text-white transition-all duration-300 bg-red-500 rounded-md hover:bg-red-600">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="size-5 me-2"
                                                viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                                            </svg>
                                            Immobilize Engine
                                        </button>

                                        <button wire:click="activateEngine"
                                            class="flex items-center px-4 py-2 text-white transition-all duration-300 bg-green-500 rounded-md hover:bg-green-600">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="size-5 me-2"
                                                viewBox="0 0 24 24" fill="currentColor">
                                                <path
                                                    d="M21.721 12.752a9.711 9.711 0 00-.945-5.003 12.754 12.754 0 01-4.339 2.708 18.991 18.991 0 01-.214 4.772 17.165 17.165 0 005.498-2.477zM14.634 15.55a17.324 17.324 0 00.332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 00.332 4.647 17.385 17.385 0 005.268 0zM9.772 17.119a18.963 18.963 0 004.456 0A17.182 17.182 0 0112 21.724a17.18 17.18 0 01-2.228-4.605zM7.777 15.23a18.87 18.87 0 01-.214-4.774 12.753 12.753 0 01-4.34-2.708 9.711 9.711 0 00-.944 5.004 17.165 17.165 0 005.498 2.477zM21.356 14.752a9.765 9.765 0 01-7.478 6.817 18.64 18.64 0 001.988-4.718 18.627 18.627 0 005.49-2.098zM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 001.988 4.718 9.765 9.765 0 01-7.478-6.816zM13.878 2.43a9.755 9.755 0 016.116 3.986 11.267 11.267 0 01-3.746 2.504 18.63 18.63 0 00-2.37-6.49zM12 2.276a17.152 17.152 0 012.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0112 2.276zM10.122 2.43a18.629 18.629 0 00-2.37 6.49 11.266 11.266 0 01-3.746-2.504 9.754 9.754 0 016.116-3.985z" />
                                            </svg>
                                            Activate Engine
                                        </button>
                                    </div>

                                    <!-- Confirmation Modal -->
                                    <div x-data="{ showModal: false, action: '' }" x-cloak>
                                        <template x-teleport="body">
                                            <div x-show="showModal"
                                                class="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
                                                <div
                                                    class="w-full max-w-md p-6 bg-white rounded-lg shadow-xl dark:bg-slate-800">
                                                    <h3 class="mb-4 text-lg font-medium dark:text-slate-200"
                                                        x-text="action === 'immobilize' ? 'Confirm Engine Immobilization' : 'Confirm Engine Activation'">
                                                    </h3>

                                                    <p class="mb-4 text-sm text-gray-600 dark:text-slate-300">
                                                        Are you sure you want to <span
                                                            x-text="action === 'immobilize' ? 'immobilize' : 'activate'"></span>
                                                        the engine? This action requires PIN verification.
                                                    </p>

                                                    <div class="flex justify-end gap-3">
                                                        <button @click="showModal = false"
                                                            class="px-4 py-2 text-sm text-gray-600 transition-all duration-300 border rounded-md hover:bg-gray-100 dark:text-slate-300 dark:border-slate-600 dark:hover:bg-slate-700">
                                                            Cancel
                                                        </button>
                                                        <button @click="showModal = false"
                                                            class="px-4 py-2 text-sm text-white transition-all duration-300 rounded-md"
                                                            :class="action === 'immobilize' ?
                                                                'bg-red-500 hover:bg-red-600' :
                                                                'bg-green-500 hover:bg-green-600'">
                                                            Confirm
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        @endif --}}


                        @if (isset($vehicleDetails['current_route']) && !empty($vehicleDetails['current_route']))
                            <div x-data="{ show: false }">

                                <h2 @click="show = !show"
                                    class="flex items-center gap-2 mt-4 mb-4 font-semibold text-gray-800 cursor-pointer md:text-xl md:mt-8 dark:text-slate-100">
                                    <svg width="12" height="13" class="transition-all duration-300"
                                        :class="show ? 'rotate-0' : 'rotate-180'" viewBox="0 0 12 13" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path d="M6 2.89999L0 9.29999H12L6 2.89999Z" fill="currentColor" />
                                    </svg>
                                    @lang('translations.trip_timeline')

                                </h2>

                                <div x-show="show" x-cloak class="grid grid-cols-2 text-sm gap-x-2 gap-y-3">
                                    <h4 class="text-slate-600 dark:text-slate-300">
                                        @lang('translations.starting_point')
                                    </h4>

                                    <p class="font-medium max-w-44 ms-auto dark:text-slate-100 text-end">
                                        {{ $vehicleDetails['current_route']['start_point'] ?? '' }}
                                    </p>

                                    <h4 class="text-slate-600 dark:text-slate-300">
                                        @lang('translations.arrival_point')
                                    </h4>

                                    <p class="font-medium dark:text-slate-100 text-end max-w-44 ms-auto">
                                        {{ $vehicleDetails['current_route']['end_point'] ?? '' }}
                                    </p>
                                    <h4 class="text-slate-600 dark:text-slate-300">
                                        @lang('translations.travel_distance')
                                    </h4>

                                    <p class="font-medium dark:text-slate-100 text-end">
                                        {{ isset($vehicleDetails['current_route']['distance']) ? $vehicleDetails['current_route']['distance'] . ' km' : '' }}
                                    </p>
                                    <h4 class="text-slate-600 dark:text-slate-300">
                                        @lang('translations.trip_duration')
                                    </h4>

                                    <p class="font-medium dark:text-slate-100 text-end">
                                        @if (isset($vehicleDetails['current_route']['duration']))
                                            @formatDuration($vehicleDetails['current_route']['duration'] ?? 0)
                                        @endif
                                    </p>
                                </div>
                                <div x-show="show" x-cloak class="mt-5">
                                    <div class="flex items-start space-x-4">
                                        <p class="w-16 text-sm text-gray-600 dark:text-slate-300">
                                            {{ isset($vehicleDetails['current_route']['start_point_completed_at']) && !empty($vehicleDetails['current_route']['start_point_completed_at']) ? \Carbon\Carbon::parse($vehicleDetails['current_route']['start_point_completed_at'])->format('H:i') : '' }}
                                        </p>
                                        <div>
                                            <div
                                                class="w-4 h-4 border-2  @if (isset($vehicleDetails['current_route']['start_point_status']) &&
                                                        $vehicleDetails['current_route']['start_point_status'] == 'pending') border-green-500
                                                    @else
                                                    bg-green-500 @endif rounded-full">
                                            </div>
                                            <div class="w-0.5 h-16 bg-[#E3E3E3] mx-auto"></div>
                                        </div>
                                        <div>

                                            <p style="max-width: 13rem"
                                                class="font-semibold text-gray-900 text-wrap ms-auto dark:text-slate-100">
                                                {{ $vehicleDetails['current_route']['start_point'] ?? '' }}</p>
                                        </div>
                                    </div>
                                    {{-- stops --}}

                                    @php
                                        $stops = isset($vehicleDetails['current_route']['stops'])
                                            ? $vehicleDetails['current_route']['stops']
                                            : [];
                                    @endphp
                                    @foreach ($stops as $index => $stop)
                                        <div wire:key="route-stop-{{ $stop['id'] ?? $index }}"
                                            class="flex items-start space-x-4">
                                            <p class="w-16 text-sm text-gray-600 dark:text-slate-300">
                                                {{ isset($stop['completed_at']) && !empty($stop['completed_at']) ? \Carbon\Carbon::parse($stop['completed_at'])->format('H:i') : '' }}
                                            </p>

                                            <div>
                                                <div
                                                    class="w-4 h-4 border-2  @if ($stop['status'] == 'pending') border-green-500
                                                    @else
                                                    bg-green-500 @endif rounded-full">
                                                </div>
                                                <div class="w-0.5 h-16 bg-[#E3E3E3] mx-auto"></div>
                                            </div>
                                            <div>
                                                <p style="max-width: 13rem"
                                                    class="font-semibold text-gray-900 text-wrap dark:text-slate-100">
                                                    {{ $stop['stop_point'] ?? '' }}</p>
                                                {{-- <p class="text-sm text-gray-500 dark:text-slate-200">
                                                    @lang('translations.stopped')
                                                </p> --}}
                                            </div>
                                        </div>
                                    @endforeach

                                    <div class="flex items-start space-x-4">
                                        <p class="w-16 text-sm text-gray-600 dark:text-slate-300">
                                            {{ isset($vehicleDetails['current_route']['end_point_completed_at']) && !empty($vehicleDetails['current_route']['end_point_completed_at']) ? \Carbon\Carbon::parse($vehicleDetails['current_route']['end_point_completed_at'])->format('H:i') : '' }}
                                        </p>

                                        <div>
                                            <div
                                                class="w-4 h-4 border-2
                                                @if (isset($vehicleDetails['current_route']['end_point_status']) &&
                                                        $vehicleDetails['current_route']['end_point_status'] == 'pending') border-green-500
                                                    @else
                                                    bg-green-500 @endif rounded-full">
                                            </div>
                                        </div>
                                        <div>
                                            <p style="max-width: 13rem"
                                                class="font-semibold text-gray-900 ms-auto dark:text-slate-100">
                                                {{ $vehicleDetails['current_route']['end_point'] ?? '' }}</p>
                                            {{-- <p class="text-sm text-gray-500 dark:text-slate-200">@lang('translations.parked')
                                            </p> --}}
                                        </div>
                                    </div>
                                </div>

                            </div>
                        @endif

                    </div>
                </div>
            </div>
        </div>


        {{-- leaflet map --}}
        <div wire:ignore class="w-full h-full">

            <!-- Leaflet CSS -->


            <div id="map" class="md:min-h-[400px] min-h-screen h-full w-full z-[1]"></div>


            @push('scripts')
                <script src="{{ asset('assets/js/markerclusterer.js') }}"></script>


                <script>
                    let map;
                    let markers = [];
                    let vehicleData = [];
                    let selectedVehicleImei;
                    let seletedVehicleIcon;
                    let selectedVehicleLat;
                    let selectedVehicleLng;
                    let markerCluster;

                    let currentGeofences;

                    let directionsService;
                    let allRoutes = []; // Store all route polylines
                    let activePolyline = null; // Store the currently selected route
                    let allMarkers = []; // Store all markers

                    let drawnGeofences = [];


                    let startRoute = null;
                    let endRoute = null;
                    let stopsRoute = [];

                    // Initialize the map asynchronously
                    async function initializeMap() {
                        // Check if the HTML tag has the 'dark' class
                        const isDarkMode = document.documentElement.classList.contains('dark');


                        const mapCenter = {
                            lat: 41.9028,
                            lng: 12.4964
                        }; // Initial center of the map

                        // Load Google Maps API and libraries
                        map = new google.maps.Map(document.getElementById('map'), {
                            center: mapCenter,
                            zoom: 6,
                            mapId: "fleetMap", // Replace with your actual Map ID if applicable
                            mapTypeId: isDarkMode ? google.maps.MapTypeId.HYBRID : google.maps.MapTypeId
                                .ROADMAP, // Use hybrid in dark mode for satellite with labels
                        });

                        directionsService = new google.maps.DirectionsService();

                        const {
                            AdvancedMarkerElement,
                            PinElement
                        } = await google.maps.importLibrary("marker");


                        // Initial marker update
                        updateMarkers(@json($devices), AdvancedMarkerElement);
                    }


                    const emptySelected = () => {

                        selectedVehicleImei = null;

                        // Remove existing route paths & markers
                        // allRoutes.forEach(route => route.setMap(null));
                        // allRoutes = [];
                        // allMarkers.forEach(marker => marker.setMap(null));
                        // allMarkers = [];
                    }

                    // Initialize marker clustering
                    // Initialize marker clustering
                    function initializeMarkerCluster() {
                        if (markerCluster) {
                            markerCluster.clearMarkers(); // Clear existing clusters
                        }

                        markerCluster = new markerClusterer.MarkerClusterer({
                            markers: markers,
                            map: map,
                            algorithm: new markerClusterer.SuperClusterAlgorithm({
                                radius: 100,
                                maxZoom: 15,
                                minPoints: 2
                            }),
                            renderer: {
                                render: ({
                                    count,
                                    position
                                }) => {
                                    // Determine cluster size and color
                                    let size, backgroundColor, pulseSize;
                                    if (count < 10) {
                                        size = '40px';
                                        pulseSize = '44px';
                                        backgroundColor = '#F54619';
                                    } else if (count < 50) {
                                        size = '50px';
                                        pulseSize = '54px';
                                        backgroundColor = '#e0360b';
                                    } else {
                                        size = '60px';
                                        pulseSize = '64px';
                                        backgroundColor = '#cc2d06';
                                    }

                                    const clusterDiv = document.createElement("div");

                                    // Create wrapper for pulse effect
                                    const pulseWrapper = document.createElement("div");
                                    pulseWrapper.style.position = "relative";
                                    pulseWrapper.style.width = pulseSize;
                                    pulseWrapper.style.height = pulseSize;

                                    // Create pulse effect
                                    const pulseEffect = document.createElement("div");
                                    pulseEffect.style.position = "absolute";
                                    pulseEffect.style.width = "100%";
                                    pulseEffect.style.height = "100%";
                                    pulseEffect.style.borderRadius = "50%";
                                    pulseEffect.style.backgroundColor = backgroundColor;
                                    pulseEffect.style.opacity = "0.2";
                                    pulseEffect.style.animation = "pulseAnimation 2s infinite";

                                    // Create main container with enhanced styles
                                    const container = document.createElement("div");
                                    container.style.width = size;
                                    container.style.height = size;
                                    container.style.backgroundColor = backgroundColor;
                                    container.style.borderRadius = "50%";
                                    container.style.display = "flex";
                                    container.style.alignItems = "center";
                                    container.style.justifyContent = "center";
                                    container.style.color = "white";
                                    container.style.fontWeight = "600";
                                    container.style.fontSize = count > 99 ? "14px" : "16px";
                                    container.style.boxShadow = "0 3px 6px rgba(0,0,0,0.3)";
                                    container.style.border = "2px solid rgba(255,255,255,0.6)";
                                    container.style.position = "absolute";
                                    container.style.top = "50%";
                                    container.style.left = "50%";
                                    container.style.transform = "translate(-50%, -50%)";
                                    container.style.transformOrigin = "center";
                                    container.style.animation = "clusterAnimation 0.3s ease-in-out";
                                    container.style.cursor = "pointer";
                                    container.style.transition = "transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out";

                                    // Add hover effect
                                    container.onmouseenter = () => {
                                        container.style.transform = "translate(-50%, -50%) scale(1.1)";
                                        container.style.boxShadow = "0 5px 12px rgba(0,0,0,0.4)";
                                    };
                                    container.onmouseleave = () => {
                                        container.style.transform = "translate(-50%, -50%) scale(1)";
                                        container.style.boxShadow = "0 3px 6px rgba(0,0,0,0.3)";
                                    };

                                    // Create count span with enhanced styles
                                    const countSpan = document.createElement("span");
                                    countSpan.textContent = count > 99 ? "99+" : count;
                                    countSpan.style.position = "relative";
                                    countSpan.style.zIndex = "2";
                                    countSpan.style.textShadow = "1px 1px 2px rgba(0,0,0,0.3)";

                                    // Create gradient overlay with enhanced effect
                                    const gradientOverlay = document.createElement("div");
                                    gradientOverlay.style.position = "absolute";
                                    gradientOverlay.style.width = "100%";
                                    gradientOverlay.style.height = "100%";
                                    gradientOverlay.style.borderRadius = "50%";
                                    gradientOverlay.style.background =
                                        "radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%)";
                                    gradientOverlay.style.pointerEvents = "none";

                                    // Create and add enhanced animations
                                    const style = document.createElement("style");
                                    style.textContent = `
    @keyframes clusterAnimation {
        from {
            transform: translate(-50%, -50%) scale(0.8);
            opacity: 0;
        }
        to {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }
    }
    @keyframes pulseAnimation {
        0% {
            transform: scale(0.95);
            opacity: 0.5;
        }
        50% {
            transform: scale(1.05);
            opacity: 0.2;
        }
        100% {
            transform: scale(0.95);
            opacity: 0.5;
        }
    }
`;

                                    // Only add style if it doesn't exist
                                    if (!document.getElementById('cluster-animations')) {
                                        style.id = 'cluster-animations';
                                        document.head.appendChild(style);
                                    }

                                    // Assemble the components
                                    container.appendChild(countSpan);
                                    container.appendChild(gradientOverlay);
                                    pulseWrapper.appendChild(pulseEffect);
                                    pulseWrapper.appendChild(container);
                                    clusterDiv.appendChild(pulseWrapper);

                                    return new google.maps.marker.AdvancedMarkerElement({
                                        position,
                                        content: clusterDiv,
                                        zIndex: 1
                                    });
                                }
                            }
                        });
                    }


                    function updateMarkers(vehicleData, AdvancedMarkerElement) {
                        // Clear existing markers
                        markers.forEach(marker => marker.map = null);
                        markers = [];

                        // Loop through vehicles and add markers
                        Object.keys(vehicleData).forEach(type => {
                            let vehicles = vehicleData[type] || vehicleData['default'];

                            // Handle case when vehicles is an object instead of array
                            if (vehicles && typeof vehicles === 'object' && !Array.isArray(vehicles)) {
                                // Convert object to array
                                vehicles = Object.values(vehicles);
                            }

                            // Ensure vehicles is defined and not empty
                            if (!vehicles || vehicles.length === 0) return;
                            vehicles.forEach(vehicle => {
                                // More precise coordinate parsing
                                const latitude = parseFloat(vehicle.latitude);
                                const longitude = parseFloat(vehicle.longitude);
                                const movementStatus = parseInt(vehicle.movement_status);
                                const ignitionStatus = parseInt(vehicle.ignition_status);
                                const speed = parseInt(vehicle.speed);
                                const angle = parseFloat(vehicle.angle) || 0;

                                // Validate coordinates
                                if (!isValidCoordinate(latitude, longitude)) return;

                                // Determine icon color based on vehicle status
                                let iconColor = determineVehicleStatus(movementStatus, ignitionStatus, speed);

                                // Create custom marker content with improved positioning
                                const markerDiv = document.createElement('div');
                                markerDiv.innerHTML = `
                <div style="position:relative; transform-origin: center bottom;">
                    <img src="{{ asset('assets/images/icons/${vehicle.icon}/${iconColor}.png') }}"
                         style="width: auto; height: 60px; transform: rotate(${angle}deg);
                         filter: drop-shadow(2px 4px 6px rgba(0,0,0,0.9));">

                    <!-- Vehicle Info Box -->
                    <div style="position:absolute; left: 50%; transform: translateX(-50%); bottom:-40px;
                                background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(50, 50, 50, 0.6));
                                color: white; padding: 6px 10px; border-radius: 8px; font-size: 10px;
                                font-weight: 600; box-shadow: 2px 4px 8px rgba(0, 0, 0, 0.4);
                                white-space: nowrap;">
                        <div style="display:flex;align-items:center;gap:5px; margin-bottom: 3px;">
                            <img src="{{ asset('assets/images/icons/plate.svg') }}"
                                 style="width: 12px; height: 12px;">
                            <span>${vehicle.license_plate}</span>
                        </div>
                        ${vehicle.driver?.name ? `<div style="display:flex;align-items:center;gap:5px;"><img src="{{ asset('assets/images/icons/driver.svg') }}"style="width: 12px; height: 12px;"><span>${vehicle.driver.name}</span> </div>` : ''}
                    </div>
                </div>
            `;

                                // Create marker with improved options
                                const marker = new google.maps.marker.AdvancedMarkerElement({
                                    map,
                                    position: {
                                        lat: latitude,
                                        lng: longitude
                                    },
                                    content: markerDiv,
                                    collisionBehavior: google.maps.CollisionBehavior
                                        .OPTIONAL_AND_HIDES_LOWER_PRIORITY,
                                    zIndex: selectedVehicleImei === vehicle.imei ? 1000 : 1
                                });

                                // Add click event to marker
                                marker.addListener('click', () => {
                                    focusOnMarker(vehicle.imei, latitude, longitude, vehicle.icon,
                                        iconColor);

                                    // window.dispatchEvent(
                                    //     new CustomEvent('show-vehicle-details', {
                                    //         detail: vehicle,
                                    //     })
                                    // );

                                    @this.call('updateVehicleDetails', vehicle);
                                });


                                // Update selected vehicle position if in live mode
                                let liveMode = document.getElementById('liveModeToggle')?.checked;
                                if (liveMode && selectedVehicleImei === vehicle.imei && iconColor == 'green') {
                                    map.panTo({
                                        lat: latitude,
                                        lng: longitude
                                    });
                                }

                                markers.push(marker);
                            });
                        });

                        initializeMarkerCluster();
                    }

                    // Helper functions
                    function isValidCoordinate(lat, lng) {
                        return !isNaN(lat) && !isNaN(lng) &&
                            lat !== 0 && lng !== 0 &&
                            lat >= -90 && lat <= 90 &&
                            lng >= -180 && lng <= 180;
                    }

                    function determineVehicleStatus(movement, ignition, speed) {
                        if (movement == 1 && speed > 0) return 'green';
                        if (movement == 0 && ignition == 1) return 'yellow';
                        return 'red';
                    }




                    function focusOnMarker(imei, latitude, longitude, icon = null, color = null) {
                        selectedVehicleImei = imei;
                        seletedVehicleIcon = icon;
                        selectedVehicleLat = latitude;
                        selectedVehicleLng = longitude;


                        // let liveMode = document.getElementById('liveMode')?.checked;
                        // if (selectedVehicleImei && liveMode == false) {
                        //     return;
                        // }

                        // updateMode();

                        if (!latitude || !longitude) return;

                        const position = {
                            lat: parseFloat(latitude),
                            lng: parseFloat(longitude)
                        };

                        // Pan and zoom to the position
                        map.panTo(position);
                        map.setZoom(18);


                        if (latitude && longitude) {
                            // Get address
                            getAddressFromCoordinates(latitude, longitude).then(address => {
                                if (document.getElementById('address')) {
                                    document.getElementById('address').innerHTML = address || "Address not found";
                                }
                            });

                            const position = {
                                lat: parseFloat(latitude),
                                lng: parseFloat(longitude)
                            };

                            // Add Street View
                            const streetViewService = new google.maps.StreetViewService();

                            streetViewService.getPanorama({
                                location: position,
                                radius: 50,
                                source: google.maps.StreetViewSource.OUTDOOR
                            }, (data, status) => {
                                if (status === google.maps.StreetViewStatus.OK) {
                                    // Create or get street view container
                                    let streetViewDiv = document.getElementById('street-view');
                                    if (!streetViewDiv) {
                                        streetViewDiv = document.createElement('div');
                                        streetViewDiv.id = 'street-view';
                                        streetViewDiv.style.width = '100%';
                                        streetViewDiv.style.height = '200px';
                                        streetViewDiv.style.marginTop = '10px';
                                        streetViewDiv.style.borderRadius = '8px';
                                        streetViewDiv.style.overflow = 'hidden';

                                        // Insert after address element
                                        const addressElement = document.getElementById('address');
                                        if (addressElement && addressElement.parentNode) {
                                            addressElement.parentNode.insertBefore(streetViewDiv, addressElement.nextSibling);
                                        }
                                    }

                                    // Initialize Street View Panorama
                                    const panorama = new google.maps.StreetViewPanorama(streetViewDiv, {
                                        position: position,
                                        pov: {
                                            heading: 34,
                                            pitch: 10
                                        },
                                        addressControl: true,
                                        fullscreenControl: true,
                                        linksControl: false,
                                        panControl: false,
                                        enableCloseButton: false,
                                        zoomControl: true,
                                        visible: true
                                    });

                                    // Connect the panorama to the map
                                    map.setStreetView(panorama);

                                } else {
                                    // Remove street view if not available
                                    const streetViewDiv = document.getElementById('street-view');
                                    if (streetViewDiv) {
                                        streetViewDiv.remove();
                                    }
                                }
                            });
                        }



                    }


                    function initializeRoute(current_route) {
                        // Clear previous routes
                        allRoutes.forEach(route => route.setMap(null));
                        allRoutes = [];
                        allMarkers.forEach(marker => marker.setMap(null));
                        allMarkers = [];

                        if (current_route) {
                            const {
                                start_point_lat,
                                start_point_lng,
                                end_point_lat,
                                end_point_lng,
                                stops,
                                route,
                                start_point_status,
                                end_point_status,
                                start_point_completed_at,
                                end_point_completed_at,
                                start_point_odometer,
                                end_point_odometer,
                                start_point_fuel,
                                end_point_fuel
                            } = current_route;

                            if (!start_point_lat || !start_point_lng || !end_point_lat || !end_point_lng || !route) return;

                            // Parse the route
                            let routePath = google.maps.geometry.encoding.decodePath(route);

                            // Create segments based on stops
                            let allPoints = [{
                                lat: parseFloat(start_point_lat),
                                lng: parseFloat(start_point_lng),
                                status: start_point_status
                            }];

                            // Add stops in order
                            if (stops && stops.length > 0) {
                                stops.forEach(stop => {
                                    allPoints.push({
                                        lat: parseFloat(stop.latitude),
                                        lng: parseFloat(stop.longitude),
                                        status: stop.status
                                    });
                                });
                            }

                            // Add end point
                            allPoints.push({
                                lat: parseFloat(end_point_lat),
                                lng: parseFloat(end_point_lng),
                                status: end_point_status
                            });

                            // Create route segments between points
                            for (let i = 0; i < allPoints.length - 1; i++) {
                                const startPoint = allPoints[i];
                                const endPoint = allPoints[i + 1];

                                // Find route segment between these points
                                const segmentPath = findRouteSegment(routePath, startPoint, endPoint);

                                // Create polyline for this segment
                                const segmentPolyline = new google.maps.Polyline({
                                    path: segmentPath,
                                    geodesic: true,
                                    strokeColor: (startPoint.status === 'completed' && endPoint.status === 'completed') ?
                                        '#10B981' : '#2563EB', // Green only if both points are completed
                                    strokeOpacity: 1.0,
                                    strokeWeight: 4,
                                    map: map,
                                    icons: [{
                                        icon: {
                                            path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                                            scale: 3,
                                            strokeColor: '#FFFFFF',
                                            strokeWeight: 2,
                                            fillColor: (startPoint.status === 'completed' && endPoint.status ===
                                                    'completed') ?
                                                '#10B981' : '#2563EB',
                                            fillOpacity: 1
                                        },
                                        offset: '50px',
                                        repeat: '100px'
                                    }]
                                });
                                allRoutes.push(segmentPolyline);
                            }

                            // Add markers (rest of your existing marker code)
                            startRoute = addCustomMarker({
                                lat: parseFloat(start_point_lat),
                                lng: parseFloat(start_point_lng)
                            }, 'start', start_point_status, {
                                start_point_completed_at,
                                start_point_odometer,
                                start_point_fuel
                            });

                            // For stops, we need to track the previous point
                            let previousPoint = {
                                odometer: start_point_odometer,
                                fuel: start_point_fuel
                            };

                            stopsRoute = stops?.map(stop => {
                                const marker = addCustomMarker({
                                        lat: parseFloat(stop.latitude),
                                        lng: parseFloat(stop.longitude)
                                    },
                                    'stop',
                                    stop.status, {
                                        previousPoint,
                                        completed_at: stop.completed_at,
                                        stop_odometer: stop.stop_odometer,
                                        stop_fuel: stop.stop_fuel
                                    }
                                );

                                // Update previous point for next iteration
                                previousPoint = {
                                    odometer: stop.stop_odometer,
                                    fuel: stop.stop_fuel
                                };

                                return marker;
                            }) || [];

                            endRoute = addCustomMarker({
                                    lat: parseFloat(end_point_lat),
                                    lng: parseFloat(end_point_lng)
                                },
                                'end',
                                end_point_status, {
                                    previousPoint,
                                    end_point_completed_at,
                                    end_point_odometer,
                                    end_point_fuel,
                                    start_point_odometer,
                                    start_point_fuel
                                }
                            );

                            // Fit bounds
                            const bounds = new google.maps.LatLngBounds();
                            routePath.forEach(point => bounds.extend(point));
                            map.fitBounds(bounds);
                        }
                    }

                    // Custom marker creation function with improved styling
                    function addCustomMarker(position, type, status, pointData) {
                        // Create main container
                        const markerContainer = document.createElement('div');
                        markerContainer.className = 'marker-container';
                        markerContainer.style.position = 'relative';

                        // Create marker icon container
                        const markerIcon = document.createElement('div');
                        markerIcon.className = 'marker-icon';
                        const statusColor = status === 'completed' ? '#10B981' : '#EF4444';
                        Object.assign(markerIcon.style, {
                            width: '40px',
                            height: '40px',
                            background: statusColor,
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                            border: '2px solid white'
                        });

                        // Create icon image
                        const iconImg = document.createElement('img');
                        iconImg.src = `{{ asset('assets/images/icons/${type}.svg') }}`;
                        Object.assign(iconImg.style, {
                            width: '24px',
                            height: '24px',
                            filter: 'brightness(0) invert(1)'
                        });
                        markerIcon.appendChild(iconImg);

                        // Create pulse effect
                        const pulseEffect = document.createElement('div');
                        pulseEffect.className = 'pulse-effect';
                        Object.assign(pulseEffect.style, {
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            width: '40px',
                            height: '40px',
                            background: statusColor,
                            borderRadius: '50%',
                            opacity: '0.5',
                            animation: 'pulse 1.5s infinite'
                        });

                        // Assemble the marker
                        markerContainer.appendChild(markerIcon);
                        if (status != 'completed') markerContainer.appendChild(pulseEffect);

                        // Create the marker
                        const marker = new google.maps.marker.AdvancedMarkerElement({
                            position,
                            content: markerContainer,
                            map,
                            zIndex: 2
                        });

                        // Create info window content based on type and data
                        let infoContent = null;
                        if (pointData && type === 'start' && status == 'completed') {
                            infoContent = `
    <div style="padding: 15px; min-width: 300px;">
        <div style="display: flex; align-items: center; margin-bottom: 12px; border-bottom: 2px solid #E5E7EB; padding-bottom: 12px;">
            <div style="width: 40px; height: 40px; background: #10B981; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: 2px solid white;margin-right: 10px;">
             <img src="{{ asset('assets/images/icons/start.svg') }}" style="width: 24px; height: 24px; ">
            </div>
            <h3 style="margin: 0; color: #1F2937; font-size: 16px; font-weight: 600;">@lang('translations.start_point')</h3>
        </div>
        <div style="color: #4B5563; font-size: 14px;">
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <div style="width: 8px; height: 8px; border-radius: 50%; background-color: ${status === 'completed' ? '#10B981' : '#EF4444'}; margin-right: 8px;"></div>
                <span>@lang('translations.status') ${status.charAt(0).toUpperCase() + status.slice(1)}</span>
            </div>
            ${pointData.start_point_completed_at ? `<div style="display: flex; align-items: center; margin-bottom: 8px;">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <img src="{{ asset('assets/images/icons/clock.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <span>@lang('translations.completed_at'): ${formatDateTime(pointData.start_point_completed_at)}</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </div>` : ''}
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/odometer.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.initial_odometer'): ${formatNumber(pointData.start_point_odometer)} km</span>
            </div>
            <div style="display: flex; align-items: center;">
                <img src="{{ asset('assets/images/icons/fuel-used.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.initial_fuel'): ${pointData.start_point_fuel || 'N/A'} L</span>
            </div>
        </div>
    </div>`;
                        } else if (pointData && type === 'stop' && status == 'completed') {
                            const prevPoint = pointData.previousPoint || {};
                            const distance = calculateDistance(prevPoint.odometer, pointData.stop_odometer);
                            const fuelConsumption = calculateFuelConsumption(prevPoint.fuel, pointData.stop_fuel);

                            infoContent = `
    <div style="padding: 15px; min-width: 300px;">
        <div style="display: flex; align-items: center; margin-bottom: 12px; border-bottom: 2px solid #E5E7EB; padding-bottom: 12px;">
            <div style="width: 40px; height: 40px; background: #10B981; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: 2px solid white;margin-right: 10px;">
            <img src="{{ asset('assets/images/icons/stop.svg') }}" style="width: 24px; height: 24px; ">
            </div>
            <h3 style="margin: 0; color: #1F2937; font-size: 16px; font-weight: 600;">@lang('translations.stop_point')</h3>
        </div>
        <div style="color: #4B5563; font-size: 14px;">
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <div style="width: 8px; height: 8px; border-radius: 50%; background-color: ${status === 'completed' ? '#10B981' : '#EF4444'}; margin-right: 8px;"></div>
                <span>@lang('translations.status'): ${status.charAt(0).toUpperCase() + status.slice(1)}</span>
            </div>
            ${pointData.completed_at ? `<div style="display: flex; align-items: center; margin-bottom: 8px;">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <img src="{{ asset('assets/images/icons/clock.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <span>@lang('translations.completed_at'): ${formatDateTime(pointData.completed_at)}</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        </div>` : ''}
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/distance.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.distance_from_previous'): ${distance} km</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/fuel-consumption-icon.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.fuel_consumption'): ${fuelConsumption} L</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/odometer.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.current_odometer'): ${formatNumber(pointData.stop_odometer)} km</span>
            </div>
            <div style="display: flex; align-items: center;">
                <img src="{{ asset('assets/images/icons/fuel-used.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.current_fuel'): ${pointData.stop_fuel || 'N/A'} L</span>
            </div>
        </div>
    </div>`;
                        } else if (pointData && type === 'end' && status == 'completed') {
                            const prevPoint = pointData.previousPoint || {};
                            const distance = calculateDistance(prevPoint.odometer, pointData.end_point_odometer);
                            const fuelConsumption = calculateFuelConsumption(prevPoint.fuel, pointData.end_point_fuel);
                            const totalDistance = calculateDistance(pointData.start_point_odometer, pointData.end_point_odometer);
                            const totalFuel = calculateFuelConsumption(pointData.start_point_fuel, pointData.end_point_fuel);

                            infoContent = `
    <div style="padding: 15px; min-width: 300px;">
        <div style="display: flex; align-items: center; margin-bottom: 12px; border-bottom: 2px solid #E5E7EB; padding-bottom: 12px;">
               <div style="width: 40px; height: 40px; background: #10B981; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: 2px solid white;margin-right: 10px;">
            <img src="{{ asset('assets/images/icons/end.svg') }}" style="width: 24px; height: 24px; ">
            </div>
            <h3 style="margin: 0; color: #1F2937; font-size: 16px; font-weight: 600;">@lang('translations.end_point')</h3>
        </div>
        <div style="color: #4B5563; font-size: 14px;">
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <div style="width: 8px; height: 8px; border-radius: 50%; background-color: ${status === 'completed' ? '#10B981' : '#EF4444'}; margin-right: 8px;"></div>
                <span>@lang('translations.status'): ${status.charAt(0).toUpperCase() + status.slice(1)}</span>
            </div>
            ${pointData.end_point_completed_at ? ` <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <img src="{{ asset('assets/images/icons/clock.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <span>@lang('translations.completed_at'): ${formatDateTime(pointData.end_point_completed_at)}</span>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </div>` : ''}
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/distance.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.distance_from_previous'): ${distance} km</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/fuel-consumption-icon.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.fuel_consumption'): ${fuelConsumption} L</span>
            </div>
            <div style="margin: 12px 0; border-top: 1px solid #E5E7EB; padding-top: 12px;">
                <div style="font-weight: 600; color: #1F2937; margin-bottom: 8px; display: flex; align-items: center;">
                    <img src="{{ asset('assets/images/icons/stats.svg') }}" style="width: 16px; height: 16px; margin-right: 8px;">
                    @lang('translations.total_route_statistics')
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <img src="{{ asset('assets/images/icons/distance.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                    <span>@lang('translations.total_distance'): ${totalDistance} km</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <img src="{{ asset('assets/images/icons/fuel-consumption-icon.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                    <span>@lang('translations.total_fuel_used'): ${totalFuel} L</span>
                </div>
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <img src="{{ asset('assets/images/icons/odometer.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                    <span>@lang('translations.final_odometer'): ${formatNumber(pointData.end_point_odometer)} km</span>
                </div>
                <div style="display: flex; align-items: center;">
                    <img src="{{ asset('assets/images/icons/fuel-used.svg') }}" style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                    <span>@lang('translations.final_fuel'): ${pointData.end_point_fuel || 'N/A'} L</span>
                </div>
            </div>
        </div>
    </div>`;
                        }

                        if (infoContent) {
                            // Create and attach info window
                            const infoWindow = new google.maps.InfoWindow({
                                content: infoContent
                            });

                            // Add mouseover listener
                            marker.addListener("mouseover", () => {
                                infoWindow.open(map, marker);

                            });

                            // Add mouseout listener
                            marker.addListener("mouseout", () => {
                                infowindow.close();
                            });
                        }



                        // Store info window reference
                        // allInfoWindows.push(infoWindow);
                        allMarkers.push(marker);

                        return marker;
                    }

                    // Helper functions
                    function formatDateTime(dateString) {
                        return new Date(dateString).toLocaleString();
                    }

                    function formatNumber(number) {
                        return number ? (number / 1000).toFixed(2) : 'N/A';
                    }

                    function calculateDistance(prevOdometer, currentOdometer) {
                        if (!prevOdometer || !currentOdometer) return 'N/A';
                        return ((currentOdometer - prevOdometer) / 1000).toFixed(2);
                    }

                    function calculateFuelConsumption(prevFuel, currentFuel) {
                        if (!prevFuel || !currentFuel) return 'N/A';
                        return Math.abs(prevFuel - currentFuel).toFixed(2);
                    }

                    // Helper function to find route segment between two points
                    function findRouteSegment(fullPath, startPoint, endPoint) {
                        // Find the closest points in the full path to our start and end points
                        let startIdx = 0;
                        let endIdx = fullPath.length - 1;
                        let minStartDist = Infinity;
                        let minEndDist = Infinity;

                        for (let i = 0; i < fullPath.length; i++) {
                            const point = fullPath[i];

                            // Calculate distance to start point
                            const startDist = getDistance({
                                    lat: point.lat(),
                                    lng: point.lng()
                                },
                                startPoint
                            );

                            // Calculate distance to end point
                            const endDist = getDistance({
                                    lat: point.lat(),
                                    lng: point.lng()
                                },
                                endPoint
                            );

                            if (startDist < minStartDist) {
                                minStartDist = startDist;
                                startIdx = i;
                            }

                            if (endDist < minEndDist) {
                                minEndDist = endDist;
                                endIdx = i;
                            }
                        }

                        // Return the segment of the path between these points
                        return fullPath.slice(startIdx, endIdx + 1);
                    }

                    // Helper function to calculate distance between two points
                    function getDistance(p1, p2) {
                        return google.maps.geometry.spherical.computeDistanceBetween(
                            new google.maps.LatLng(p1.lat, p1.lng),
                            new google.maps.LatLng(p2.lat, p2.lng)
                        );
                    }





                    let historyData = [];
                    let historyMarker = null; // Reference for the history marker
                    let path = null; // Reference for the Polyline
                    let pathCoordinates = [];

                    // if (document.getElementById('liveMode')) {
                    //     document.getElementById('liveMode').addEventListener('change', updateMode);
                    //     const dateDropdown = document.getElementById('dateDropdown');
                    // }

                    // async function updateMode() {
                    //     let liveMode = document.getElementById('liveMode')?.checked;
                    //     document.getElementById('historyControls').style.display = liveMode ? 'none' : 'block';

                    //     clearMapElements();

                    //     if (!liveMode) {
                    //         dateDropdown.innerHTML = '';
                    //         await fetchAvailableDates();
                    //     } else {
                    //         historyData = [];


                    //         if (selectedVehicleLat && selectedVehicleLng) {
                    //             const latitude = parseFloat(selectedVehicleLat);
                    //             const longitude = parseFloat(selectedVehicleLng);

                    //             if (!latitude || !longitude) return;

                    //             const position = {
                    //                 lat: parseFloat(latitude),
                    //                 lng: parseFloat(longitude)
                    //             };

                    //             // Pan and zoom to the position
                    //             map.panTo(position);
                    //             map.setZoom(18);
                    //         }
                    //     }
                    // }

                    // async function fetchAvailableDates() {
                    //     try {
                    //         const response = await fetch(
                    //             `{{ url('/') }}/data/history/${selectedVehicleImei}/dates.json?nocache=${Date.now()}`);


                    //         if (!response.ok) {
                    //             // If file doesn't exist (404), simply return without breaking the script
                    //             if (response.status === 404) {
                    //                 console.warn("No available dates found.");
                    //                 return;
                    //             }
                    //             throw new Error(`HTTP error! Status: ${response.status}`);
                    //         }

                    //         const dates = await response.json();

                    //         if (dates.length > 0) {
                    //             populateDateDropdown(dates);
                    //             fetchHistoryData(dates[0]); // Fetch data for the first date
                    //         }
                    //     } catch (error) {
                    //         console.error("Error fetching available dates:", error);
                    //     }
                    // }


                    // function populateDateDropdown(dates) {
                    //     dateDropdown.innerHTML = ""; // Clear existing options

                    //     dates.forEach((date, index) => {
                    //         const option = document.createElement("option");
                    //         option.value = date;
                    //         option.textContent = date;
                    //         if (index === 0) option.selected = true;
                    //         dateDropdown.appendChild(option);
                    //     });

                    //     dateDropdown.addEventListener("change", (event) => {
                    //         fetchHistoryData(event.target.value);
                    //     });
                    // }

                    // async function fetchHistoryData(selectedDate) {
                    //     try {
                    //         clearMapElements();
                    //         historyData = [];

                    //         const response = await fetch(
                    //             `{{ asset('data/history/${selectedVehicleImei}/${selectedDate}.json') }}`);

                    //         if (!response.ok) {
                    //             // If file doesn't exist (404), log a warning and exit gracefully
                    //             if (response.status === 404) {
                    //                 console.warn(`History data for ${selectedDate} not found.`);
                    //                 return;
                    //             }
                    //             throw new Error(`HTTP error! Status: ${response.status}`);
                    //         }

                    //         historyData = await response.json();

                    //         plotPathOnMap();
                    //         initializeHistoryMarker(seletedVehicleIcon, 'red');
                    //     } catch (error) {
                    //         console.error("Error fetching history data:", error);
                    //     }
                    // }



                    let startMarker = null;
                    let endMarker = null;
                    let infoWindows = [];
                    let infoMarkers = [];
                    let historyPath = null;
                    let stopMarkers = [];

                    function plotPathOnMap() {
                        if (!historyData || historyData.length === 0) return;

                        // Clear existing path coordinates
                        pathCoordinates = [];
                        let stopPoints = [];
                        const DISTANCE_THRESHOLD = 50; // meters
                        const SPEED_THRESHOLD = 15; // km/h
                        const ANGLE_THRESHOLD = 45; // degrees

                        let lastAddedPoint = null;
                        let currentStopStart = null;

                        let lastTripStop = null;
                        let nextTripStart = null;

                        let totalOdometer = 0; // Track cumulative distance

                        // Process history data
                        historyData.forEach((item, index) => {
                            if (!item.latitude || !item.longitude) return;

                            const point = {
                                lat: parseFloat(item.latitude),
                                lng: parseFloat(item.longitude),
                                timestamp: item?.last_update,
                                angle: parseFloat(item?.angle) || 0,
                                speed: parseFloat(item?.speed) || 0,
                                ignition: item["239"], // ignition status
                                movement: item["240"], // movement status
                                trip_event: item.eventID,
                                trip_status: parseInt(item["250"]) || 0, // trip status
                                trip_odometer: item["199"] || 0, // trip odometer
                                index: index
                            };

                            // Update total odometer from start
                            if (index === 0) {
                                totalOdometer = point.trip_odometer;
                            }


                            // Always add first and last points
                            if (index === 0 || index === historyData.length - 1) {
                                pathCoordinates.push(point);
                                lastAddedPoint = point;
                                return;
                            }

                            // Detect stops (vehicle not moving with ignition on)
                            if (point.speed < 1 && point.ignition === 1) {
                                if (!currentStopStart) {
                                    currentStopStart = point;
                                    stopPoints.push(point);
                                }
                            } else {
                                currentStopStart = null;
                            }

                            // Track trip events for stop duration
                            if (point.trip_event === 250) {
                                if (point.trip_status === 0) { // Vehicle stopped
                                    if (!lastTripStop) { // Only set if not already tracking a stop
                                        lastTripStop = point;
                                        lastTripStop.totalOdometer = point.trip_odometer; // Store total distance at stop
                                    }
                                } else if (point.trip_status === 1) { // Vehicle started
                                    if (lastTripStop) {
                                        // Calculate stop duration
                                        const stopDuration = calculateDuration(lastTripStop.timestamp, point.timestamp);
                                        stopPoints.push({
                                            ...lastTripStop,
                                            duration: stopDuration,
                                            startTime: lastTripStop.timestamp,
                                            endTime: point.timestamp,
                                            trip_odometer: point.trip_odometer - lastTripStop.trip_odometer,
                                            total_distance: (lastTripStop.totalOdometer / 1000).toFixed(
                                                2) // Convert to km
                                        });
                                    }
                                    lastTripStop = null;
                                }
                            } else if (point.speed < 1 && point.ignition === 1) {
                                // Backup method: Also detect stops based on speed and ignition
                                if (!lastTripStop) {
                                    lastTripStop = point;
                                    lastTripStop.totalOdometer = point.trip_odometer;
                                }
                            } else if (point.speed > 5 && lastTripStop) {
                                // Vehicle started moving again
                                const stopDuration = calculateDuration(lastTripStop.timestamp, point.timestamp);
                                stopPoints.push({
                                    ...lastTripStop,
                                    duration: stopDuration,
                                    startTime: lastTripStop.timestamp,
                                    endTime: point.timestamp,
                                    trip_odometer: point.trip_odometer - lastTripStop.trip_odometer,
                                    total_distance: (lastTripStop.totalOdometer / 1000).toFixed(2) // Convert to km
                                });
                                lastTripStop = null;
                            }

                            // Add points based on significant changes
                            if (lastAddedPoint) {
                                // Use Haversine formula for distance calculation
                                const calculateDistance = (lat1, lon1, lat2, lon2) => {
                                    const R = 6371000;
                                    const φ1 = lat1 * Math.PI / 180;
                                    const φ2 = lat2 * Math.PI / 180;
                                    const Δφ = (lat2 - lat1) * Math.PI / 180;
                                    const Δλ = (lon2 - lon1) * Math.PI / 180;

                                    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
                                        Math.cos(φ1) * Math.cos(φ2) *
                                        Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
                                    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

                                    return R * c;
                                };

                                const distance = calculateDistance(
                                    lastAddedPoint.lat, lastAddedPoint.lng,
                                    point.lat, point.lng
                                );

                                const speedChange = Math.abs(point.speed - lastAddedPoint.speed);
                                const angleChange = Math.abs(point.angle - lastAddedPoint.angle);

                                if (distance > DISTANCE_THRESHOLD ||
                                    speedChange > SPEED_THRESHOLD ||
                                    angleChange > ANGLE_THRESHOLD) {
                                    pathCoordinates.push(point);
                                    lastAddedPoint = point;
                                }
                            }
                        });

                        // Draw the path
                        historyPath = new google.maps.Polyline({
                            path: pathCoordinates,
                            geodesic: true,
                            strokeColor: '#F54619',
                            strokeOpacity: 0.8,
                            strokeWeight: 3,
                            icons: [{
                                icon: {
                                    path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                                    scale: 5,
                                    fillColor: '#FFFFFF',
                                    fillOpacity: 1,
                                    strokeColor: '#F54619',
                                    strokeWeight: 2
                                },
                                repeat: '150px'
                            }],
                            map: map
                        });

                        // Add mouseover and mouseout listeners to the polyline
                        let hoverInfoWindow = null;

                        google.maps.event.addListener(historyPath, 'mouseover', function(e) {
                            // Find the closest point to where the user hovered
                            const closestPoint = pathCoordinates.reduce((prev, curr) => {
                                const prevDistance = google.maps.geometry.spherical.computeDistanceBetween(
                                    new google.maps.LatLng(prev.lat, prev.lng),
                                    e.latLng
                                );
                                const currDistance = google.maps.geometry.spherical.computeDistanceBetween(
                                    new google.maps.LatLng(curr.lat, curr.lng),
                                    e.latLng
                                );
                                return prevDistance < currDistance ? prev : curr;
                            });

                            const content = `
        <div class="relative min-w-[250px] bg-white rounded-lg shadow-lg border border-gray-100">
            <!-- Main Content -->
            <div class="p-4">
                <!-- Speed Section -->
                <div class="flex items-center justify-between pb-3 mb-3 border-b border-gray-100">
                    <div class="flex items-center gap-3">
                        <div class="p-2 rounded-full bg-primary/10">
                            <svg class="w-5 h-5 text-primary" fill="currentColor" version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                <path d="M10,20C4.5,20,0,15.5,0,10S4.5,0,10,0s10,4.5,10,10S15.5,20,10,20z M10,2c-4.4,0-8,3.6-8,8s3.6,8,8,8s8-3.6,8-8S14.4,2,10,2z"></path>
                                <path d="M8.6,11.4c-0.8-0.8-2.8-5.7-2.8-5.7s4.9,2,5.7,2.8c0.8,0.8,0.8,2,0,2.8C10.6,12.2,9.4,12.2,8.6,11.4z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-sm text-gray-500">@lang('translations.speed')</div>
                            <div class="text-lg font-semibold text-gray-800">${closestPoint.speed} km/h</div>
                        </div>
                    </div>
                </div>

                <!-- Time Section -->
                <div class="flex items-center gap-3">
                    <div class="p-2 bg-gray-100 rounded-full">
                        <svg class="w-5 h-5 text-gray-600" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12ZM3.00683 12C3.00683 16.9668 7.03321 20.9932 12 20.9932C16.9668 20.9932 20.9932 16.9668 20.9932 12C20.9932 7.03321 16.9668 3.00683 12 3.00683C7.03321 3.00683 3.00683 7.03321 3.00683 12Z" fill="currentColor"/>
                            <path d="M12 5C11.4477 5 11 5.44771 11 6V12.4667C11 12.4667 11 12.7274 11.1267 12.9235C11.2115 13.0898 11.3437 13.2343 11.5174 13.3346L16.1372 16.0019C16.6155 16.278 17.2271 16.1141 17.5032 15.6358C17.7793 15.1575 17.6155 14.5459 17.1372 14.2698L13 11.8812V6C13 5.44772 12.5523 5 12 5Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">@lang('translations.time')</div>
                        <div class="text-sm font-medium text-gray-700">${formatTimestamp(closestPoint.timestamp)}</div>
                    </div>
                </div>
            </div>

            <!-- Speech Bubble Arrow -->
            <div class="absolute w-4 h-4 rotate-45 -translate-x-1/2 bg-white border-b border-r border-gray-100 -bottom-2 left-1/2"></div>
        </div>
    `;

                            // Close previous hover info window if exists
                            if (hoverInfoWindow) {
                                hoverInfoWindow.close();
                            }

                            // Create and open new info window
                            hoverInfoWindow = new google.maps.InfoWindow({
                                content,
                                position: e.latLng,
                                pixelOffset: new google.maps.Size(0, -20),
                                disableAutoPan: true,
                            });

                            // Remove default InfoWindow styles
                            google.maps.event.addListenerOnce(hoverInfoWindow, 'domready', function() {
                                const bubbleContainer = document.querySelector('.gm-style-iw-c');
                                const bubbleBackground = document.querySelector('.gm-style-iw-d');
                                const closeButton = document.querySelector('.gm-style-iw-t button');

                                if (bubbleContainer) {
                                    bubbleContainer.style.padding = '0';
                                    bubbleContainer.style.borderRadius = '8px';
                                    bubbleContainer.style.boxShadow = '0 4px 6px -1px rgb(0 0 0 / 0.1)';
                                }
                                if (bubbleBackground) {
                                    bubbleBackground.style.overflow = 'visible';
                                }
                                if (closeButton) {
                                    closeButton.style.display = 'none';
                                }
                            });

                            hoverInfoWindow.open(map);
                        });

                        // Close info window when mouse leaves the polyline
                        google.maps.event.addListener(historyPath, 'mouseout', function() {
                            if (hoverInfoWindow) {
                                hoverInfoWindow.close();
                                hoverInfoWindow = null;
                            }
                        });

                        // Add start marker
                        if (pathCoordinates[0]) {
                            startMarker = new google.maps.Marker({
                                position: pathCoordinates[0],
                                map: map,
                                icon: {
                                    url: "{{ asset('assets/images/icons/start-green.svg') }}",
                                    scaledSize: new google.maps.Size(30, 30),
                                }
                            });
                            createInfoWindow(startMarker, pathCoordinates[0], 'Trip Start');
                        }

                        // Add stop points with duration information
                        stopPoints.forEach(point => {
                            // Skip stops with duration less than 1 minute
                            if (!point.duration || point.duration === 'N/A') return;

                            // Parse duration string (e.g., "2h 30m" or "45m")
                            const durationInMinutes = (() => {
                                const hours = point.duration.match(/(\d+)h/);
                                const minutes = point.duration.match(/(\d+)m/);
                                return (hours ? parseInt(hours[1]) * 60 : 0) + (minutes ? parseInt(minutes[1]) : 0);
                            })();

                            // Only show stops longer than 1 minute
                            if (durationInMinutes <= 1) return;

                            // Choose icon based on duration
                            const iconUrl = durationInMinutes >= 30 ?
                                "{{ asset('assets/images/icons/stop-marker-black.svg') }}" // Black dot for stops >= 30 minutes
                                :
                                "{{ asset('assets/images/icons/key.svg') }}"; // Key icon for short stops

                            const iconSize = durationInMinutes >= 30 ? 45 : 35; // Larger size for longer stops

                            const marker = new google.maps.Marker({
                                position: point,
                                map: map,
                                icon: {
                                    url: iconUrl,
                                    scaledSize: new google.maps.Size(iconSize, iconSize),
                                }
                            });
                            stopMarkers.push(marker);

                            // Create info window with stop duration
                            const content = `<div style="min-width: 250px; padding: 15px;">
        <div style="display: flex; align-items: center; margin-bottom: 12px; border-bottom: 2px solid #E5E7EB; padding-bottom: 12px;">
            <div style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-right: 10px;">
                <img src="${iconUrl}" style="width: 28px; height: 28px;">
            </div>
            <h3 style="margin: 0; color: #1F2937; font-size: 16px; font-weight: 600;">
                ${durationInMinutes >= 30 ? '@lang('translations.long_stop')' : '@lang('translations.short_stop')'}
            </h3>
        </div>
        <div style="color: #4B5563; font-size: 14px;">
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/clock.svg') }}"
                     style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.duration') <strong>${point.duration}</strong></span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <img src="{{ asset('assets/images/icons/distance.svg') }}"
                     style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <div style="display: flex; flex-direction: column;">
                    <span style="margin-bottom: 4px;">Start: <strong>${formatTimestamp(point.startTime)}</strong></span>
                    <span>@lang('translations.end') <strong>${formatTimestamp(point.endTime)}</strong></span>
                </div>
            </div>
            <div style="display: flex; align-items: center; margin-top: 8px; padding-top: 8px; border-top: 1px solid #E5E7EB;">
                <img src="{{ asset('assets/images/icons/odometer.svg') }}"
                     style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                <span>@lang('translations.distance') <strong>${point.total_distance} km</strong></span>
            </div>
        </div>
    </div>
`;

                            const infowindow = new google.maps.InfoWindow({
                                content,
                                maxWidth: 300,
                                pixelOffset: new google.maps.Size(0, -20)
                            });

                            marker.addListener("click", () => {
                                infoWindows.forEach(window => window.close());
                                infowindow.open(map, marker);
                            });

                            infoWindows.push(infowindow);
                        });

                        // Add end marker
                        if (pathCoordinates[pathCoordinates.length - 1]) {
                            endMarker = new google.maps.Marker({
                                position: pathCoordinates[pathCoordinates.length - 1],
                                map: map,
                                icon: {
                                    url: "{{ asset('assets/images/icons/end-green.svg') }}",
                                    scaledSize: new google.maps.Size(30, 30),
                                }
                            });
                            createInfoWindow(endMarker, pathCoordinates[pathCoordinates.length - 1], 'Trip End');
                        }



                        // Fit bounds to show all markers
                        const bounds = new google.maps.LatLngBounds();
                        pathCoordinates.forEach(point => bounds.extend(point));
                        map.fitBounds(bounds);
                    }

                    // Helper function to format timestamp
                    function formatTimestamp(timestamp) {
                        if (!timestamp) return 'N/A';

                        // Split the date and time
                        const [date, time] = timestamp.split(' ');
                        // Split date components (assuming DD/MM/YYYY format)
                        const [day, month, year] = date.split('/');

                        // Create date object (month is 0-based, so subtract 1)
                        const dateObj = new Date(year, month - 1, day,
                            ...(time ? time.split(':') : []));

                        return dateObj.toLocaleString();
                    }

                    // Helper function to calculate duration between two timestamps
                    function calculateDuration(startTime, endTime) {
                        if (!startTime || !endTime) return 'N/A';

                        // Parse DD/MM/YYYY HH:mm format
                        const parseCustomDate = (dateStr) => {
                            const [date, time] = dateStr.split(' ');
                            const [day, month, year] = date.split('/');
                            const [hours, minutes] = time ? time.split(':') : [0, 0];
                            return new Date(year, month - 1, day, hours, minutes);
                        };

                        const start = parseCustomDate(startTime);
                        const end = parseCustomDate(endTime);
                        const diffMs = end - start;

                        const hours = Math.floor(diffMs / (1000 * 60 * 60));
                        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

                        let duration = '';
                        if (hours > 0) duration += `${hours}h `;
                        duration += `${minutes}m`;


                        return duration;
                    }

                    function createInfoWindow(marker, point, type = '') {
                        const typeIcons = {
                            'Trip Start': 'start-green.svg',
                            'Trip End': 'end-green.svg'
                        };

                        const content = `
        <div style="min-width: 250px; padding: 15px;">
            <div style="display: flex; align-items: center; margin-bottom: 12px; border-bottom: 2px solid #E5E7EB; padding-bottom: 12px;">
                ${type ? `<img src="{{ asset('assets/images/icons/${typeIcons[type]}') }}" style="width: 24px; height: 24px; margin-right: 10px;"><h3 style="margin: 0; color: #1F2937; font-size: 16px; font-weight: 600;">@lang('translations.stop_point')</h3>` : ''}
            </div>
            <div style="color: #4B5563; font-size: 14px;">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <img src="{{ asset('assets/images/icons/odometer.svg') }}"
                         style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                    <span>@lang('translations.speed') <strong>${point.speed} km/h</strong></span>
                </div>
                <div style="display: flex; align-items: center;">
                    <img src="{{ asset('assets/images/icons/clock.svg') }}"
                         style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;">
                    <span>@lang('translations.time') <strong>${formatTimestamp(point.timestamp)}</strong></span>
                </div>
                ${point.address ? `<div style="display: flex; align-items: center; margin-top: 8px; padding-top: 8px; border-top: 1px solid #E5E7EB;"><img src="{{ asset('assets/images/icons/pin.svg') }}"  style="width: 16px; height: 16px; margin-right: 8px; opacity: 0.7;"><span style="font-size: 12px;">${point.address}</span></div>                                                                                         ` : ''}
            </div>
        </div>
    `;

                        const infowindow = new google.maps.InfoWindow({
                            content,
                            maxWidth: 300,
                            pixelOffset: new google.maps.Size(0, -20)
                        });


                        marker.addListener("click", () => {
                            // Close all other info windows first
                            infoWindows.forEach(window => window.close());
                            infowindow.open(map, marker);
                        });

                        infoWindows.push(infowindow);
                    }




                    // Define arrow symbol that will be placed along the polyline
                    function createArrowSymbol(angle) {
                        return {
                            path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                            strokeColor: "#ff0000",
                            strokeWeight: 2,
                            scale: 3,
                            rotation: angle // Rotate arrow based on real vehicle direction
                        };
                    }





                    function clearMapElements() {
                        // Clear the Polyline
                        if (path) {
                            path.setMap(null); // Remove Polyline from the map
                            path = null;
                        }
                        // Clear the Polyline
                        if (historyPath) {
                            historyPath.setMap(null); // Remove Polyline from the map
                            historyPath = null;
                        }
                        // Clear the Polyline
                        if (historyData) {
                            historyData = [];
                        }

                        // Clear the marker
                        if (historyMarker) {
                            historyMarker.setMap(null); // Remove marker from the map
                            historyMarker = null;
                        }

                        // Clear the Start Marker
                        if (startMarker) {
                            startMarker.setMap(null); // Remove Start Marker from the map
                            startMarker = null;
                        }

                        // Clear the End Marker
                        if (endMarker) {
                            endMarker.setMap(null); // Remove End Marker from the map
                            endMarker = null;
                        }

                        // Clear all InfoWindows
                        infoWindows.forEach(infowindow => infowindow.close());
                        infoWindows = [];

                        // Clear markers for speed/time
                        if (infoMarkers.length > 0) {
                            infoMarkers.forEach(marker => marker.setMap(null));
                            infoMarkers = [];
                        }

                        // Clear stop markers
                        if (stopMarkers.length > 0) {
                            stopMarkers.forEach(marker => marker.setMap(null));
                            stopMarkers = [];
                        }

                        // Clear path coordinates
                        pathCoordinates = [];

                        // Clear existing geofences
                        drawnGeofences.forEach(function(geofence) {
                            geofence.setMap(null);
                        });
                        drawnGeofences = [];

                        // if (startRoute) {
                        //     startRoute.map = null;
                        //     startRoute = null;
                        // }
                        // if (endRoute) {
                        //     endRoute.map = null;
                        //     endRoute = null;
                        // }
                        // stopsRoute.forEach(marker => {
                        //     marker.map = null;
                        // });
                        // stopsRoute = [];
                    }


                    function initializeHistoryMarker(icon = null, color = null) {
                        let historyIcon = icon ? icon : 'default';
                        let historyColor = color ?? 'red';

                        if (historyData.length > 0) {
                            let lastPoint = historyData[historyData.length - 1];
                            let initialPosition = new google.maps.LatLng(
                                parseFloat(lastPoint.latitude),
                                parseFloat(lastPoint.longitude)
                            );

                            // Create wrapper container
                            const wrapperElement = document.createElement('div');
                            wrapperElement.className = 'relative flex items-center justify-center';

                            // Create outer ring
                            const outerRing = document.createElement('div');
                            outerRing.className = 'absolute w-[75px] h-[75px] rounded-full';
                            outerRing.style.background = 'rgba(245, 69, 25, 0.2)';
                            outerRing.style.animation = 'spin 5s linear infinite';

                            // Create inner ring
                            const innerRing = document.createElement('div');
                            innerRing.className = 'absolute w-[65px] h-[65px] rounded-full';
                            innerRing.style.border = '2px dashed #F54619';
                            innerRing.style.animation = 'spin 5s linear infinite reverse';

                            // Main vehicle icon
                            const iconImg = document.createElement('img');
                            iconImg.src = `{{ asset('assets/images/icons/${historyIcon}/${historyColor}.png') }}`;
                            iconImg.className = 'h-[55px] w-auto z-10';
                            iconImg.style.transform = `rotate(${lastPoint.angle || 0}deg)`;

                            // Add animation styles
                            const style = document.createElement('style');
                            style.textContent = `
                                @keyframes spin {
                                    from { transform: rotate(0deg); }
                                    to { transform: rotate(360deg); }
                                }
                            `;
                            document.head.appendChild(style);

                            // Assemble the marker elements
                            wrapperElement.appendChild(outerRing);
                            wrapperElement.appendChild(innerRing);
                            wrapperElement.appendChild(iconImg);

                            // Initialize AdvancedMarkerElement
                            historyMarker = new google.maps.marker.AdvancedMarkerElement({
                                map: map,
                                position: initialPosition,
                                content: wrapperElement
                            });

                            // Set initial map view
                            map.panTo(initialPosition);
                            map.setZoom(16);

                            let fullTime = lastPoint?.last_update;
                            if (document.getElementById('currentTime')) {
                                document.getElementById('currentTime').innerText = fullTime;
                            }
                        }
                    }



                    let currentHistoryIndex = 0;
                    let playInterval = null;
                    let isPlaying = false;

                    function playPause() {
                        if (playInterval) {
                            clearInterval(playInterval);
                            playInterval = null;
                            isPlaying = false;
                            document.getElementById('playIcon').classList.remove('hidden');
                            document.getElementById('pauseIcon').classList.add('hidden');
                            document.getElementById('buttonText').textContent = "@lang('translations.play')";
                        } else {
                            playInterval = setInterval(moveHistoryMarker, 800); // Move every 500ms
                            isPlaying = true;
                            document.getElementById('playIcon').classList.add('hidden');
                            document.getElementById('pauseIcon').classList.remove('hidden');
                            document.getElementById('buttonText').textContent = "@lang('translations.pause')";
                        }
                    }

                    // Update marker position based on seekbar change
                    function seekToPosition(e) {
                        if (document.getElementById('seekbar')) {

                            currentHistoryIndex = Math.floor((e / 100) * (historyData.length - 1));
                            let point = historyData[currentHistoryIndex];
                            if (point?.latitude && point?.longitude) {
                                let markerPosition = new google.maps.LatLng(parseFloat(point.latitude), parseFloat(point
                                    .longitude));

                                if (historyMarker) {
                                    historyMarker.position = markerPosition; // Update the position directly

                                    let ignitionStatus = point["239"]; // 1 for 'On', 0 for 'Off'
                                    let movementStatus = point["240"]; // 1 for 'On', 0 for 'Off'
                                    let speed = point?.speed; // 1 for 'On', 0 for 'Off'
                                    let angle = point.angle;

                                    // Determine icon color
                                    let iconColor = 'red';
                                    if (movementStatus == 1 && speed > 1) {
                                        iconColor = 'green';
                                    } else if (movementStatus == 0 && ignitionStatus == 1) {
                                        iconColor = 'yellow';
                                    }



                                    // Create wrapper container
                                    const wrapperElement = document.createElement('div');
                                    wrapperElement.className = 'relative flex items-center justify-center';

                                    // Create outer ring
                                    const outerRing = document.createElement('div');
                                    outerRing.className = 'absolute w-[75px] h-[75px] rounded-full';
                                    outerRing.style.background = 'rgba(245, 69, 25, 0.2)';
                                    outerRing.style.animation = 'spin 5s linear infinite';

                                    // Create inner ring
                                    const innerRing = document.createElement('div');
                                    innerRing.className = 'absolute w-[65px] h-[65px] rounded-full';
                                    innerRing.style.border = '2px dashed #F54619';
                                    innerRing.style.animation = 'spin 5s linear infinite reverse';

                                    // Main vehicle icon
                                    const iconImg = document.createElement('img');
                                    iconImg.src =
                                        `{{ asset('assets/images/icons/${seletedVehicleIcon}/${iconColor}.png') }}`;
                                    iconImg.className = 'h-[55px] w-auto z-10';
                                    iconImg.style.transform = `rotate(${angle || 0}deg)`;

                                    // Add animation styles
                                    const style = document.createElement('style');
                                    style.textContent = `
                                @keyframes spin {
                                    from { transform: rotate(0deg); }
                                    to { transform: rotate(360deg); }
                                }
                            `;
                                    document.head.appendChild(style);

                                    // Assemble the marker elements
                                    wrapperElement.appendChild(outerRing);
                                    wrapperElement.appendChild(innerRing);
                                    wrapperElement.appendChild(iconImg);


                                    historyMarker.content = wrapperElement;


                                    map.panTo(markerPosition);



                                }
                            }

                            // Update additional UI elements
                            // document.getElementById('speed').innerHTML = `${point?.speed} km/h`;
                            document.getElementById('currentTime').innerText = point?.last_update;

                            // dispatch event to update speed
                            window.dispatchEvent(new CustomEvent('speed-updated', {
                                detail: point?.speed
                            }));

                            // Your existing fuel level calculation
                            let fuelLevel = 0; // default value
                            if (point && point['37'] !== undefined && point['37'] !== null) {
                                fuelLevel = point['37'] || 0;
                            } else if (point && point['87'] !== undefined && point['87'] !== null) {
                                fuelLevel = point['87'] || 0;
                            } else if (point && point['89'] !== undefined && point['89'] !== null) {
                                fuelLevel = point['89'] || 0;
                            } else if (point && point['48'] !== undefined && point['48'] !== null) {
                                fuelLevel = point['48'] || 0;
                            }

                            if (fuelLevel !== 0 && fuelLevel !== null) {
                                window.dispatchEvent(new CustomEvent('fuel-level-updated', {
                                    detail: fuelLevel
                                }));
                            }
                        }

                    }

                    function getDevicePin(ignitionStatus, movementStatus, speed, callback) {
                        // Use the cached vehicle type
                        let vehicleType = seletedVehicleIcon || 'default';
                        let color = getPinColor(ignitionStatus, movementStatus, speed);
                        let iconUrl = `{{ asset('assets/images/icons/${vehicleType}/${color}.png') }}`;
                        callback(iconUrl);

                    }

                    // Function to determine the pin color
                    function getPinColor(ignitionStatus, movementStatus, speed = 0) {
                        if (movementStatus == 1 && speed > 0) {
                            return "green";
                        } else if (ignitionStatus == 1 && movementStatus == 0) {
                            return "yellow";
                        } else {
                            if (movementStatus == 1 && ignitionStatus == 1) {
                                return "green";
                            } else {
                                return "red";
                            }
                        }
                    }


                    // Play/Pause logic and updating the marker
                    function moveHistoryMarker() {
                        if (currentHistoryIndex < historyData.length - 1) {
                            currentHistoryIndex++;
                            let point = historyData[currentHistoryIndex];
                            if (point.latitude && point.longitude) {
                                let markerPosition = new google.maps.LatLng(parseFloat(point.latitude), parseFloat(point.longitude));

                                // Move the marker
                                if (historyMarker) {
                                    historyMarker.position = markerPosition; // Update the position directly
                                    let ignitionStatus = point["239"]; // 1 for 'On', 0 for 'Off'
                                    let movementStatus = point["240"]; // 1 for 'On', 0 for 'Off'
                                    let speed = point?.speed; // 1 for 'On', 0 for 'Off'
                                    let angle = point?.angle;

                                    // Determine icon color
                                    let iconColor;
                                    if (movementStatus == 1) {
                                        iconColor = 'green';
                                    } else if (movementStatus == 0 && ignitionStatus == 1) {
                                        iconColor = 'yellow';
                                    } else {
                                        iconColor = 'red';
                                    }

                                    // Create wrapper container
                                    const wrapperElement = document.createElement('div');
                                    wrapperElement.className = 'relative flex items-center justify-center';

                                    // Create outer ring
                                    const outerRing = document.createElement('div');
                                    outerRing.className = 'absolute w-[75px] h-[75px] rounded-full';
                                    outerRing.style.background = 'rgba(245, 69, 25, 0.2)';
                                    outerRing.style.animation = 'spin 5s linear infinite';

                                    // Create inner ring
                                    const innerRing = document.createElement('div');
                                    innerRing.className = 'absolute w-[65px] h-[65px] rounded-full';
                                    innerRing.style.border = '2px dashed #F54619';
                                    innerRing.style.animation = 'spin 5s linear infinite reverse';

                                    // Main vehicle icon
                                    const iconImg = document.createElement('img');
                                    iconImg.src =
                                        `{{ asset('assets/images/icons/${seletedVehicleIcon}/${iconColor}.png') }}`;
                                    iconImg.className = 'h-[55px] w-auto z-10';
                                    iconImg.style.transform = `rotate(${angle || 0}deg)`;

                                    // Add animation styles
                                    const style = document.createElement('style');
                                    style.textContent = `
                                @keyframes spin {
                                    from { transform: rotate(0deg); }
                                    to { transform: rotate(360deg); }
                                }
                            `;
                                    document.head.appendChild(style);

                                    // Assemble the marker elements
                                    wrapperElement.appendChild(outerRing);
                                    wrapperElement.appendChild(innerRing);
                                    wrapperElement.appendChild(iconImg);


                                    historyMarker.content = wrapperElement;

                                    // Center the map on the moving marker
                                    map.panTo(markerPosition);


                                }
                            }

                            // Update the seekbar
                            updateSeekbar();
                        } else {
                            clearInterval(playInterval);
                            playInterval = null;
                        }
                    }


                    // Update the seekbar when the marker moves automatically
                    function updateSeekbar() {
                        let seekbar = document.getElementById('seekbar');
                        seekbar.value = (currentHistoryIndex / (historyData.length - 1)) * 100;

                        // Update the current time display
                        let fullTime = historyData[currentHistoryIndex]?.last_update;
                        let speed = historyData[currentHistoryIndex]?.speed;
                        document.getElementById('currentTime').innerText = fullTime;
                        // document.getElementById('speed').innerHTML = `${speed} km/h`;

                        window.dispatchEvent(new CustomEvent('speed-updated', {
                            detail: speed
                        }));


                        // Corrected fuel level calculation
                        let fuelLevel = null;
                        const currentData = historyData[currentHistoryIndex];

                        if (currentData) {
                            // Check each possible fuel level property in order of priority
                            if (currentData['37'] !== undefined && currentData['37'] !== null) {
                                fuelLevel = currentData['37'];
                            } else if (currentData['87'] !== undefined && currentData['87'] !== null) {
                                fuelLevel = currentData['87'];
                            } else if (currentData['89'] !== undefined && currentData['89'] !== null) {
                                fuelLevel = currentData['89'];
                            } else if (currentData['48'] !== undefined && currentData['48'] !== null) {
                                fuelLevel = currentData['48'];
                            }

                            // Only dispatch the event if we have a valid fuel level
                            if (fuelLevel !== null) {
                                window.dispatchEvent(new CustomEvent('fuel-level-updated', {
                                    detail: fuelLevel
                                }));
                            }
                        }
                    }

                    window.onload = initializeMap;

                    // Load the map when the API is ready
                    // initializeMap();

                    function updateGeofencesOnMap(geofencesData, editable = false) {
                        // Clear existing geofences
                        drawnGeofences.forEach(function(geofence) {
                            geofence.setMap(null);
                        });
                        drawnGeofences = [];

                        geofencesData.forEach(function(geofenceData) {
                            let geofence;

                            // Handle circle geofences
                            if (geofenceData?.geofence_data?.type === 'circle') {
                                geofence = new google.maps.Circle({
                                    center: geofenceData?.geofence_data?.geofence,
                                    radius: geofenceData?.geofence_data?.radius,
                                    fillColor: 'green',
                                    fillOpacity: 0.35,
                                    strokeColor: 'green',
                                    strokeWeight: 2,
                                    editable: editable,
                                    clickable: true,
                                });
                            }

                            // Handle polygon geofences
                            else if (geofenceData?.geofence_data?.type === 'polygon') {
                                geofence = new google.maps.Polygon({
                                    paths: geofenceData?.geofence_data?.geofence,
                                    fillColor: 'green',
                                    fillOpacity: 0.35,
                                    strokeColor: 'green',
                                    strokeWeight: 2,
                                    editable: editable,
                                    clickable: true,
                                });
                            }

                            // Handle rectangle geofences
                            else if (geofenceData?.geofence_data?.type === 'rectangle') {
                                geofence = new google.maps.Rectangle({
                                    bounds: geofenceData?.geofence_data?.geofence,
                                    fillColor: 'green',
                                    fillOpacity: 0.35,
                                    strokeColor: 'green',
                                    strokeWeight: 2,
                                    editable: editable,
                                    clickable: true,
                                });
                            }

                            // Add geofence to the map and keep track of it
                            if (geofence) {
                                geofence.setMap(map);
                                drawnGeofences.push(geofence);
                            }
                        });
                    }
                </script>

                <script async
                    src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_API_KEY') }}&callback=initializeMap&v=weekly&libraries=marker,geometry">
                </script>
            @endpush


        </div>

        <script>
            async function getAddressFromCoordinates(lat, lng) {
                let url =
                    `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=it`;

                try {
                    const response = await fetch(url, {
                        headers: {
                            'User-Agent': 'Controllone/1.0 (<EMAIL>)' // Set a custom user-agent
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data && data.display_name) {
                        return data.display_name; // Return the address
                    } else {
                        console.error('No address found for the given coordinates.');
                        return null;
                    }
                } catch (error) {
                    console.error('Error fetching address:', error);
                    return null;
                }
            }
        </script>
        @script
            <script>
                $wire.on('updateMapData', (data) => {
                    setTimeout(() => {
                        vehicleData = data.data;
                        updateMarkers(vehicleData);
                    }, 100);
                });
                $wire.on('update-geofences-on-map', (event) => {
                    updateGeofencesOnMap(event.geofences);
                });
                $wire.on('show-vehicle-route', (event) => {
                    initializeRoute(event.route);
                });
                $wire.on('update-mode', ({
                    liveMode,
                    data
                }) => {
                    clearMapElements();

                    if (liveMode) {
                        historyData = [];

                        if (selectedVehicleLat && selectedVehicleLng) {
                            const latitude = parseFloat(selectedVehicleLat);
                            const longitude = parseFloat(selectedVehicleLng);

                            if (!latitude || !longitude) return;

                            const position = {
                                lat: parseFloat(latitude),
                                lng: parseFloat(longitude)
                            };

                            // Pan and zoom to the position
                            map.panTo(position);
                            map.setZoom(18);
                        }
                    } else {
                        // Handle history mode
                        if (data && data.length > 0) {
                            historyData = data;
                            plotPathOnMap();
                            initializeHistoryMarker(seletedVehicleIcon, 'red');

                            let fullTime = historyData[historyData.length - 1]?.last_update;
                            if (document.getElementById('currentTime')) {
                                document.getElementById('currentTime').innerText = fullTime;
                            }

                            if (document.getElementById('seekbar')) {
                                let seekbar = document.getElementById('seekbar');
                                seekbar.value = (historyData.length - 1);
                            }
                        }
                    }
                });
            </script>
        @endscript
    @endcan
</div>
