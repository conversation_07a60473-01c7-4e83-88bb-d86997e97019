<div class="p-4 text-sm">

    <div>
        <h1 class="text-xl font-medium tracking-wide font-poppins text-slate-800 dark:text-slate-200">
            @lang('translations.settings')
        </h1>
        <p class="mt-2 text-slate-600 dark:text-slate-400">
            @lang('translations.manage_settings')
        </p>
    </div>

    <section
        class="container flex flex-col w-full gap-4 p-5 mx-auto mt-10 bg-white rounded-lg shadow dark:bg-slate-800 md:flex-row grow">

        <div class="md:w-[30%] w-full">
            <button class="flex items-center w-full gap-2 p-2 rounded-md text-primary bg-primary/10 ps-4">
                <svg wire:ignore xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="size-5">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                </svg>

                General Settings
            </button>
            {{-- <button
                class="flex items-center w-full gap-2 p-2 mt-3 transition-all duration-300 rounded-md text-slate-800 dark:text-slate-400 ps-4 hover:text-primary hover:bg-primary/10">
                <svg wire:ignore xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                    stroke-width="1.5" stroke="currentColor" class="size-5">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M12 9v3.75m0-10.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.75c0 5.592 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.57-.598-3.75h-.152c-3.196 0-6.1-1.25-8.25-3.286Zm0 13.036h.008v.008H12v-.008Z" />
                </svg>


                Security
            </button>
            <button
                class="flex items-center w-full gap-2 p-2 mt-3 transition-all duration-300 rounded-md text-slate-800 dark:text-slate-400 ps-4 hover:text-primary hover:bg-primary/10">
                <svg wire:ignore xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                    stroke-width="1.5" stroke="currentColor" class="size-6">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M5.25 14.25h13.5m-13.5 0a3 3 0 0 1-3-3m3 3a3 3 0 1 0 0 6h13.5a3 3 0 1 0 0-6m-16.5-3a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3m-19.5 0a4.5 4.5 0 0 1 .9-2.7L5.737 5.1a3.375 3.375 0 0 1 2.7-1.35h7.126c1.062 0 2.062.5 2.7 1.35l2.587 3.45a4.5 4.5 0 0 1 .9 2.7m0 0a3 3 0 0 1-3 3m0 3h.008v.008h-.008v-.008Zm0-6h.008v.008h-.008v-.008Zm-3 6h.008v.008h-.008v-.008Zm0-6h.008v.008h-.008v-.008Z" />
                </svg>


                Server
            </button> --}}
        </div>
        <div class="w-full border-l ps-4 border-l-gray-300 min-h-80">
            <div class="flex items-center gap-3">
                <p class="w-32 text-gray-700 dark:text-slate-300">Enable Dark Mode</p>

                <label for="toggleFour" class="flex items-center cursor-pointer select-none"
                    @click.prevent="toggleDarkMode">
                    <div class="relative">
                        <input type="checkbox" id="toggleFour" class="sr-only peer" :checked="darkMode" />
                        <div class="block bg-gray-300 rounded-full h-7 w-14 dark:bg-gray-600 peer-checked:bg-primary">
                        </div>
                        <div
                            class="absolute flex items-center justify-center w-5 h-5 transition-all duration-300 bg-white rounded-full left-1 top-1 peer-checked:right-1 peer-checked:left-auto">
                        </div>
                    </div>
                </label>
            </div>




            <div class="flex items-center gap-3 mt-4">
                <p class="w-32 text-gray-700 dark:text-slate-300">Language</p>
                <select wire:model.live="currentLanguage"
                    class="w-full max-w-xs px-3 py-3 mt-2 border rounded-lg shadow-sm border-slate-200 dark:bg-slate-900 dark:text-slate-200 dark:border-slate-500 focus:outline-none focus:border-primary hover:shadow">
                    <option value="">@lang('translations.select')</option>
                    @foreach ($languages as $shortCode => $language)
                        <option @if ($currentLanguage == $shortCode) selected @endif value="{{ $shortCode }}">
                            {{ $language['name'] }}</option>
                    @endforeach
                </select>

            </div>

        </div>

    </section>


</div>
