<div class="p-4 text-sm">

    <div class="justify-between md:flex">
        <div>
            <h1 class="text-xl font-medium tracking-wide font-poppins text-slate-800 dark:text-slate-100">
                @lang('translations.roles_management')
            </h1>
            <p class="mt-2 text-slate-600 dark:text-slate-300">
                @lang('translations.add_edit_assign_roles')
            </p>
        </div>
        <div class="flex justify-end mt-3 h-fit md:mt-0">
            <button wire:click="addRole"
                class="flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="w-5 h-5">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>

                <span>@lang('translations.add_role')</span>
            </button>
        </div>


    </div>

    <section class="w-full mx-auto mt-4">

        <div class="flex items-center flex-grow gap-4 md:justify-end">

            <div class="w-full max-w-xs">
                <div class="flex items-center w-full space-x-5">
                    <div
                        class="flex w-full p-3 space-x-2 text-sm bg-gray-100 border border-transparent rounded-lg shadow focus-within:border-gray-300 dark:bg-slate-800 dark:text-slate-300 dark:focus-within:border-slate-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0 w-5 h-5 opacity-30" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <input wire:model.live.debounce.500ms="search" class="bg-transparent outline-none"
                            type="search" placeholder="@lang('translations.search')" />
                    </div>
                </div>
            </div>

            <div class="w-fit">
                <select wire:model.live="status" class="bg-transparent outline-none dark:text-slate-200">
                    <option selected>@lang('translations.status')</option>
                    <option value="1">@lang('translations.active')</option>
                    <option value="0">@lang('translations.inactive')</option>
                </select>
            </div>
        </div>



        <div class="w-full mt-4 mb-20 overflow-hidden bg-white rounded-lg shadow dark:bg-slate-800">

            <div class="w-full mb-5 overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr
                            class="text-xs font-semibold tracking-wide text-left text-gray-600 uppercase bg-gray-100 dark:bg-slate-700 dark:text-slate-300">
                            <th class="px-4 py-3 whitespace-nowrap">@lang('translations.role')</th>
                            <th class="px-4 py-3 whitespace-nowrap">@lang('translations.permissions')</th>
                            <th class="px-4 py-3 whitespace-nowrap text-end md:min-w-28 min-w-40">@lang('translations.actions')
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-slate-800">
                        @forelse ($roles as $role)
                            <tr class="text-gray-700 dark:text-slate-300">
                                <td class="px-4 py-3">
                                    <div>
                                        <p class="text-sm font-semibold text-black dark:text-slate-300">
                                            {{ $role->name ?? '' }}
                                        </p>
                                    </div>
                                </td>


                                <td class="px-4 py-3 text-sm">
                                    <div x-data="{ open: false }" class="relative w-fit">
                                        <div class="flex flex-wrap items-center gap-1">
                                            <!-- Show only first 3 permissions -->
                                            @foreach ($role->permissions->take(5) as $permission)
                                                <span class="px-2 py-1 text-xs text-white rounded-full bg-primary/60">
                                                    @lang('translations.' . $permission->name)
                                                </span>
                                            @endforeach

                                            <!-- Show more button -->
                                            @if ($role->permissions->count() > 5)
                                                <button @click="open = !open"
                                                    class="px-2 py-1 text-xs text-white bg-gray-600 rounded-full hover:bg-gray-700">
                                                    +{{ $role->permissions->count() - 5 }} more
                                                </button>

                                                <!-- Dropdown -->
                                                <div x-show="open" @click.away="open = false"
                                                    class="absolute right-0 z-10 w-48 p-2 mt-1 overflow-auto bg-white border rounded-lg shadow-md dark:bg-slate-700 max-h-32">
                                                    <ul class="space-y-1">
                                                        @foreach ($role->permissions as $permission)
                                                            <li
                                                                class="px-2 py-1 text-sm text-gray-700 bg-gray-100 rounded dark:bg-slate-800 dark:text-slate-200">
                                                                @lang('translations.' . $permission->name)
                                                            </li>
                                                        @endforeach
                                                    </ul>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </td>


                                <td class="px-4 py-3 text-sm">
                                    <div class="flex items-center justify-end gap-2">


                                        {{-- <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="showRecord({{ $role->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5"
                                                    src="{{ asset('assets/images/icons/eye-icon.svg') }}"
                                                    alt="Edit">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                @lang('translations.view')
                                            </div>
                                        </div> --}}

                                        <!-- Edit Button with Tooltip -->
                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="editRecord({{ $role->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5" src="{{ asset('assets/images/icons/edit.svg') }}"
                                                    alt="Edit">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                @lang('translations.edit')
                                            </div>
                                        </div>

                                        <!-- Delete Button with Tooltip -->
                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="deleteRecordConfirmation({{ $role->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5"
                                                    src="{{ asset('assets/images/icons/delete.svg') }}"
                                                    alt="Delete">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                @lang('translations.delete')
                                            </div>
                                        </div>



                                    </div>

                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="px-4 py-3 text-center">@lang('translations.no_record_found')
                                </td>
                            </tr>
                        @endforelse

                    </tbody>
                </table>
            </div>

            {{ $roles->links('livewire.components.pagination') }}

        </div>
    </section>

    <x-modal name="manage-role">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black dark:text-slate-100">
                        @if ($recordId)
                            @lang('translations.edit_role')
                        @else
                            @lang('translations.add_role')
                        @endif
                    </h2>
                </div>


                <div class="mt-4">

                    <div class="grid grid-cols-2 gap-4">
                        <div class="col-span-2">
                            <label class="text-sm text-[#414651] dark:text-slate-200">@lang('translations.name')</label>

                            <div class="relative mt-2">
                                <input type="text" wire:model="name" id="name"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-900 dark:border-slate-600 dark:focus:border-primary"
                                    placeholder="@lang('translations.enter') @lang('translations.name')">
                            </div>

                            @error('name')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>


                        <div class="col-span-2">
                            <label class="text-sm text-[#414651] dark:text-slate-200">@lang('translations.permissions')</label>
                            <div class="grid grid-cols-2 gap-1 mt-2">
                                @foreach ($allPermissions as $permission)
                                    <label wire:key="permission-{{ $permission->name }}"
                                        class="flex items-center gap-2">
                                        <input type="checkbox" value="{{ $permission->name }}"
                                            wire:model="permissions" id="permission-{{ $permission->name }}"
                                            class="accent-primary">
                                        @lang('translations.' . $permission->name)

                                    </label>
                                @endforeach

                            </div>
                        </div>


                    </div>

                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false;"
                            class="modal-close text-[#414651] dark:text-slate-400 p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            @lang('translations.cancel')
                        </button>

                        <button wire:click="addUpdateRole" wire:loading.attr="disabled" wire:target="addUpdateRole"
                            type="button"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-secondary transition-all duration-300">
                            <span wire:loading.remove wire:target="addUpdateRole"> Save
                            </span>
                            <div wire:loading wire:target="addUpdateRole">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

            </div>
        </x-slot:body>
    </x-modal>

    <x-modal name="delete-record-modal">
        <x-slot:body>

            <div class="flex items-center justify-between">
                <img class="size-10" src="{{ asset('assets/images/icons/delete.svg') }}" alt="delete">

                <button @click="show = false" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>

            </div>

            <div class="mt-4">
                <h3 class="font-medium dark:text-slate-100">
                    @lang('translations.confirm_delete_record')
                </h3>

                <p class="text-sm mt-2 text-[#535862] dark:text-slate-300">
                    @lang('translations.delete_warning')
                </p>


                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false"
                        class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm dark:border-slate-400 dark:text-slate-400">
                        @lang('translations.cancel')
                    </button>
                    <button wire:click="deleteRecord"
                        class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                        @lang('translations.delete')
                    </button>
                </div>
            </div>


        </x-slot:body>
    </x-modal>


    {{-- add client modal --}}
    {{-- <x-modal name="show-record">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">
                <h2 class="col-span-2 text-lg font-semibold text-center">
                    @lang('translations.user_details')
                </h2>

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="mt-4">

                    <div class="grid gap-4 md:grid-cols-2">
                        @if ($selectedRecord)
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-300">@lang('translations.name')</label>
                                <p class="text-sm text-gray-800 dark:text-gray-100">{{ $selectedRecord->name }}
                                </p>
                            </div>



                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-300">@lang('translations.email')</label>
                                <p class="text-sm text-primary"><a
                                        href="mailto:{{ $selectedRecord->email }}">{{ $selectedRecord->email }}</a>
                                </p>
                            </div>
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-300">@lang('translations.role')</label>
                                <p class="text-sm text-gray-800 capitalize dark:text-gray-100">
                                    {{ $selectedRecord->role }}</p>
                            </div>

                            <div class="col-span-2">
                                <label class="text-sm text-gray-600 dark:text-slate-300">@lang('translations.permissions')</label>
                                <p
                                    class="flex flex-wrap mt-2 text-xs capitalize text-primary dark:text-gray-100 gap-x-2 gap-y-3">
                                    @foreach ($selectedRecord->permissions as $permission)
                                        <span class="px-2 py-1 rounded-full bg-primary/10 dark:bg-primary/40 w-fit">
                                            {{ $permission->description }}
                                        </span>
                                    @endforeach
                                </p>
                            </div>


                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-300">@lang('translations.created_at')</label>
                                <p class="text-sm text-gray-800 dark:text-gray-100">
                                    {{ $selectedRecord->created_at->format('d-m-Y H:i') }}</p>
                            </div>

                            <div class="col-span-2">
                                <label class="text-sm text-gray-600 dark:text-slate-300">@lang('translations.active')</label>
                                <p class="mt-3 text-sm text-gray-800 dark:text-gray-100">
                                    @if ($selectedRecord->is_active == 1)
                                        <span
                                            class="px-2 py-1 font-medium leading-tight rounded-full text-emerald-700 bg-emerald-100">
                                            Active </span>
                                    @else
                                        <span
                                            class="px-2 py-1 font-medium leading-tight rounded-full text-rose-700 bg-rose-100">
                                            Inactive </span>
                                    @endif
                                </p>
                            </div>
                        @else
                            <p>@lang('translations.no_user_found')</p>
                        @endif
                    </div>


                </div>

            </div>
        </x-slot:body>
    </x-modal> --}}




</div>
