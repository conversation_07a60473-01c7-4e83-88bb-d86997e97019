<div class="relative flex flex-col-reverse h-auto min-h-[80vh] text-sm md:h-full overflow-y-scroll md:flex-row grow">
    @can('remote_control')
        {{-- sidebar --}}
        <div
            class="2xl:p-5 p-3 bg-white dark:bg-slate-800 shadow-[5px_0px_5px_rgba(0,0,0,0.1)] overflow-y-scroll h-full md:w-[40%]">
            <h2 class="text-lg font-semibold text-slate-800 dark:text-slate-100">
                @lang('translations.vehicle_list')
            </h2>

            <label
                class="flex items-center mt-3 text-sm border rounded-sm ps-2 border-slate-300 focus-within:border-slate-400 text-slate-700 dark:text-slate-300">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="size-5">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                </svg>
                <input type="text" wire:model.live.debounce.500ms="search"
                    class="px-2 py-2 bg-transparent border-none outline-none" placeholder="@lang('translations.search')">
            </label>

            <div class="flex flex-wrap gap-2 mt-3 text-xs">
                <!-- All Tab -->
                <button wire:click="setFilter('all')"
                    class="px-3 py-2 text-gray-500 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-300 @if ($filter == 'all') ring-2 ring-red-300 border-primary text-primary @endif">
                    @lang('translations.all')
                </button>

                <!-- Moving Tab -->
                <button wire:click="setFilter('moving')"
                    class="flex items-center px-3 py-2 space-x-2 text-gray-800 border border-gray-300 rounded-lg dark:text-slate-300 hover:border-green-500 hover:text-green-500 focus:outline-none focus:ring-2 focus:ring-green-300  @if ($filter == 'moving') border-green-300 ring-2 ring-green-300 @endif">
                    <span class="bg-green-500 rounded-full size-2"></span>
                    <span>@lang('translations.moving')</span>
                </button>

                <!-- Stopped Tab -->
                <button wire:click="setFilter('stopped')"
                    class="flex items-center px-3 py-2 space-x-2 text-gray-800 border border-gray-300 rounded-lg dark:text-slate-300 hover:border-yellow-500 hover:text-yellow-500 focus:outline-none focus:ring-2 focus:ring-yellow-300  @if ($filter == 'stopped') ring-2 ring-yellow-300 border-yellow-300 @endif">
                    <span class="bg-yellow-500 rounded-full size-2"></span>
                    <span>@lang('translations.stopped')</span>
                </button>

                <!-- Parked Tab -->
                <button wire:click="setFilter('parked')"
                    class="flex items-center px-3 py-2 space-x-2 text-gray-800 border border-gray-300 rounded-lg dark:text-slate-300 hover:border-red-500 hover:text-red-500 focus:outline-none focus:ring-2 focus:ring-red-300  @if ($filter == 'parked') ring-2 ring-red-300 border-red-300 @endif">
                    <span class="bg-red-500 rounded-full size-2"></span>
                    <span>@lang('translations.parked')</span>
                </button>
            </div>


            <div class="mt-3 text-gray-500 dark:text-gray-300">
                @lang('translations.vehicles') {{ count($devices) ?? 0 }}
            </div>
            @foreach ($devices as $type => $vehicles)
                <div x-data="{ show: true }">
                    <h3 @click="show =!show"
                        class="flex items-center gap-2 mt-3 text-base font-semibold cursor-pointer select-none dark:text-gray-100">
                        <svg width="12" height="13" class="transition-all duration-300"
                            :class="show ? 'rotate-0' : 'rotate-180'" viewBox="0 0 12 13" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2.89999L0 9.29999H12L6 2.89999Z" fill="currentColor" />
                        </svg>
                        @lang('translations.' . $type)
                        <img class="size-5" src="{{ asset('assets/images/icons/' . ($type ?? 'default') . '.svg') }}"
                            alt="icon">
                    </h3>
                    <div x-show="show" x-cloak class="mt-3 space-y-2">

                        @foreach ($vehicles as $vehicle)
                            <!-- List Item  -->
                            <div wire:click="selectDevice({{ $vehicle->id }})"
                                class="flex items-end justify-between pb-4 p-3 cursor-pointer border-b border-gray-200 @if ($vehicle->id == $selectedDevice?->id) bg-primary/10 border-l-4 border-l-primary @else dark:hover:bg-slate-900 hover:bg-gray-100 @endif">
                                <!-- Left Section -->
                                <div class="flex items-center space-x-4">
                                    <!-- Status Dot -->
                                    @isset($vehicle->movement_status)
                                        @if ($vehicle->movement_status == 1)
                                            <span class="bg-green-500 rounded-full size-2"></span>
                                        @elseif ($vehicle->movement_status == 0 && $vehicle->ignition_status == 1)
                                            <span class="bg-yellow-500 rounded-full size-2"></span>
                                        @elseif ($vehicle->movement_status == 0)
                                            <span class="bg-red-500 rounded-full size-2"></span>
                                        @endif
                                    @endisset
                                    <!-- Content -->
                                    <div>
                                        <p class="text-base font-semibold text-gray-900 dark:text-gray-200">
                                            {{ $vehicle->license_plate }}</p>
                                        <div class="flex items-center mt-1 space-x-1 text-gray-600 dark:text-slate-300">
                                            <span class="inline-flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path
                                                        d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-3.31 0-6 2.69-6 6h12c0-3.31-2.69-6-6-6z">
                                                    </path>
                                                </svg>
                                            </span>
                                            <span class="text-xs">{{ $vehicle->driver?->name }}</span>
                                        </div>
                                    </div>
                                </div>

                                <p class="text-[10px] text-gray-500 dark:text-slate-400">
                                    {{ isset($vehicle->timestamp) ? parseFlexibleTimestamp($vehicle->timestamp)->diffForHumans() : '' }}
                                </p>
                            </div>
                        @endforeach

                    </div>
                </div>
            @endforeach

        </div>
    @endcan

    @if ($selectedDevice)
        {{-- pin management --}}
        <div class="w-full p-5 overflow-hidden">
            <h2 class="text-lg font-medium tracking-wide dark:text-slate-100">
                @lang('translations.vehicle_details')
            </h2>

            <div class="grid gap-3 p-3 mt-5 bg-gray-100 rounded-lg shadow dark:bg-slate-800 md:grid-cols-2">
                <div class="grid md:grid-cols-2">
                    <div class="text-gray-500 dark:text-slate-300">
                        @lang('translations.license_plate')
                    </div>
                    <div class="font-semibold text-slate-800 dark:text-slate-100">
                        {{ $selectedDevice->license_plate }}
                    </div>
                </div>
                <div class="grid md:grid-cols-2 ">
                    <div class="text-gray-500 dark:text-slate-300">
                        @lang('translations.type')
                    </div>
                    <div class="font-semibold text-slate-800 dark:text-slate-100">

                        @lang('translations.' . $selectedDevice->type ?? 'type')

                    </div>
                </div>
                <hr class="col-span-2">
                <div class="grid md:grid-cols-2 ">
                    <div class="text-gray-500 dark:text-slate-300">
                        @lang('translations.driver_name')
                    </div>
                    <div class="font-semibold text-slate-800 dark:text-slate-100">
                        {{ $selectedDevice->driver?->name }}

                    </div>
                </div>
                <div class="grid md:grid-cols-2 ">
                    <div class="text-gray-500 dark:text-slate-300">
                        @lang('translations.lock_status')
                    </div>
                    <div class="flex items-center gap-2 font-semibold text-slate-800 dark:text-slate-100">
                        <span class="bg-green-500 rounded-full size-2"></span>
                        <span>@lang('translations.unlocked')</span>
                    </div>
                </div>
            </div>

            <h2 class="mt-5 text-lg font-medium tracking-wide dark:text-slate-200">
                @lang('translations.pin_entry')
            </h2>

            <div class="flex items-center gap-3 mt-3">


                <div x-data="{
                    showPassword: false,
                    pin: '{{ $pin ?? null }}',
                    generatePassword() {
                        // Function to generate a random password (8 characters with letters and digits)
                        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                        let generatedPassword = '';
                        for (let i = 0; i < 5; i++) {
                            generatedPassword += chars.charAt(Math.floor(Math.random() * chars.length));
                        }
                        this.pin = generatedPassword;
                        // Set the generated password to Livewire model
                        @this.set('pin', generatedPassword);
                    }
                }" class="relative mt-2">

                    <!-- Password Input Field -->
                    <input x-bind:type="showPassword ? 'text' : 'password'" id="pin" x-model="pin"
                        wire:model="pin" placeholder="@lang('translations.enter_pin')"
                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                           rounded-lg text-sm focus:border-primary outline-none transition-all
                           duration-300 dark:text-slate-300 dark:bg-slate-900 dark:border-slate-600 dark:focus:border-primary">

                    <!-- Generate Password Button -->
                    <button type="button" @click="generatePassword"
                        class="absolute top-0 bottom-0 transition-all duration-300 rounded text-primary right-5">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.8"
                            stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 0 0-3.7-3.7 48.678 48.678 0 0 0-7.324 0 4.006 4.006 0 0 0-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 0 0 3.7 3.7 48.656 48.656 0 0 0 7.324 0 4.006 4.006 0 0 0 3.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3-3 3" />
                        </svg>
                    </button>

                    <!-- password show hide button -->
                    <div class="absolute z-10 cursor-pointer top-3 right-14" @click="showPassword = !showPassword">
                        <img x-show="!showPassword" id="show-icon" class="size-5"
                            src="{{ asset('assets/images/eye.svg') }}" alt="Show password">
                        <img x-show="showPassword" id="hide-icon" class="size-5"
                            src="{{ asset('assets/images/eye-closed.svg') }}" alt="Hide password">
                    </div>
                </div>





                @can('remote_control_pin_manage')
                    <button wire:click="checkPin"
                        class="px-4 py-2 text-white transition-all duration-300 bg-black rounded-md hover:bg-black/80 dark:bg-slate-800 dark:hover:bg-slate-700">@lang('translations.submit')</button>
                @endcan
            </div>
            @error('pin')
                <div class="mt-2 text-xs text-red-500">
                    {{ $message }}
                </div>
            @enderror

            @can('remote_control_pin_manage')
                <label class="flex items-center gap-2 mt-3 text-gray-500 dark:text-gray-400">
                    <input type="checkbox" wire:model="local_lock_ibutton" class="accent-primary">
                    @lang('translations.enable_local_lock')
                </label>

                <button wire:click="updatePin"
                    class="px-4 py-2 mt-4 text-white transition-all duration-300 rounded-md bg-primary hover:bg-primaryDark">@lang('translations.save_changes')</button>
            @endcan

            @if ($can_lock_unlock)
                <h2 class="mt-5 text-lg font-medium tracking-wide dark:text-slate-200">
                    @lang('translations.lock_unlock')
                </h2>

                <div x-data="{ isLocked: false }" class="mt-3">
                    <!-- Toggle Button -->
                    <div @click="isLocked = !isLocked"
                        class="flex items-center h-8 px-1 rounded-full cursor-pointer w-14"
                        :class="isLocked ? 'bg-red-100 border-2 border-red-500' :
                            'bg-green-100 border-2 border-green-500'">
                        <!-- Icon -->
                        <img class="select-none size-6" :class="isLocked ? 'me-auto' : 'ms-auto'"
                            :src="isLocked ? '{{ asset('assets/images/icons/locked.svg') }}' :
                                '{{ asset('assets/images/icons/unlocaked.svg') }}'"
                            alt="icon">
                    </div>
                </div>
            @endif


        </div>
    @endif


</div>
