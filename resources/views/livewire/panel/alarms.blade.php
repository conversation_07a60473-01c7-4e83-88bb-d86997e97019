

<div class="p-4 text-sm">

    <div class="justify-between md:flex">
        <div>
            <h1 class="text-xl font-medium tracking-wide font-poppins text-slate-800 dark:text-slate-100">
                @lang('translations.alarms')
            </h1>
            <p class="mt-2 text-slate-600 dark:text-slate-300">
                @lang('translations.alarms_details')
            </p>
        </div>



    </div>



    <section class="w-full mx-auto mt-4">

        <div class="grid gap-4 mt-5 md:grid-cols-5 grow">

            <div>
                <label class="block mb-2.5 dark:text-slate-300">@lang('translations.select_vehicles')</label>
                <livewire:components.select-dropdown placeholder="{{ __('translations.select_vehicle') }}"
                    field-name="selectedVehicle" fetch-method="getVehicles" />

            </div>
            <div>
                <label class="block mb-2.5 dark:text-slate-300">@lang('translations.select_geofence')</label>
                <livewire:components.select-dropdown placeholder="{{ __('translations.select_geofence') }}"
                    field-name="selectedGeofence" fetch-method="getGeofences" />

            </div>
            <div>
                <label class="dark:text-slate-300">@lang('translations.alarm_type')</label>
                <select wire:model.live="alarmType"
                    class="w-full px-3 py-2 mt-2 border rounded-lg shadow-sm border-slate-200 focus:outline-none focus:border-primary hover:shadow dark:bg-slate-800 dark:text-slate-300 dark:border-slate-500">
                    <option value="">@lang('translations.select')</option>
                    <option value="geofence_exit">@lang('translations.geofence_out')</option>
                    <option value="geofence_in">@lang('translations.geofence_in')</option>
                    <option value="jamming">@lang('translations.jamming')</option>
                    <option value="towing">@lang('translations.towing')</option>
                    <option value="crash">@lang('translations.crash')</option>
                    <option value="signal_lost">@lang('translations.signal_lost')</option>
                    <option value="unplug">@lang('translations.unplug')</option>
                </select>
            </div>
            <div>
                <label class="dark:text-slate-300">@lang('translations.start_date')</label>
                <input type="date" wire:model.live="start_date"
                    class="w-full px-3 py-2 mt-2 border rounded-lg shadow-sm border-slate-200 focus:outline-none focus:border-primary hover:shadow dark:bg-slate-800 dark:text-slate-300 dark:border-slate-500">
            </div>

            <div>
                <label class="dark:text-slate-300">@lang('translations.end_date')</label>
                <input type="date" wire:model.live="end_date"
                    class="w-full px-3 py-2 mt-2 border rounded-lg shadow-sm border-slate-200 focus:outline-none focus:border-primary hover:shadow dark:bg-slate-800 dark:text-slate-300 dark:border-slate-500">
            </div>

        </div>



        <div class="w-full mt-4 mb-20 overflow-hidden bg-white rounded-lg shadow dark:bg-slate-800">
            <div class="w-full mb-5 overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr
                            class="text-xs font-semibold tracking-wide text-left text-gray-600 uppercase bg-gray-100 dark:bg-slate-700 dark:text-slate-300">
                            <th class="px-4 py-3 whitespace-nowrap">@lang('translations.vehicle')</th>
                            <th class="px-4 py-3 whitespace-nowrap">@lang('translations.alarm_type')</th>
                            <th class="px-4 py-3 whitespace-nowrap">@lang('translations.alarm')</th>
                            <th class="px-4 py-3 whitespace-nowrap">@lang('translations.location')</th>
                            <th class="px-4 py-3 whitespace-nowrap">@lang('translations.occurred_at')</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-slate-800 dark:text-slate-300">
                        @forelse ($alarms as $alarm)
                            <tr>
                                <td class="px-4 py-2">{{ $alarm->vehicle?->license_plate ?? $alarm->imei }}</td>
                                <td class="px-4 py-2">
                                    @if ($alarm->alarm_type == 'jamming')
                                        {{ __('translations.jamming') }}
                                    @elseif ($alarm->alarm_type == 'towing')
                                        {{ __('translations.towing') }}
                                    @elseif ($alarm->alarm_type == 'crash')
                                        {{ __('translations.crash') }}
                                    @elseif ($alarm->alarm_type == 'signal_lost')
                                        {{ __('translations.signal_lost') }}
                                    @elseif ($alarm->alarm_type == 'unplug')
                                        {{ __('translations.unplug') }}
                                    @elseif ($alarm->alarm_type == 'geofence_exit')
                                        {{ __('translations.geofence_exit') }}
                                    @elseif ($alarm->alarm_type == 'geofence_in')
                                        {{ __('translations.geofence_in') }}
                                    @endif
                                </td>

                                <td class="px-4 py-2">
                                    @if ($alarm->alarm_type == 'jamming')
                                        {{ __('translations.jamming_' . $alarm->alarm_value) }}
                                    @elseif ($alarm->alarm_type == 'towing')
                                        {{ __('translations.towing_' . $alarm->alarm_value) }}
                                    @elseif ($alarm->alarm_type == 'crash')
                                        {{ __('translations.crash_' . $alarm->alarm_value) }}
                                    @elseif ($alarm->alarm_type == 'signal_lost')
                                        {{ __('translations.signal_lost_' . $alarm->alarm_value) }}
                                    @elseif ($alarm->alarm_type == 'unplug')
                                        {{ __('translations.unplug_' . $alarm->alarm_value) }}
                                    @elseif ($alarm->alarm_type == 'geofence_exit')
                                        {{ __('translations.geofence_exit_event', ['geofence' => $alarm->geofence?->name]) }}
                                    @elseif ($alarm->alarm_type == 'geofence_in')
                                        {{ __('translations.geofence_in_event', ['geofence' => $alarm->geofence?->name]) }}
                                    @else
                                        {{ __('translations.unknown_event') }}
                                    @endif
                                </td>

                                <td class="px-4 py-2">{{ $alarm->location ?? 'N/A' }}</td>
                                <td class="px-4 py-2">{{ $alarm->created_at->format('d/m/Y H:i') }}</td>

                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="px-4 py-3 text-center">@lang('translations.no_record_found')
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>

            </div>
            <div class="py-5">
                {{ $alarms->links('livewire.components.pagination') }}
            </div>

        </div>
    </section>



</div>
