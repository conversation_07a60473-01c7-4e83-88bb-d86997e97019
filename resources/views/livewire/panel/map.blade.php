<script src="{{ asset('assets/js/markerclusterer.js') }}"></script>

<script>
    let map;
    let markers = [];
    let vehicleData;
    let selectedVehicleImei;
    let seletedVehicleIcon;
    let selectedVehicleLat;
    let selectedVehicleLng;
    let markerCluster;

    let directionsService;
    let allRoutes = []; // Store all route polylines
    let activePolyline = null; // Store the currently selected route
    let allMarkers = []; // Store all markers


    // Initialize the map asynchronously
    async function initializeMap() {
        // Check if the HTML tag has the 'dark' class
        const isDarkMode = document.documentElement.classList.contains('dark');


        const mapCenter = {
            lat: 41.9028,
            lng: 12.4964
        }; // Initial center of the map

        // Load Google Maps API and libraries
        map = new google.maps.Map(document.getElementById('map'), {
            center: mapCenter,
            zoom: 6,
            mapId: "fleetMap", // Replace with your actual Map ID if applicable
            mapTypeId: isDarkMode ? google.maps.MapTypeId.HYBRID : google.maps.MapTypeId
                .ROADMAP, // Use hybrid in dark mode for satellite with labels
        });

        directionsService = new google.maps.DirectionsService();

        const {
            AdvancedMarkerElement,
            PinElement
        } = await google.maps.importLibrary("marker");


        // Initial marker update
        updateMarkers(@json($devices), AdvancedMarkerElement);

        initializeMarkerCluster();

    }


    const emptySelected = () => {

        selectedVehicleImei = null;

        // Remove existing route paths & markers
        allRoutes.forEach(route => route.setMap(null));
        allRoutes = [];
        allMarkers.forEach(marker => marker.setMap(null));
        allMarkers = [];
    }
    // Initialize marker clustering
    function initializeMarkerCluster() {
        if (markerCluster) {
            markerCluster.clearMarkers(); // Clear existing clusters
        }

        markerCluster = new markerClusterer.MarkerClusterer({
            markers: markers,
            map: map,
            algorithm: new markerClusterer.SuperClusterAlgorithm({
                radius: 60,
                maxZoom: 16,
                minPoints: 2
            }),
            renderer: {
                render: ({
                    count,
                    position
                }) => {
                    // Determine cluster size and color
                    let size, backgroundColor, pulseSize;
                    if (count < 10) {
                        size = '40px';
                        pulseSize = '44px';
                        backgroundColor = '#F54619';
                    } else if (count < 50) {
                        size = '50px';
                        pulseSize = '54px';
                        backgroundColor = '#e0360b';
                    } else {
                        size = '60px';
                        pulseSize = '64px';
                        backgroundColor = '#cc2d06';
                    }

                    const clusterDiv = document.createElement("div");

                    // Create wrapper for pulse effect
                    const pulseWrapper = document.createElement("div");
                    pulseWrapper.style.position = "relative";
                    pulseWrapper.style.width = pulseSize;
                    pulseWrapper.style.height = pulseSize;

                    // Create pulse effect
                    const pulseEffect = document.createElement("div");
                    pulseEffect.style.position = "absolute";
                    pulseEffect.style.width = "100%";
                    pulseEffect.style.height = "100%";
                    pulseEffect.style.borderRadius = "50%";
                    pulseEffect.style.backgroundColor = backgroundColor;
                    pulseEffect.style.opacity = "0.2";
                    pulseEffect.style.animation = "pulseAnimation 2s infinite";

                    // Create main container with enhanced styles
                    const container = document.createElement("div");
                    container.style.width = size;
                    container.style.height = size;
                    container.style.backgroundColor = backgroundColor;
                    container.style.borderRadius = "50%";
                    container.style.display = "flex";
                    container.style.alignItems = "center";
                    container.style.justifyContent = "center";
                    container.style.color = "white";
                    container.style.fontWeight = "600";
                    container.style.fontSize = count > 99 ? "14px" : "16px";
                    container.style.boxShadow = "0 3px 6px rgba(0,0,0,0.3)";
                    container.style.border = "2px solid rgba(255,255,255,0.6)";
                    container.style.position = "absolute";
                    container.style.top = "50%";
                    container.style.left = "50%";
                    container.style.transform = "translate(-50%, -50%)";
                    container.style.transformOrigin = "center";
                    container.style.animation = "clusterAnimation 0.3s ease-in-out";
                    container.style.cursor = "pointer";
                    container.style.transition = "transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out";

                    // Add hover effect
                    container.onmouseenter = () => {
                        container.style.transform = "translate(-50%, -50%) scale(1.1)";
                        container.style.boxShadow = "0 5px 12px rgba(0,0,0,0.4)";
                    };
                    container.onmouseleave = () => {
                        container.style.transform = "translate(-50%, -50%) scale(1)";
                        container.style.boxShadow = "0 3px 6px rgba(0,0,0,0.3)";
                    };

                    // Create count span with enhanced styles
                    const countSpan = document.createElement("span");
                    countSpan.textContent = count > 99 ? "99+" : count;
                    countSpan.style.position = "relative";
                    countSpan.style.zIndex = "2";
                    countSpan.style.textShadow = "1px 1px 2px rgba(0,0,0,0.3)";

                    // Create gradient overlay with enhanced effect
                    const gradientOverlay = document.createElement("div");
                    gradientOverlay.style.position = "absolute";
                    gradientOverlay.style.width = "100%";
                    gradientOverlay.style.height = "100%";
                    gradientOverlay.style.borderRadius = "50%";
                    gradientOverlay.style.background =
                        "radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%)";
                    gradientOverlay.style.pointerEvents = "none";

                    // Create and add enhanced animations
                    const style = document.createElement("style");
                    style.textContent = `
    @keyframes clusterAnimation {
        from {
            transform: translate(-50%, -50%) scale(0.8);
            opacity: 0;
        }
        to {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }
    }
    @keyframes pulseAnimation {
        0% {
            transform: scale(0.95);
            opacity: 0.5;
        }
        50% {
            transform: scale(1.05);
            opacity: 0.2;
        }
        100% {
            transform: scale(0.95);
            opacity: 0.5;
        }
    }
`;

                    // Only add style if it doesn't exist
                    if (!document.getElementById('cluster-animations')) {
                        style.id = 'cluster-animations';
                        document.head.appendChild(style);
                    }

                    // Assemble the components
                    container.appendChild(countSpan);
                    container.appendChild(gradientOverlay);
                    pulseWrapper.appendChild(pulseEffect);
                    pulseWrapper.appendChild(container);
                    clusterDiv.appendChild(pulseWrapper);

                    return new google.maps.marker.AdvancedMarkerElement({
                        position,
                        content: clusterDiv,
                        zIndex: 1
                    });
                }
            }
        });
    }



    function updateMarkers(vehicleData, AdvancedMarkerElement) {
        // Clear existing markers
        markers.forEach(marker => marker.map = null);
        markers = [];

        // Loop through vehicles and add markers
        Object.keys(vehicleData).forEach(type => {
            const vehicles = vehicleData[type] || vehicleData['default'];
            vehicles.forEach(vehicle => {
                // More precise coordinate parsing
                const latitude = parseFloat(vehicle.latitude);
                const longitude = parseFloat(vehicle.longitude);
                const movementStatus = parseInt(vehicle.movement_status);
                const ignitionStatus = parseInt(vehicle.ignition_status);
                const angle = parseFloat(vehicle.angle) || 0;

                // Validate coordinates
                if (!isValidCoordinate(latitude, longitude)) return;

                // Determine icon color based on vehicle status
                let iconColor = determineVehicleStatus(movementStatus, ignitionStatus);

                // Create custom marker content with improved positioning
                const markerDiv = document.createElement('div');
                markerDiv.innerHTML = `
    <div style="position:relative; transform-origin: center bottom;">
        <img src="{{ asset('assets/images/icons/${vehicle.icon}/${iconColor}.png') }}"
             style="width: auto; height: 60px; transform: rotate(${angle}deg); 
             filter: drop-shadow(2px 4px 6px rgba(0,0,0,0.9));">

        <!-- Vehicle Info Box -->
        <div style="position:absolute; left: 50%; transform: translateX(-50%); bottom:-40px;
                    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(50, 50, 50, 0.6)); 
                    color: white; padding: 6px 10px; border-radius: 8px; font-size: 10px; 
                    font-weight: 600; box-shadow: 2px 4px 8px rgba(0, 0, 0, 0.4);
                    white-space: nowrap;">
            <div style="display:flex;align-items:center;gap:5px; margin-bottom: 3px;">
                <img src="{{ asset('assets/images/icons/plate.svg') }}"
                     style="width: 12px; height: 12px;">
                <span>${vehicle.license_plate}</span>
            </div>
            ${vehicle.driver?.name ? `
                                                                            <div style="display:flex;align-items:center;gap:5px;">
                                                                                <img src="{{ asset('assets/images/icons/driver.svg') }}"
                                                                                     style="width: 12px; height: 12px;">
                                                                                <span>${vehicle.driver.name}</span>
                                                                            </div>
                                                                        ` : ''}
        </div>
    </div>
`;

                // Create marker with improved options
                const marker = new google.maps.marker.AdvancedMarkerElement({
                    map,
                    position: {
                        lat: latitude,
                        lng: longitude
                    },
                    content: markerDiv,
                    collisionBehavior: google.maps.CollisionBehavior
                        .OPTIONAL_AND_HIDES_LOWER_PRIORITY,
                    zIndex: selectedVehicleImei === vehicle.imei ? 1000 : 1
                });

                // Add click event to marker
                marker.addListener('click', () => {
                    focusOnMarker(vehicle.imei, latitude, longitude, vehicle.icon, iconColor);
                    window.dispatchEvent(
                        new CustomEvent('show-vehicle-details', {
                            detail: vehicle,
                        })
                    );
                });

                // Update selected vehicle position if in live mode
                let liveMode = document.getElementById('liveMode').checked;
                if (liveMode && selectedVehicleImei === vehicle.imei) {
                    map.panTo({
                        lat: latitude,
                        lng: longitude
                    });
                }

                markers.push(marker);
            });
        });

    }

    // Helper functions
    function isValidCoordinate(lat, lng) {
        return !isNaN(lat) && !isNaN(lng) &&
            lat !== 0 && lng !== 0 &&
            lat >= -90 && lat <= 90 &&
            lng >= -180 && lng <= 180;
    }

    function determineVehicleStatus(movement, ignition) {
        if (movement === 1 && ignition === 1) return 'green';
        if (movement === 0 && ignition === 1) return 'yellow';
        return 'red';
    }



    function focusOnMarker(imei, latitude, longitude, icon = null, color = null) {
        selectedVehicleImei = imei;
        seletedVehicleIcon = icon;
        selectedVehicleLat = latitude;
        selectedVehicleLng = longitude;

        updateMode();

        if (!latitude || !longitude) return;

        const position = {
            lat: parseFloat(latitude),
            lng: parseFloat(longitude)
        };

        // Pan and zoom to the position
        map.panTo(position);
        map.setZoom(20);

        // Clear previous routes
        allRoutes.forEach(route => route.setMap(null));
        allRoutes = [];
        allMarkers.forEach(marker => marker.setMap(null));
        allMarkers = [];

        if (vehicleData && vehicleData.length) {
            // Find the selected vehicle's data
            let vehicle = Object.values(vehicleData).flat().find(v => v.imei === imei);

            if (vehicle && vehicle.current_route) {
                let {
                    start_point,
                    start_point_lat,
                    start_point_lng,
                    start_point_status,
                    end_point,
                    end_point_lat,
                    end_point_lng,
                    end_point_status,
                    route,
                    stops
                } = vehicle.current_route;

                if (!start_point_lat || !start_point_lng || !end_point_lat || !end_point_lng) return;

                let start = {
                    lat: parseFloat(start_point_lat),
                    lng: parseFloat(start_point_lng)
                };
                let end = {
                    lat: parseFloat(end_point_lat),
                    lng: parseFloat(end_point_lng)
                };

                // Define the arrow icon with a white fill
                let arrowSymbol = {
                    path: "M 0,3 L 5,8 L -5,8 L 0,3 M 5,8 L -5,8", // Custom filled arrow shape
                    fillColor: "#ffffff", // White fill
                    fillOpacity: 1, // Fully filled
                    strokeColor: "#ff0000", // Red border
                    strokeWeight: 2,
                    scale: 2 // Adjust size
                };

                // Draw Route Path with Stylish Arrows
                let savedPolyline = new google.maps.Polyline({
                    path: route,
                    strokeColor: "#ff0000", // Red border
                    strokeOpacity: 1, // No transparency for solid border
                    strokeWeight: 0, // Thicker line
                    map: map,
                    icons: [{
                        icon: arrowSymbol,
                        offset: "1%", // First arrow at 10% of route
                        repeat: "4%" // Repeat arrows every 15% of the route length
                    }]
                });

                allRoutes.push(savedPolyline);

                // Function to add marker with custom icons & info window
                function addMarker(position, label, status, type, pointName) {
                    let iconUrl = "";

                    if (type === "start") {
                        iconUrl = status === "completed" ? "{{ asset('assets/images/icons/start-green.svg') }}" :
                            "{{ asset('assets/images/icons/start-red.svg') }}";
                    } else if (type === "stop") {
                        iconUrl = status === "completed" ? "{{ asset('assets/images/icons/stop-green.svg') }}" :
                            "{{ asset('assets/images/icons/stop-red.svg') }}";
                    } else if (type === "end") {
                        iconUrl = status === "completed" ? "{{ asset('assets/images/icons/end-green.svg') }}" :
                            "{{ asset('assets/images/icons/end-red.svg') }}";
                    }

                    let marker = new google.maps.Marker({
                        position: position,
                        map: map,
                        label: '',
                        icon: {
                            url: iconUrl,
                            scaledSize: new google.maps.Size(30, 30),

                        }
                    });





                    allMarkers.push(marker);
                }

                // Add Start Marker
                addMarker(start, "A", start_point_status, "start", start_point);

                // Add Stop Markers
                stops.forEach((stop, index) => {
                    let stopPos = {
                        lat: parseFloat(stop.latitude),
                        lng: parseFloat(stop.longitude)
                    };
                    addMarker(stopPos, String.fromCharCode(66 + index), stop.status, "stop", stop.stop_point);
                });

                // Add End Marker
                addMarker(end, String.fromCharCode(66 + stops.length), end_point_status, "end", end_point);
            }
        }

        if (latitude && longitude) {
            getAddressFromCoordinates(latitude, longitude).then(address => {
                if (document.getElementById('address')) {
                    document.getElementById('address').innerHTML = address || "Address not found";
                }
            });
        }



    }


    let historyData = [];
    let historyMarker = null; // Reference for the history marker
    let path = null; // Reference for the Polyline
    let pathCoordinates = [];

    if (document.getElementById('liveMode')) {
        document.getElementById('liveMode').addEventListener('change', updateMode);
        const dateDropdown = document.getElementById('dateDropdown');
    }

    async function updateMode() {
        let liveMode = document.getElementById('liveMode').checked;
        document.getElementById('historyControls').style.display = liveMode ? 'none' : 'block';

        clearMapElements();

        if (!liveMode) {
            dateDropdown.innerHTML = '';
            await fetchAvailableDates();
        } else {
            historyData = [];


            if (selectedVehicleLat && selectedVehicleLng) {
                const latitude = parseFloat(selectedVehicleLat);
                const longitude = parseFloat(selectedVehicleLng);

                if (!latitude || !longitude) return;

                const position = {
                    lat: parseFloat(latitude),
                    lng: parseFloat(longitude)
                };

                // Pan and zoom to the position
                map.panTo(position);
                map.setZoom(15);
            }
        }
    }

    async function fetchAvailableDates() {
        try {
            const response = await fetch(
                `{{ url('/') }}/data/history/${selectedVehicleImei}/dates.json?nocache=${Date.now()}`);


            if (!response.ok) {
                // If file doesn't exist (404), simply return without breaking the script
                if (response.status === 404) {
                    console.warn("No available dates found.");
                    return;
                }
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const dates = await response.json();

            if (dates.length > 0) {
                populateDateDropdown(dates);
                fetchHistoryData(dates[0]); // Fetch data for the first date
            }
        } catch (error) {
            console.error("Error fetching available dates:", error);
        }
    }


    function populateDateDropdown(dates) {
        dateDropdown.innerHTML = ""; // Clear existing options

        dates.forEach((date, index) => {
            const option = document.createElement("option");
            option.value = date;
            option.textContent = date;
            if (index === 0) option.selected = true;
            dateDropdown.appendChild(option);
        });

        dateDropdown.addEventListener("change", (event) => {
            fetchHistoryData(event.target.value);
        });
    }

    async function fetchHistoryData(selectedDate) {
        try {
            clearMapElements();
            historyData = [];

            const response = await fetch(
                `{{ asset('data/history/${selectedVehicleImei}/${selectedDate}.json') }}`);

            if (!response.ok) {
                // If file doesn't exist (404), log a warning and exit gracefully
                if (response.status === 404) {
                    console.warn(`History data for ${selectedDate} not found.`);
                    return;
                }
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            historyData = await response.json();

            plotPathOnMap();
            initializeHistoryMarker(seletedVehicleIcon, 'red');
        } catch (error) {
            console.error("Error fetching history data:", error);
        }
    }



    let startMarker = null;
    let endMarker = null;
    let infoWindows = [];
    let infoMarkers = [];
    let historyPath = null;
    let stopMarkers = [];

    function plotPathOnMap() {
        let firstTripStart = null;
        let lastTripStop = null;
        let stopMarkersPoints = [];
        const MARKER_INTERVAL = 1; // Show a marker every 1 point

        // Clear existing path coordinates
        pathCoordinates = [];

        // Batch process the history data
        historyData.forEach((item, index) => {
            if (item.latitude && item.longitude) {
                let point = {
                    lat: parseFloat(item.latitude),
                    lng: parseFloat(item.longitude),
                    timestamp: item?.last_update,
                    angle: item?.angle,
                    speed: item?.speed || 0,
                    trip_status: item["250"],
                    trip_event: item.eventID,
                    index: index
                };
                pathCoordinates.push(point);

                // Track trip events only when needed
                if (point.trip_event === 250) {
                    if (point.trip_status == 1 && !firstTripStart) {
                        firstTripStart = point;
                    } else if (point.trip_status == 0) {
                        lastTripStop = point;
                        stopMarkersPoints.push(point);
                    }
                }
            }
        });

        if (pathCoordinates.length === 0) return;

        // Create start marker
        if (firstTripStart) {
            startMarker = new google.maps.Marker({
                position: {
                    lat: firstTripStart.lat,
                    lng: firstTripStart.lng
                },
                map: map,
                icon: {
                    url: "{{ asset('assets/images/icons/start-green.svg') }}",
                    scaledSize: new google.maps.Size(30, 30),
                },
                title: "Trip Started"
            });
            attachInfoWindow(startMarker, firstTripStart);
        }

        stopMarkersPoints.forEach(stopPoint => {
            const marker = new google.maps.Marker({
                position: {
                    lat: stopPoint.lat,
                    lng: stopPoint.lng
                },
                map: map,
                icon: {
                    url: "{{ asset('assets/images/icons/stop-marker-black.svg') }}",
                    scaledSize: new google.maps.Size(35, 35),
                },
                title: "Trip Stopped"
            });
            stopMarkers.push(marker);
            attachInfoWindow(marker, stopPoint);
        });

        // Create end marker
        if (lastTripStop) {
            endMarker = new google.maps.Marker({
                position: {
                    lat: lastTripStop.lat,
                    lng: lastTripStop.lng
                },
                map: map,
                icon: {
                    url: "{{ asset('assets/images/icons/end-green.svg') }}",
                    scaledSize: new google.maps.Size(30, 30),
                },
                title: "Trip Ended"
            });
            attachInfoWindow(endMarker, lastTripStop);
        }

        // Create optimized polyline
        historyPath = new google.maps.Polyline({
            path: pathCoordinates.map(point => ({
                lat: point.lat,
                lng: point.lng
            })),
            geodesic: true,
            strokeColor: '#F54619',
            strokeOpacity: 0.6,
            strokeWeight: 2,
        });
        historyPath.setMap(map);

        // Add info markers at intervals
        for (let i = 0; i < pathCoordinates.length; i += MARKER_INTERVAL) {
            const item = pathCoordinates[i];
            const position = {
                lat: item.lat,
                lng: item.lng
            };

            // Create marker only if speed or angle changed significantly
            if (i === 0 || i === pathCoordinates.length - 1 ||
                Math.abs(item.speed - pathCoordinates[i - MARKER_INTERVAL].speed) > 10 ||
                Math.abs(item.angle - pathCoordinates[i - MARKER_INTERVAL].angle) > 30) {

                const iconElement = document.createElement("img");
                iconElement.src = `{{ asset('assets/images/icons/info.svg') }}`;
                iconElement.style.width = `20px`;
                iconElement.style.height = `20px`;
                iconElement.style.transform = `rotate(${item.angle}deg)`;
                iconElement.style.opacity = 0.6;

                const marker = new google.maps.marker.AdvancedMarkerElement({
                    position: position,
                    map: map,
                    content: iconElement
                });

                attachInfoWindow(marker, item);
                infoMarkers.push(marker);
            }
        }
    }
    // Define arrow symbol that will be placed along the polyline
    function createArrowSymbol(angle) {
        return {
            path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
            strokeColor: "#ff0000",
            strokeWeight: 2,
            scale: 3,
            rotation: angle // Rotate arrow based on real vehicle direction
        };
    }

    // Function to attach InfoWindow to a marker
    function attachInfoWindow(marker, point) {
        const contentString = `Speed: ${point.speed} km/h\nTime: ${point.timestamp}`;
        const infowindow = new google.maps.InfoWindow({
            content: contentString
        });

        marker.addListener("click", () => {
            infowindow.open(map, marker);
        });

        infoWindows.push(infowindow);
    }



    function clearMapElements() {
        // Clear the Polyline
        if (path) {
            path.setMap(null); // Remove Polyline from the map
            path = null;
        }
        // Clear the Polyline
        if (historyPath) {
            historyPath.setMap(null); // Remove Polyline from the map
            historyPath = null;
        }
        // Clear the Polyline
        if (historyData) {
            historyData = [];
        }

        // Clear the marker
        if (historyMarker) {
            historyMarker.setMap(null); // Remove marker from the map
            historyMarker = null;
        }

        // Clear the Start Marker
        if (startMarker) {
            startMarker.setMap(null); // Remove Start Marker from the map
            startMarker = null;
        }

        // Clear the End Marker
        if (endMarker) {
            endMarker.setMap(null); // Remove End Marker from the map
            endMarker = null;
        }

        // Clear all InfoWindows
        infoWindows.forEach(infowindow => infowindow.close());
        infoWindows = [];

        // Clear markers for speed/time
        if (infoMarkers.length > 0) {
            infoMarkers.forEach(marker => marker.setMap(null));
            infoMarkers = [];
        }

        // Clear stop markers
        if (stopMarkers.length > 0) {
            stopMarkers.forEach(marker => marker.setMap(null));
            stopMarkers = [];
        }

        // Clear path coordinates
        pathCoordinates = [];
    }


    function initializeHistoryMarker(icon = null, color = null) {
        let historyIcon = icon ? icon : 'default';
        let historyColor = color ?? 'red';

        if (historyData.length > 0) {
            let firstPoint = historyData[0];
            let initialPosition = new google.maps.LatLng(
                parseFloat(firstPoint.latitude),
                parseFloat(firstPoint.longitude)
            );

            // Create a custom icon
            const iconElement = document.createElement('img');
            iconElement.src = `{{ asset('assets/images/icons/${historyIcon}/${historyColor}.png') }}`;
            iconElement.style.height = '55px';
            iconElement.style.width = 'auto';
            iconElement.style.transform = 'rotate(0deg)';
            iconElement.style.filter = 'drop-shadow(2px 4px 6px rgba(0,0,0,0.9))';


            // Initialize AdvancedMarkerElement
            historyMarker = new google.maps.marker.AdvancedMarkerElement({
                map: map,
                position: initialPosition,
                content: iconElement // Use the custom content for the marker
            });

            // Focus the map on the first position
            map.panTo(initialPosition);
            map.setZoom(20);


            const position = {
                lat: parseFloat(firstPoint.latitude),
                lng: parseFloat(firstPoint.longitude)
            };

            // google.maps.event.addListenerOnce(map, 'idle', () => {
            //     const projection = map.getProjection();
            //     const bounds = map.getBounds();
            //     const topRight = projection.fromLatLngToPoint(bounds.getNorthEast());
            //     const bottomLeft = projection.fromLatLngToPoint(bounds.getSouthWest());
            //     const scale = Math.pow(2, map.getZoom());

            //     const worldCoordinate = projection.fromLatLngToPoint(position);
            //     const pixelCoordinate = {
            //         x: (worldCoordinate.x - bottomLeft.x) * scale,
            //         y: (worldCoordinate.y - topRight.y) * scale,
            //     };

            //     // Offset by 30% of map width to the left
            //     const mapWidth = document.getElementById('map').offsetWidth;
            //     pixelCoordinate.x += mapWidth * 0.3;

            //     const newWorldCoordinate = {
            //         x: pixelCoordinate.x / scale + bottomLeft.x,
            //         y: pixelCoordinate.y / scale + topRight.y,
            //     };

            //     const newLatLng = projection.fromPointToLatLng(newWorldCoordinate);
            //     map.panTo(newLatLng);
            // });


        }
    }






    let currentHistoryIndex = 0;
    let playInterval = null;
    let isPlaying = false;

    function playPause() {
        if (playInterval) {
            clearInterval(playInterval);
            playInterval = null;
            isPlaying = false;
            document.getElementById('playIcon').classList.remove('hidden');
            document.getElementById('pauseIcon').classList.add('hidden');
            document.getElementById('buttonText').textContent = "@lang('translations.play')";
        } else {
            playInterval = setInterval(moveHistoryMarker, 800); // Move every 500ms
            isPlaying = true;
            document.getElementById('playIcon').classList.add('hidden');
            document.getElementById('pauseIcon').classList.remove('hidden');
            document.getElementById('buttonText').textContent = "@lang('translations.pause')";
        }
    }

    // Update marker position based on seekbar change
    if (document.getElementById('seekbar')) {
        document.getElementById('seekbar').addEventListener('input', (e) => {
            currentHistoryIndex = Math.floor((e.target.value / 100) * (historyData.length - 1));
            let point = historyData[currentHistoryIndex];
            if (point?.latitude && point?.longitude) {
                let markerPosition = new google.maps.LatLng(parseFloat(point.latitude), parseFloat(point
                    .longitude));

                if (historyMarker) {
                    historyMarker.position = markerPosition; // Update the position directly

                    let ignitionStatus = point["239"]; // 1 for 'On', 0 for 'Off'
                    let movementStatus = point["240"]; // 1 for 'On', 0 for 'Off'
                    let speed = point?.speed; // 1 for 'On', 0 for 'Off'
                    let angle = point.angle;

                    // Determine icon color
                    let iconColor = 'red';
                    if (movementStatus == 1 && speed > 1) {
                        iconColor = 'green';
                    } else if (movementStatus == 0 && ignitionStatus == 1) {
                        iconColor = 'yellow';
                    }

                    // Create custom icon
                    const iconElement = document.createElement('img');
                    iconElement.src =
                        `{{ asset('assets/images/icons/${seletedVehicleIcon}/${iconColor}.png') }}`;
                    iconElement.style.height = '55px';
                    iconElement.style.width = 'auto';
                    iconElement.style.transform = `rotate(${angle}deg)`;
                    iconElement.style.filter = 'drop-shadow(2px 4px 6px rgba(0,0,0,0.9))';


                    historyMarker.content = iconElement;


                    map.panTo(markerPosition);



                }
            }

            // Update additional UI elements
            document.getElementById('speed').innerHTML = `${point?.speed} km/h`;
            document.getElementById('currentTime').innerText = point?.last_update;
        });
    }

    function getDevicePin(ignitionStatus, movementStatus, speed, callback) {
        // Use the cached vehicle type
        let vehicleType = seletedVehicleIcon || 'default';
        let color = getPinColor(ignitionStatus, movementStatus, speed);
        let iconUrl = `{{ asset('assets/images/icons/${vehicleType}/${color}.png') }}`;
        callback(iconUrl);

    }

    // Function to determine the pin color
    function getPinColor(ignitionStatus, movementStatus, speed = 0) {
        if (movementStatus == 1 && speed > 0) {
            return "green";
        } else if (ignitionStatus == 1 && movementStatus == 0) {
            return "yellow";
        } else {
            if (movementStatus == 1 && ignitionStatus == 1) {
                return "green";
            } else {
                return "red";
            }
        }
    }


    // Play/Pause logic and updating the marker
    function moveHistoryMarker() {
        if (currentHistoryIndex < historyData.length - 1) {
            currentHistoryIndex++;
            let point = historyData[currentHistoryIndex];
            if (point.latitude && point.longitude) {
                let markerPosition = new google.maps.LatLng(parseFloat(point.latitude), parseFloat(point.longitude));

                // Move the marker
                if (historyMarker) {
                    historyMarker.position = markerPosition; // Update the position directly
                    let ignitionStatus = point["239"]; // 1 for 'On', 0 for 'Off'
                    let movementStatus = point["240"]; // 1 for 'On', 0 for 'Off'
                    let speed = point?.speed; // 1 for 'On', 0 for 'Off'
                    let angle = point?.angle;

                    // Determine icon color
                    let iconColor;
                    if (movementStatus == 1) {
                        iconColor = 'green';
                    } else if (movementStatus == 0 && ignitionStatus == 1) {
                        iconColor = 'yellow';
                    } else {
                        iconColor = 'red';
                    }

                    // Create custom icon
                    const iconElement = document.createElement('img');
                    iconElement.src =
                        `{{ asset('assets/images/icons/${seletedVehicleIcon}/${iconColor}.png') }}`;
                    iconElement.style.height = '55px';
                    iconElement.style.width = 'auto';
                    iconElement.style.transform = `rotate(${angle}deg)`;
                    iconElement.style.filter = 'drop-shadow(2px 4px 6px rgba(0,0,0,0.9))';

                    historyMarker.content = iconElement;

                    // Center the map on the moving marker
                    map.panTo(markerPosition);


                }
            }

            // Update the seekbar
            updateSeekbar();
        } else {
            clearInterval(playInterval);
            playInterval = null;
        }
    }


    // Update the seekbar when the marker moves automatically
    function updateSeekbar() {
        let seekbar = document.getElementById('seekbar');
        seekbar.value = (currentHistoryIndex / (historyData.length - 1)) * 100;

        // Update the current time display
        let fullTime = historyData[currentHistoryIndex]?.last_update;
        let speed = historyData[currentHistoryIndex]?.speed;
        document.getElementById('currentTime').innerText = fullTime;
        document.getElementById('speed').innerHTML = `${speed} km/h`;
    }

    window.onload = initializeMap;

    // Load the map when the API is ready
    // initializeMap();
</script>

<script async
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyC8DHtH6KQlFbii460Aegpt25GER2Bhshk&v=weekly&libraries=marker">
</script>


<script>
    async function getAddressFromCoordinates(lat, lng) {
        let url =
            `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=it`;

        try {
            const response = await fetch(url, {
                headers: {
                    'User-Agent': 'Controllone/1.0 (<EMAIL>)' // Set a custom user-agent
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            const data = await response.json();

            if (data && data.display_name) {
                return data.display_name; // Return the address
            } else {
                console.error('No address found for the given coordinates.');
                return null;
            }
        } catch (error) {
            console.error('Error fetching address:', error);
            return null;
        }
    }
</script>
@script
    <script>
        $wire.on('updateMapData', (data) => {
            setTimeout(() => {
                vehicleData = data.data;
                updateMarkers(vehicleData);

                // Find and update the selected vehicle if it exists in the new data
                if (selectedVehicleImei) {
                    Object.values(vehicleData).forEach(group => {
                        // Check if group is an array
                        if (Array.isArray(group)) {
                            group.forEach(vehicle => {
                                if (vehicle.imei === selectedVehicleImei) {
                                    window.dispatchEvent(
                                        new CustomEvent('update-selected-vehicle', {
                                            detail: vehicle
                                        })
                                    );
                                }
                            });
                        }
                    });
                }
            }, 500);
        });
    </script>
@endscript
