<div wire:poll.5s="refreshExportHistory">
    {{-- Export History Section --}}
    @if($show)
        <section class="w-full mx-auto mt-8">
            <div class="p-6 bg-white rounded-lg shadow-lg dark:bg-slate-800">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-slate-800 dark:text-slate-200">
                        @lang('translations.export_history')
                    </h2>
                    <button wire:click="toggleExportHistory" 
                        class="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                @if($exportHistory->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="text-xs font-semibold tracking-wide text-left text-gray-600 uppercase bg-gray-100 dark:bg-slate-700 dark:text-slate-300">
                                    <th class="px-4 py-3">@lang('translations.report_type')</th>
                                    <th class="px-4 py-3">@lang('translations.vehicle')</th>
                                    <th class="px-4 py-3">@lang('translations.period')</th>
                                    <th class="px-4 py-3">@lang('translations.file_type')</th>
                                    <th class="px-4 py-3">@lang('translations.status')</th>
                                    <th class="px-4 py-3">@lang('translations.file_size')</th>
                                    <th class="px-4 py-3">@lang('translations.created_at')</th>
                                    <th class="px-4 py-3">@lang('translations.actions')</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-slate-800 dark:text-slate-300">
                                @foreach($exportHistory as $export)
                                    <tr class="border-b border-slate-200 dark:border-slate-700">
                                        <td class="px-4 py-3">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full 
                                                {{ $export->report_type === 'routes' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : '' }}
                                                {{ $export->report_type === 'fuel_consumption' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : '' }}
                                                {{ $export->report_type === 'alarms' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : '' }}">
                                                @lang('translations.' . $export->report_type)
                                            </span>
                                        </td>
                                        <td class="px-4 py-3">{{ $export->vehicle?->license_plate ?? 'All Vehicles' }}</td>
                                        <td class="px-4 py-3">
                                            <div class="text-sm">
                                                <div>{{ $export->start_date->format('d/m/Y') }}</div>
                                                @if($export->start_date->format('Y-m-d') !== $export->end_date->format('Y-m-d'))
                                                    <div class="text-slate-500">to {{ $export->end_date->format('d/m/Y') }}</div>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full 
                                                {{ $export->file_type === 'pdf' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' }}">
                                                {{ strtoupper($export->file_type) }}
                                            </span>
                                        </td>
                                        <td class="px-4 py-3">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full 
                                                {{ $export->status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : '' }}
                                                {{ $export->status === 'processing' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : '' }}
                                                {{ $export->status === 'pending' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : '' }}
                                                {{ $export->status === 'failed' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : '' }}">
                                                @lang('translations.' . $export->status)
                                                @if($export->status === 'processing')
                                                    <svg class="inline w-3 h-3 ml-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                                    </svg>
                                                @endif
                                            </span>
                                        </td>
                                        <td class="px-4 py-3">{{ $export->file_size_human }}</td>
                                        <td class="px-4 py-3">
                                            <div class="text-sm">
                                                <div>{{ $export->created_at->format('d/m/Y H:i') }}</div>
                                                @if($export->completed_at)
                                                    <div class="text-xs text-slate-500">
                                                        @lang('translations.completed'): {{ $export->completed_at->format('H:i') }}
                                                    </div>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="flex gap-2">
                                                @if($export->status === 'completed')
                                                    <a href="{{ route('reports.download', $export->id) }}" 
                                                        class="px-3 py-1 text-xs text-white transition-colors bg-blue-500 rounded hover:bg-blue-600">
                                                        <svg class="inline w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                        </svg>
                                                        @lang('translations.download')
                                                    </a>
                                                    @if($export->file_type === 'pdf')
                                                        <a href="{{ route('reports.preview', $export->id) }}" target="_blank"
                                                            class="px-3 py-1 text-xs text-white transition-colors bg-gray-500 rounded hover:bg-gray-600">
                                                            <svg class="inline w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                            </svg>
                                                            @lang('translations.preview')
                                                        </a>
                                                    @endif
                                                @elseif($export->status === 'failed')
                                                    <span class="px-3 py-1 text-xs text-red-600 bg-red-100 rounded dark:bg-red-900 dark:text-red-200">
                                                        <svg class="inline w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        @lang('translations.failed')
                                                    </span>
                                                @else
                                                    <span class="px-3 py-1 text-xs text-yellow-600 bg-yellow-100 rounded dark:bg-yellow-900 dark:text-yellow-200">
                                                        <svg class="inline w-3 h-3 mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                                        </svg>
                                                        @lang('translations.processing')
                                                    </span>
                                                @endif
                                                
                                                <button wire:click="deleteExportedReport({{ $export->id }})" 
                                                    wire:confirm="@lang('translations.are_you_sure')"
                                                    class="px-3 py-1 text-xs text-white transition-colors bg-red-500 rounded hover:bg-red-600">
                                                    <svg class="inline w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                    @lang('translations.delete')
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $exportHistory->links('livewire.components.pagination') }}
                    </div>
                @else
                    <div class="p-8 text-center text-slate-500 dark:text-slate-400">
                        <svg class="w-12 h-12 mx-auto mb-4 text-slate-300 dark:text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="text-lg font-medium">@lang('translations.no_export_history')</p>
                        <p class="text-sm">@lang('translations.export_reports_will_appear_here')</p>
                    </div>
                @endif

                {{-- Flash Messages --}}
                @if (session()->has('message'))
                    <div class="p-4 mt-4 text-green-700 bg-green-100 border border-green-300 rounded dark:bg-green-900 dark:text-green-200 dark:border-green-700">
                        {{ session('message') }}
                    </div>
                @endif

                @if (session()->has('error'))
                    <div class="p-4 mt-4 text-red-700 bg-red-100 border border-red-300 rounded dark:bg-red-900 dark:text-red-200 dark:border-red-700">
                        {{ session('error') }}
                    </div>
                @endif
            </div>
        </section>
    @endif
</div>
