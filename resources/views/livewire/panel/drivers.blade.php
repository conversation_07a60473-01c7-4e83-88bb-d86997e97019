<div class="p-4 text-sm">

    <div class="justify-between md:flex">
        <div>
            <h1 class="text-xl font-medium tracking-wide font-poppins text-slate-800 dark:text-slate-100">
                @lang('translations.driver_management')
            </h1>
        </div>
        @can('driver_add')
            <div class="flex justify-end mt-3 h-fit md:mt-0">
                <button wire:click="addDriver"
                    class="flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-secondary ">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>

                    <span>@lang('translations.add_driver')</span>
                </button>
            </div>
        @endcan


    </div>


    @can('driver_view')

        <section class="w-full mx-auto mt-4">

            <div class="flex items-center flex-grow gap-4 md:justify-end">

                <div class="w-full max-w-xs">
                    <div class="flex items-center w-full space-x-5">
                        <div
                            class="flex w-full p-3 space-x-2 text-sm bg-gray-100 border border-transparent rounded-lg shadow dark:bg-slate-800 focus-within:border-gray-300 dark:focus-within:border-slate-600 dark:text-slate-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0 w-5 h-5 opacity-30" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            <input wire:model.live.debounce.500ms="search" class="bg-transparent outline-none"
                                type="search" placeholder="Search" />
                        </div>
                    </div>
                </div>

                <div class="w-fit">
                    <select wire:model.live="filter_status" class="bg-transparent outline-none dark:text-slate-200">
                        <option selected value="">@lang('translations.status')</option>
                        <option value="1">@lang('translations.active')</option>
                        <option value="2">@lang('translations.inactive')</option>
                    </select>
                </div>
            </div>



            <div class="w-full mt-4 mb-20 overflow-hidden bg-white rounded-lg shadow dark:bg-slate-800">

                <div class="w-full mb-5 overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr
                                class="text-xs font-semibold tracking-wide text-left text-gray-600 uppercase bg-gray-100 dark:bg-slate-900 dark:text-slate-100">
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.driver_name')</th>
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.phone_number')</th>
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.address')</th>
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.status')</th>
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.added_at')</th>
                                <th class="px-4 py-3 whitespace-nowrap text-end md:min-w-28 min-w-40">@lang('translations.actions')
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-slate-800">
                            @forelse ($drivers as $driver)
                                <tr class="text-gray-700 dark:text-slate-300">
                                    <td class="px-4 py-3">
                                        <div>
                                            <p class="text-sm font-semibold text-black dark:text-slate-300">
                                                {{ $driver->name ?? '' }}
                                            </p>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        {{ $driver->phone ?? 'N/A' }}
                                    </td>
                                    <td class="px-4 py-3">
                                        {{ $driver->address ?? 'N/A' }}
                                    </td>
                                    <td class="px-4 py-3 text-xs">
                                        @if ($driver->status == 1)
                                            <span
                                                class="px-2 py-1 font-medium leading-tight rounded-full text-emerald-700 bg-emerald-100">
                                                @lang('translations.active') </span>
                                        @else
                                            <span
                                                class="px-2 py-1 font-medium leading-tight rounded-full text-rose-700 bg-rose-100">
                                                @lang('translations.inactive') </span>
                                        @endif
                                    </td>

                                    <td class="px-4 py-3 text-sm">{{ $driver->created_at->format('d-m-Y H:i') }}
                                    </td>

                                    <td class="px-4 py-3 text-sm">
                                        <div class="flex items-center justify-end gap-2">



                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <button wire:click="showRecord({{ $driver->id }})"
                                                    @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                    <img class="size-5"
                                                        src="{{ asset('assets/images/icons/eye-icon.svg') }}"
                                                        alt="Edit">
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    @lang('translations.view')
                                                </div>
                                            </div>



                                            @can('driver_edit')
                                                <!-- Edit Button with Tooltip -->
                                                <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                    <button wire:click="editRecord({{ $driver->id }})"
                                                        @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                        <img class="size-5" src="{{ asset('assets/images/icons/edit.svg') }}"
                                                            alt="Edit">
                                                    </button>
                                                    <div x-show="showTooltip"
                                                        x-transition:enter="transition ease-out duration-200"
                                                        x-transition:enter-start="opacity-0 transform scale-95"
                                                        x-transition:enter-end="opacity-100 transform scale-100"
                                                        x-transition:leave="transition ease-in duration-150"
                                                        x-transition:leave-start="opacity-100 transform scale-100"
                                                        x-transition:leave-end="opacity-0 transform scale-95"
                                                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                        style="display: none;">
                                                        @lang('translations.edit')
                                                    </div>
                                                </div>
                                            @endcan

                                            @can('driver_delete')
                                                <!-- Delete Button with Tooltip -->
                                                <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                    <button wire:click="deleteRecordConfirmation({{ $driver->id }})"
                                                        @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                        <img class="size-5" src="{{ asset('assets/images/icons/delete.svg') }}"
                                                            alt="Delete">
                                                    </button>
                                                    <div x-show="showTooltip"
                                                        x-transition:enter="transition ease-out duration-200"
                                                        x-transition:enter-start="opacity-0 transform scale-95"
                                                        x-transition:enter-end="opacity-100 transform scale-100"
                                                        x-transition:leave="transition ease-in duration-150"
                                                        x-transition:leave-start="opacity-100 transform scale-100"
                                                        x-transition:leave-end="opacity-0 transform scale-95"
                                                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                        style="display: none;">
                                                        @lang('translations.delete')
                                                    </div>
                                                </div>
                                            @endcan


                                        </div>

                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="10" class="px-4 py-3 text-center dark:text-slate-300">
                                        @lang('translations.no_driver_found')
                                    </td>
                                </tr>
                            @endforelse

                        </tbody>
                    </table>
                </div>

                {{ $drivers->links('livewire.components.pagination') }}

            </div>
        </section>
    @endcan

    {{-- modals --}}

    @canany(['driver_add', 'driver_edit'])
        <x-modal name="manage-driver">
            <x-slot:body>
                <!-- Modal Header -->
                <div class="flex items-center justify-between">

                    <button @click="show = false;" class="ms-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="#808080" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                </div>

                <div class="w-full">

                    <div class="flex items-center justify-center">
                        <h2 class="text-xl font-medium text-center text-black dark:text-slate-100">
                            @if ($recordId)
                                @lang('translations.edit_driver')
                            @else
                                @lang('translations.add_driver')
                            @endif
                        </h2>
                    </div>


                    <div class="mt-4">

                        <div class="grid grid-cols-2 gap-4">
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.driver_name')</label>

                                <div class="relative mt-2">
                                    <input type="text" wire:model="name" id="name"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.name')">
                                </div>

                                @error('name')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.phone_number')</label>

                                <div class="relative mt-2">
                                    <input type="text" wire:model="phone" id="phone"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.phone_number')">
                                </div>

                                @error('phone')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.employment_date')</label>

                                <div class="relative mt-2">
                                    <input type="date" wire:model="employment_date" id="employment_date"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.employment_date')">
                                </div>

                                @error('employment_date')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.license_number')</label>

                                <div class="relative mt-2">
                                    <input type="text" wire:model="license_number" id="license_number"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.license_number')">
                                </div>

                                @error('license_number')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.ibutton_code')</label>

                                <div class="relative mt-2">
                                    <input type="text" wire:model="ibutton_code" id="ibutton_code"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.ibutton_code')">
                                </div>

                                @error('ibutton_code')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.plant_type')</label>

                                <div class="relative mt-2">
                                    <input type="text" wire:model="plant_type" id="plant_type"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.plant_type')">
                                </div>

                                @error('plant_type')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>

                            <div class="col-span-2">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.address')</label>

                                <div class="relative mt-2">
                                    <input type="text" wire:model="address" id="address"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.address')">
                                </div>

                                @error('address')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>


                            <div class="flex items-center col-span-2">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" wire:model="status" id="active" class="sr-only peer">
                                    <div
                                        class="w-9 h-5 bg-gray-200 hover:bg-gray-300 peer-focus:outline-0 peer-focus:ring-transparent rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary hover:peer-checked:bg-primary">
                                    </div>
                                </label>

                                <label for="active"
                                    class="ms-2 cursor-pointer select-none text-sm text-[#414651] dark:text-slate-300">
                                    @lang('translations.active')
                                </label>
                            </div>
                        </div>

                        <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                            <button @click="show = false;"
                                class="modal-close text-[#414651] dark:border-slate-600 dark:text-slate-500 p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                                @lang('translations.cancel')
                            </button>

                            <button wire:click="addUpdateDriver" wire:loading.attr="disabled"
                                wire:target="addUpdateDriver" type="button"
                                class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-secondary transition-all duration-300">
                                <span wire:loading.remove wire:target="addUpdateDriver"> @lang('translations.save')
                                </span>
                                <div wire:loading wire:target="addUpdateDriver">
                                    <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                    </div>
                                </div>
                            </button>
                        </div>
                    </div>

                </div>
            </x-slot:body>
        </x-modal>
    @endcanany

    @can('driver_delete')
        <x-modal name="delete-record-modal">
            <x-slot:body>

                <div class="flex items-center justify-between">
                    <img class="size-10" src="{{ asset('assets/images/icons/delete.svg') }}" alt="delete">

                    <button @click="show = false" class="ms-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="#808080" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>

                </div>

                <div class="mt-4">
                    <h3 class="font-medium dark:text-slate-200">
                        @lang('translations.confirm_delete_record')
                    </h3>

                    <p class="text-sm mt-2 text-[#535862]da rk:text-slate-300 ">
                        @lang('translations.delete_warning')
                    </p>


                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false"
                            class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm dark:text-slate-400 dark:border-slate-600">
                            @lang('translations.cancel')
                        </button>
                        <button wire:click="deleteRecord"
                            class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                            @lang('translations.delete')
                        </button>
                    </div>
                </div>


            </x-slot:body>
        </x-modal>
    @endcan


    @can('driver_view')
        <x-modal name="show-record">
            <x-slot:body>
                <!-- Modal Header -->
                <div class="flex items-center justify-between">
                    <h2 class="col-span-2 text-lg font-semibold text-center">
                        @lang('translations.vehicle_details')
                    </h2>

                    <button @click="show = false;" class="ms-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="#808080" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                </div>

                <div class="w-full">

                    <div class="mt-4">

                        <div class="grid gap-4 md:grid-cols-2">
                            @if ($selectedRecord)
                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.name')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        {{ $selectedRecord->name ?? 'N/A' }}</p>
                                </div>

                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.phone_number')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        {{ $selectedRecord->phone ?? 'N/A' }}</p>
                                </div>

                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.employment_date')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        {{ $selectedRecord->employment_date ? \Carbon\Carbon::parse($selectedRecord->employment_date)->format('d/m/Y') : 'N/A' }}
                                    </p>
                                </div>


                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.license_number')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        {{ $selectedRecord->license_number ?? 'N/A' }}</p>
                                </div>


                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.plant_type')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        {{ $selectedRecord->plant_type ?? 'N/A' }}</p>
                                </div>

                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.ibutton_code')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        {{ $selectedRecord->ibutton_code ?? 'N/A' }}</p>
                                </div>


                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.status')</label>
                                    <p class="mt-2 text-sm text-gray-800 dark:text-slate-300">
                                        @if ($selectedRecord->status == 1)
                                            <span
                                                class="px-2 py-0.5 font-medium leading-tight text-emerald-700 bg-emerald-100 rounded-full">
                                                @lang('translations.active') </span>
                                        @else
                                            <span
                                                class="px-2 py-0.5 font-medium leading-tight text-rose-700 bg-rose-100 rounded-full">
                                                @lang('translations.inactive') </span>
                                        @endif
                                    </p>
                                </div>

                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.updated_at')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        @if ($selectedRecord->updated_at)
                                            {{ $selectedRecord->updated_at->format('d-m-Y H:i') }}
                                        @else
                                            N/A
                                        @endif
                                    </p>
                                </div>



                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.created_at')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        {{ $selectedRecord->created_at->format('d-m-Y H:i') }}</p>
                                </div>
                            @else
                                <p>@lang('translations.no_vehicle_found')</p>
                            @endif
                        </div>


                    </div>

                </div>
            </x-slot:body>
        </x-modal>
    @endcan



</div>
