<?php

/**
 * Test script for the optimized TripCalculationHelper
 * This script tests the consolidation logic to prevent duplicate trips and stops
 */

require_once 'app/Helpers/TripCalculationHelper.php';
require_once 'app/Helpers/helpers.php'; // For parseFlexibleTimestamp and other helper functions

use App\Helpers\TripCalculationHelper;
use Carbon\Carbon;

// Mock the helper functions if they don't exist
if (!function_exists('parseFlexibleTimestamp')) {
    function parseFlexibleTimestamp($timestamp) {
        return Carbon::createFromFormat('d/m/Y H:i', $timestamp);
    }
}

if (!function_exists('getAddressFromCoordinates')) {
    function getAddressFromCoordinates($lat, $lng) {
        return "Test Address ($lat, $lng)";
    }
}

if (!function_exists('getOdometerValue')) {
    function getOdometerValue($data) {
        return $data['16'] ?? 0;
    }
}

if (!function_exists('getFuelConsumption')) {
    function getFuelConsumption($data) {
        return $data['86'] ?? 0;
    }
}

/**
 * Create test data that simulates the problematic patterns
 */
function createTestData() {
    return [
        // Trip 1: 05:48 - Moving
        [
            'last_update' => '26/03/2025 05:48',
            'latitude' => 44.9941683,
            'longitude' => 7.53481,
            'speed' => 6,
            'eventID' => 250,
            '250' => 1, // Trip
            '16' => 3688083,
            '86' => 16.247,
            '239' => 1,
            '240' => 1
        ],
        // Stop 1: 05:53 - Stopped (should be first stop)
        [
            'last_update' => '26/03/2025 05:53',
            'latitude' => 44.9941383,
            'longitude' => 7.5361316,
            'speed' => 0,
            'eventID' => 250,
            '250' => 0, // Stop
            '16' => 3688209,
            '86' => 16.289,
            '239' => 0,
            '240' => 0
        ],
        // Trip 2: 06:42 - Brief movement (should be ignored or consolidated)
        [
            'last_update' => '26/03/2025 06:42',
            'latitude' => 44.9940966,
            'longitude' => 7.5361883,
            'speed' => 5,
            'eventID' => 250,
            '250' => 1, // Trip
            '16' => 3688219,
            '86' => 16.418,
            '239' => 1,
            '240' => 1
        ],
        // Stop 2: 07:14 - Stopped at different location
        [
            'last_update' => '26/03/2025 07:14',
            'latitude' => 45.007405,
            'longitude' => 7.8256416,
            'speed' => 0,
            'eventID' => 250,
            '250' => 0, // Stop
            '16' => 3721510,
            '86' => 16.425,
            '239' => 0,
            '240' => 0
        ],
        // Trip 3: 07:19 - Brief movement
        [
            'last_update' => '26/03/2025 07:19',
            'latitude' => 45.0073666,
            'longitude' => 7.8258066,
            'speed' => 6,
            'eventID' => 250,
            '250' => 1, // Trip
            '16' => 3721525,
            '86' => 16.432,
            '239' => 1,
            '240' => 1
        ],
        // Stop 3: 07:22 - Stopped nearby (should consolidate with previous stop)
        [
            'last_update' => '26/03/2025 07:22',
            'latitude' => 45.0117966,
            'longitude' => 7.8224949,
            'speed' => 0,
            'eventID' => 250,
            '250' => 0, // Stop
            '16' => 3722238,
            '86' => 16.435,
            '239' => 0,
            '240' => 1
        ],
        // Trip 4: 10:09 - Real trip
        [
            'last_update' => '26/03/2025 10:09',
            'latitude' => 45.005925,
            'longitude' => 7.8291783,
            'speed' => 5,
            'eventID' => 250,
            '250' => 1, // Trip
            '16' => 3756175,
            '86' => 16.751,
            '239' => 1,
            '240' => 1
        ],
        // Stop 4: 10:21 - Brief stop
        [
            'last_update' => '26/03/2025 10:21',
            'latitude' => 45.0147016,
            'longitude' => 7.82253,
            'speed' => 0,
            'eventID' => 250,
            '250' => 0, // Stop
            '16' => 3758277,
            '86' => 16.789,
            '239' => 0,
            '240' => 0
        ],
        // Trip 5: 10:21 - Continuation (should consolidate with Trip 4)
        [
            'last_update' => '26/03/2025 10:21',
            'latitude' => 45.0147266,
            'longitude' => 7.8225316,
            'speed' => 9,
            'eventID' => 250,
            '250' => 1, // Trip
            '16' => 3758305,
            '86' => 16.792,
            '239' => 1,
            '240' => 1
        ],
        // Stop 5: 10:25 - Final stop
        [
            'last_update' => '26/03/2025 10:25',
            'latitude' => 45.0058716,
            'longitude' => 7.82918,
            'speed' => 0,
            'eventID' => 250,
            '250' => 0, // Stop
            '16' => 3760153,
            '86' => 16.795,
            '239' => 0,
            '240' => 1
        ]
    ];
}

/**
 * Test the trip calculation with the optimized logic
 */
function testTripCalculation() {
    echo "🧪 Testing Optimized Trip Calculation Logic\n";
    echo "==========================================\n\n";

    $testData = createTestData();
    $result = TripCalculationHelper::calculateTripsAndStops($testData, '26-03-2025');

    echo "📊 RESULTS SUMMARY:\n";
    echo "Stops found: " . count($result['stops']) . "\n";
    echo "Trips found: " . count($result['trips']) . "\n";
    echo "Sequence events: " . count($result['sequence'] ?? []) . "\n\n";

    // Show the proper sequence
    echo "🔄 PROPER SEQUENCE:\n";
    echo "===================\n";
    if (isset($result['sequence'])) {
        foreach ($result['sequence'] as $index => $event) {
            $startTime = $event['start_time']->format('H:i');
            $endTime = $event['end_time'] === 'Ongoing' ? 'Ongoing' : $event['end_time']->format('H:i');
            $type = strtoupper($event['type']);

            echo ($index + 1) . ". {$startTime} - {$endTime} [{$type}]\n";

            if ($event['type'] === 'stop') {
                $lat = round($event['location']['lat'], 6);
                $lng = round($event['location']['lng'], 6);
                echo "   Location: {$lat}, {$lng}\n";
                echo "   Duration: {$event['duration']}\n";
            } else {
                $startLat = round($event['start_location']['lat'], 6);
                $startLng = round($event['start_location']['lng'], 6);
                $endLat = round($event['end_location']['lat'], 6);
                $endLng = round($event['end_location']['lng'], 6);
                $distance = round($event['distance'], 2);
                echo "   From: {$startLat}, {$startLng}\n";
                echo "   To: {$endLat}, {$endLng}\n";
                echo "   Distance: {$distance} km\n";
                echo "   Duration: {$event['duration']}\n";
            }
            echo "\n";
        }
    }

    echo "🛑 STOPS ANALYSIS:\n";
    echo "==================\n";
    foreach ($result['stops'] as $index => $stop) {
        $startTime = $stop['start_time']->format('H:i');
        $endTime = $stop['end_time'] === 'Ongoing' ? 'Ongoing' : $stop['end_time']->format('H:i');
        $lat = round($stop['location']['lat'], 6);
        $lng = round($stop['location']['lng'], 6);

        echo "Stop " . ($index + 1) . ": {$startTime} - {$endTime}\n";
        echo "  Location: {$lat}, {$lng}\n";
        echo "  Duration: {$stop['duration']}\n\n";
    }

    echo "🚗 TRIPS ANALYSIS:\n";
    echo "==================\n";
    foreach ($result['trips'] as $index => $trip) {
        $startTime = $trip['start_time']->format('H:i');
        $endTime = $trip['end_time']->format('H:i');
        $startLat = round($trip['start_location']['lat'], 6);
        $startLng = round($trip['start_location']['lng'], 6);
        $endLat = round($trip['end_location']['lat'], 6);
        $endLng = round($trip['end_location']['lng'], 6);
        $distance = round($trip['distance'], 2);
        $fuel = round($trip['fuel_consumption'], 2);

        echo "Trip " . ($index + 1) . ": {$startTime} - {$endTime}\n";
        echo "  From: {$startLat}, {$startLng}\n";
        echo "  To: {$endLat}, {$endLng}\n";
        echo "  Distance: {$distance} km\n";
        echo "  Fuel: {$fuel} L\n";
        echo "  Duration: {$trip['duration']}\n\n";
    }

    // Validation
    echo "✅ VALIDATION:\n";
    echo "==============\n";

    $expectedMaxStops = 3; // Should consolidate nearby stops
    $expectedMaxTrips = 2; // Should consolidate consecutive trips

    if (count($result['stops']) <= $expectedMaxStops) {
        echo "✅ Stops consolidation: PASSED (found " . count($result['stops']) . ", expected ≤ {$expectedMaxStops})\n";
    } else {
        echo "❌ Stops consolidation: FAILED (found " . count($result['stops']) . ", expected ≤ {$expectedMaxStops})\n";
    }

    if (count($result['trips']) <= $expectedMaxTrips) {
        echo "✅ Trips consolidation: PASSED (found " . count($result['trips']) . ", expected ≤ {$expectedMaxTrips})\n";
    } else {
        echo "❌ Trips consolidation: FAILED (found " . count($result['trips']) . ", expected ≤ {$expectedMaxTrips})\n";
    }

    // Validate sequence consistency
    if (isset($result['sequence'])) {
        $sequenceValid = true;
        $lastType = null;

        foreach ($result['sequence'] as $event) {
            if ($lastType === $event['type']) {
                echo "❌ Sequence consistency: FAILED (consecutive {$event['type']}s found)\n";
                $sequenceValid = false;
                break;
            }
            $lastType = $event['type'];
        }

        if ($sequenceValid) {
            echo "✅ Sequence consistency: PASSED (proper alternating pattern)\n";
        }
    } else {
        echo "⚠️  Sequence data not available\n";
    }

    return $result;
}

// Run the test
if (php_sapi_name() === 'cli') {
    testTripCalculation();
} else {
    echo "<pre>";
    testTripCalculation();
    echo "</pre>";
}

?>
