<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicle_events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vehicle_id')->constrained()->onDelete('cascade'); 
            $table->string('event_type'); 
            $table->string('other_event_type')->nullable(); 
            $table->text('description')->nullable(); 
            $table->string('start_date'); 
            $table->string('end_date')->nullable(); 
            $table->boolean('is_under_maintenance')->default(false); 
            $table->string('maintenance_status')->nullable(); 
            $table->boolean('has_alert')->default(true); 
            $table->json('alert_recipients')->nullable(); 
            $table->json('attachments')->nullable(); 
            $table->string('alert_type')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicle_events');
    }
};
