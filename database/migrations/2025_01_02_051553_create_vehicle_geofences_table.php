<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicle_geofences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vehicle_id')->constrained()->cascadeOnDelete()->noActionOnUpdate();
            $table->foreignId('geofence_id')->constrained()->cascadeOnDelete()->noActionOnUpdate();
            $table->string('vehicle_imei')->nullable();
            $table->tinyInteger('zone_number')->nullable()->comment('Zone number 1-5 for Teltonika device');
            $table->boolean('push_notification')->default(false);
            $table->boolean('email_notification')->default(false);
            $table->boolean('geofence_in_notification')->default(false);
            $table->boolean('geofence_out_notification')->default(false);
            $table->timestamps();

            // Ensure unique zone per IMEI
            $table->unique(['vehicle_imei', 'zone_number'], 'unique_imei_zone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicle_geofences');
    }
};
