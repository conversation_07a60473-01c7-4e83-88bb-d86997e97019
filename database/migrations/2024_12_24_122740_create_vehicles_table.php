<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();
            $table->string('imei')->nullable();
            $table->string('license_plate')->nullable();
            $table->foreignId('driver_id')->nullable()->constrained('drivers')->nullOnDelete()->noActionOnUpdate();
            $table->string('model')->nullable();
            $table->string('type')->nullable();
            $table->string('icon')->nullable();
            $table->string('current_odometer_reading')->nullable();
            $table->string('mileage')->nullable();
            $table->string('year_of_registration')->nullable();
            $table->string('vin')->nullable();
            $table->boolean('status')->nullable();
            $table->string('pin')->nullable();
            $table->boolean('local_lock_ibutton')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicles');
    }
};
