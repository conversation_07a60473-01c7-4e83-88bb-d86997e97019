<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicle_route_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vehicle_id')->constrained('vehicles')->cascadeOnDelete()->noActionOnUpdate(); // Foreign key
            $table->string('start_point'); // Start point
            $table->string('end_point'); // End point

            $table->string('start_point_lat')->nullable();
            $table->string('start_point_lng')->nullable();
            $table->string('start_point_status')->default('pending');
            $table->string('start_point_completed_at')->nullable();
            $table->string('start_point_odometer')->nullable();
            $table->string('start_point_fuel')->nullable();



            $table->string('end_point_lat')->nullable();
            $table->string('end_point_lng')->nullable();
            $table->string('end_point_status')->default('pending');
            $table->string('end_point_completed_at')->nullable();
            $table->string('end_point_odometer')->nullable();
            $table->string('end_point_fuel')->nullable();

            $table->string('start_date'); // Start date and time
            $table->string('end_date')->nullable(); // End date and time, nullable if repeating

            $table->string('departure_time')->nullable(); // End date and time, nullable if repeating

            $table->boolean('is_repeat')->default(false); // Repeat flag
            $table->text('route')->nullable();
            $table->float('distance')->nullable(); // Distance in kilometers or miles
            $table->string('duration')->nullable(); // Duration in minutes
            $table->string('status')->nullable(); // Duration in minutes
            $table->text('remarks')->nullable(); // Additional remarks
            $table->timestamps(); // Created at and updated at
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('driver_route_assignments');
    }
};
