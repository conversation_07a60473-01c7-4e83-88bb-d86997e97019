<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Dashboard
            ['name' => 'dashboard_view', 'description' => 'View Dashboard'],

            // User Management
            ['name' => 'user_view', 'description' => 'View Users'],
            ['name' => 'user_add', 'description' => 'Add Users'],
            ['name' => 'user_edit', 'description' => 'Edit Users'],
            ['name' => 'user_delete', 'description' => 'Delete Users'],

            // Vehicle Management
            ['name' => 'vehicle_view', 'description' => 'View Vehicles'],
            ['name' => 'vehicle_add', 'description' => 'Add Vehicles'],
            ['name' => 'vehicle_edit', 'description' => 'Edit Vehicles'],
            ['name' => 'vehicle_delete', 'description' => 'Delete Vehicles'],
            
            // Driver Management
            ['name' => 'driver_view', 'description' => 'View Drivers'],
            ['name' => 'driver_add', 'description' => 'Add Drivers'],
            ['name' => 'driver_edit', 'description' => 'Edit Drivers'],
            ['name' => 'driver_delete', 'description' => 'Delete Drivers'],
            ['name' => 'all_drivers_access', 'description' => 'Access all Drivers'],

            // Fleet Monitoring
            ['name' => 'fleet_view', 'description' => 'View Fleet'],

            // Remote Control
            ['name' => 'remote_control', 'description' => 'Access Remote Control'],
            ['name' => 'remote_control_pin_manage', 'description' => 'Manage Remote Control PIN'],

            // Geofencing
            ['name' => 'geofence_view', 'description' => 'View Geofence'],
            ['name' => 'geofence_add', 'description' => 'Add Geofence'],
            ['name' => 'geofence_edit', 'description' => 'Edit Geofence'],
            ['name' => 'geofence_delete', 'description' => 'Delete Geofence'],

            // Reporting
            ['name' => 'reporting_view', 'description' => 'View Reporting'],
            ['name' => 'reporting_export', 'description' => 'Export Reports'],

            // Quick Stats
            ['name' => 'stats_view', 'description' => 'View Quick Stats'],

            // Notifications
            ['name' => 'notifications_view', 'description' => 'View Notifications'],
        ];

        foreach ($permissions as $permission) {
            Permission::updateOrCreate(['name' => $permission['name']], $permission);
        }
    }
}
