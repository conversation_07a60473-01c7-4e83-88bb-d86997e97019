<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Helpers\TripCalculationHelper;
use Carbon\Carbon;

class TripCalculationHelperTest extends TestCase
{
    public function test_trip_calculation_helper_basic_functionality()
    {
        // Sample history data with eventID 250 for trip start/stop
        $historyData = [
            [
                'latitude' => 45.4215,
                'longitude' => -75.6972,
                'last_update' => '01/01/2024 08:00',
                'speed' => 0,
                '239' => 1, // ignition on
                'eventID' => 250,
                '250' => 0, // stopped
                'odometer' => 100000,
                'fuel_level' => 50.0
            ],
            [
                'latitude' => 45.4215,
                'longitude' => -75.6972,
                'last_update' => '01/01/2024 09:00',
                'speed' => 30,
                '239' => 1, // ignition on
                'eventID' => 250,
                '250' => 1, // trip started
                'odometer' => 100000,
                'fuel_level' => 50.0
            ],
            [
                'latitude' => 45.4315,
                'longitude' => -75.7072,
                'last_update' => '01/01/2024 10:00',
                'speed' => 0,
                '239' => 1, // ignition on
                'eventID' => 250,
                '250' => 0, // trip ended
                'odometer' => 105000,
                'fuel_level' => 48.0
            ]
        ];

        $result = TripCalculationHelper::calculateTripsAndStops($historyData, '01-01-2024');

        // Verify we have the expected structure
        $this->assertArrayHasKey('stops', $result);
        $this->assertArrayHasKey('trips', $result);
        $this->assertArrayHasKey('firstValidData', $result);
        $this->assertArrayHasKey('lastValidData', $result);

        // Verify we have at least one stop and one trip
        $this->assertGreaterThan(0, count($result['stops']));
        $this->assertGreaterThan(0, count($result['trips']));

        // Verify first and last data are set correctly
        $this->assertEquals($historyData[0], $result['firstValidData']);
        $this->assertEquals($historyData[2], $result['lastValidData']);
    }

    public function test_trip_calculation_with_fallback_logic()
    {
        // Sample history data without eventID 250 (fallback to speed/ignition)
        $historyData = [
            [
                'latitude' => 45.4215,
                'longitude' => -75.6972,
                'last_update' => '01/01/2024 08:00',
                'speed' => 0,
                '239' => 1, // ignition on, stopped
                'odometer' => 100000,
                'fuel_level' => 50.0
            ],
            [
                'latitude' => 45.4215,
                'longitude' => -75.6972,
                'last_update' => '01/01/2024 09:00',
                'speed' => 30,
                '239' => 1, // ignition on, moving
                'odometer' => 102000,
                'fuel_level' => 49.5
            ],
            [
                'latitude' => 45.4315,
                'longitude' => -75.7072,
                'last_update' => '01/01/2024 10:00',
                'speed' => 0,
                '239' => 1, // ignition on, stopped again
                'odometer' => 105000,
                'fuel_level' => 48.0
            ]
        ];

        $result = TripCalculationHelper::calculateTripsAndStops($historyData, '01-01-2024');

        // Verify we have the expected structure
        $this->assertArrayHasKey('stops', $result);
        $this->assertArrayHasKey('trips', $result);

        // With fallback logic, we should still detect stops and trips
        $this->assertIsArray($result['stops']);
        $this->assertIsArray($result['trips']);
    }

    public function test_empty_history_data()
    {
        $result = TripCalculationHelper::calculateTripsAndStops([], '01-01-2024');

        $this->assertArrayHasKey('stops', $result);
        $this->assertArrayHasKey('trips', $result);
        $this->assertEmpty($result['stops']);
        $this->assertEmpty($result['trips']);
        $this->assertNull($result['firstValidData']);
        $this->assertNull($result['lastValidData']);
    }

    public function test_invalid_coordinates_are_skipped()
    {
        $historyData = [
            [
                // Missing latitude/longitude - should be skipped
                'last_update' => '01/01/2024 08:00',
                'speed' => 0,
                '239' => 1,
                'odometer' => 100000,
                'fuel_level' => 50.0
            ],
            [
                'latitude' => 45.4215,
                'longitude' => -75.6972,
                'last_update' => '01/01/2024 09:00',
                'speed' => 30,
                '239' => 1,
                'odometer' => 102000,
                'fuel_level' => 49.5
            ]
        ];

        $result = TripCalculationHelper::calculateTripsAndStops($historyData, '01-01-2024');

        // Should only process the valid data point
        $this->assertEquals($historyData[1], $result['firstValidData']);
        $this->assertEquals($historyData[1], $result['lastValidData']);
    }
}
