<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;

class SetUserLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Default locale from config
        $locale = config('app.locale');

        // Check for authenticated user
        if (Auth::check()) {
            $user = Auth::user();
            if ($user->language) {
                $locale = $user->language;
            }
        } else {
            // Parse Accept-Language header
            $acceptLanguage = $request->header('Accept-Language');
            if ($acceptLanguage) {
                $languages = explode(',', $acceptLanguage);
                foreach ($languages as $language) {
                    // Extract the language code
                    $lang = explode(';', trim($language))[0];
                    // Get the primary language code (e.g., 'en' from 'en-US')
                    $primaryLang = explode('-', $lang)[0];
                    $primaryLang = explode('_', $primaryLang)[0]; // Handle cases like 'en_US'

                    // Check if we support this language
                    if (in_array($primaryLang, ['en', 'it', 'fr', 'es', 'nl', 'pt', 'de'])) {
                        $locale = $primaryLang;
                        break;
                    }
                }
            }
        }

        // Set the application locale
        App::setLocale($locale);

        // Set the Laravel Carbon locale
        // \Carbon\Carbon::setLocale($locale);

        return $next($request);
    }
}
