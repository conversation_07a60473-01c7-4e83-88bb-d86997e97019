<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Vehicle;
use App\Models\VehicleEvent;
use App\Models\VehicleUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class VehicleController extends Controller
{
    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'search' => 'nullable|string|max:255',
            'status' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = Auth::user();
            
            $perPage = $request->get('per_page', 10);
            $search = $request->get('search');
            $status = $request->get('status');

            $vehicles = Vehicle::with(['driver:id,name'])
                ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                    $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
                })
                ->when($search, function ($query, $search) {
                    $query->where(function ($q) use ($search) {
                        $q->where('license_plate', 'like', "%{$search}%")
                          ->orWhere('model', 'like', "%{$search}%")
                          ->orWhereHas('driver', function ($driverQuery) use ($search) {
                              $driverQuery->where('name', 'like', "%{$search}%");
                          });
                    });
                })
                ->when($status !== null, function ($query) use ($status) {
                    $query->where('status', $status);
                })
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            $formattedVehicles = collect($vehicles->items())->map(function ($vehicle) {
                return [
                    'id' => $vehicle->id,
                    'license_plate' => $vehicle->license_plate,
                    'driver_name' => $vehicle->driver ? $vehicle->driver->name : null,
                    'model' => $vehicle->model,
                    'mileage' => $vehicle->mileage,
                    'type' => $vehicle->type ? __('translations.' . $vehicle->type) : null,
                    'icon_url' => $vehicle->icon ? asset('assets/images/icons/' . ($vehicle->icon ?? 'default') . '.svg') : null,
                    'status' => $vehicle->status,
                    'status_text' => $vehicle->status ? __('translations.active') : __('translations.inactive'),
                    'created_at' => Carbon::parse($vehicle->created_at)->format('d/m/Y H:i'),
                    'created_at_human' => Carbon::parse($vehicle->created_at)->diffForHumans(),
                ];
            });

            return jsonResponse(true, [
                'message' => __('app.vehicles_retrieved'),
                'vehicles' => $formattedVehicles,
                'pagination' => [
                    'total' => $vehicles->total(),
                    'per_page' => $vehicles->perPage(),
                    'current_page' => $vehicles->currentPage(),
                    'last_page' => $vehicles->lastPage(),
                ]
            ]);

        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
            ], 500);
        }
    }

    public function show($id)
    {
        try {
            $vehicle = Vehicle::with(['driver:id,name'])
                ->when(!Auth::user()->can('all_vehicles_access'), function ($query) {
                    $query->whereIn('id', VehicleUser::where('user_id', Auth::user()->id)->pluck('vehicle_id'));
                })
                ->findOrFail($id);

            $formattedVehicle = [
                'id' => $vehicle->id,
                'imei' => $vehicle->imei,
                'license_plate' => $vehicle->license_plate,
                'driver_name' => $vehicle->driver ? $vehicle->driver->name : null,
                'driver_id' => $vehicle->driver_id,
                'model' => $vehicle->model,
                'mileage' => $vehicle->mileage,
                'current_odometer_reading' => $vehicle->current_odometer_reading,
                'type' => $vehicle->type ? __('translations.' . $vehicle->type) : null,
                'type_raw' => $vehicle->type,
                'icon_url' => $vehicle->icon ? asset('assets/images/icons/' . ($vehicle->icon ?? 'default') . '.svg') : null,
                'year_of_registration' => $vehicle->year_of_registration,
                'vin' => $vehicle->vin,
                'status' => $vehicle->status,
                'status_text' => $vehicle->status ? __('translations.active') : __('translations.inactive'),
                'pin' => $vehicle->pin,
                'local_lock_ibutton' => $vehicle->local_lock_ibutton,
                'created_at' => Carbon::parse($vehicle->created_at)->format('d/m/Y H:i'),
                'updated_at' => Carbon::parse($vehicle->updated_at)->format('d/m/Y H:i'),
            ];

            return jsonResponse(true, [
                'message' => __('app.vehicle_retrieved'),
                'vehicle' => $formattedVehicle
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return jsonResponse(false, [
                'message' => __('app.vehicle_not_found'),
            ], 404);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
            ], 500);
        }
    }

    public function getEvents(Request $request, $vehicleId)
    {
        $validator = Validator::make(array_merge($request->all(), ['vehicle_id' => $vehicleId]), [
            'vehicle_id' => 'required|integer|exists:vehicles,id',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'event_type' => 'nullable|string',
            'maintenance_status' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            // Check if user has access to this vehicle
            $vehicle = Vehicle::when(!Auth::user()->can('all_vehicles_access'), function ($query) {
                $query->whereIn('id', VehicleUser::where('user_id', Auth::user()->id)->pluck('vehicle_id'));
            })->findOrFail($vehicleId);

            $perPage = $request->get('per_page', 15);
            $eventType = $request->get('event_type');
            $maintenanceStatus = $request->get('maintenance_status');

            $events = VehicleEvent::where('vehicle_id', $vehicleId)
                ->when($eventType, function ($query, $eventType) {
                    $query->where('event_type', $eventType);
                })
                ->when($maintenanceStatus, function ($query, $maintenanceStatus) {
                    $query->where('maintenance_status', $maintenanceStatus);
                })
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            $formattedEvents = collect($events->items())->map(function ($event) {
                return [
                    'id' => $event->id,
                    'event_type' => $event->event_type,
                    'other_event_type' => $event->other_event_type,
                    'description' => $event->description,
                    'start_date' => $event->start_date,
                    'end_date' => $event->end_date,
                    'is_under_maintenance' => $event->is_under_maintenance,
                    'maintenance_status' => $event->maintenance_status,
                    'maintenance_status_text' => $event->maintenance_status ? __('translations.' . $event->maintenance_status) : null,
                    'has_alert' => $event->has_alert,
                    'alert_recipients' => $event->alert_recipients ? json_decode($event->alert_recipients, true) : [],
                    'alert_type' => $event->alert_type ? json_decode($event->alert_type, true) : [],
                    'attachments' => $event->attachments ? json_decode($event->attachments, true) : [],
                    'created_at' => Carbon::parse($event->created_at)->format('d/m/Y H:i'),
                    'updated_at' => Carbon::parse($event->updated_at)->format('d/m/Y H:i'),
                ];
            });

            return jsonResponse(true, [
                'message' => __('app.vehicle_events_retrieved'),
                'events' => $formattedEvents,
                'vehicle' => [
                    'id' => $vehicle->id,
                    'license_plate' => $vehicle->license_plate,
                    'model' => $vehicle->model,
                ],
                'pagination' => [
                    'total' => $events->total(),
                    'per_page' => $events->perPage(),
                    'current_page' => $events->currentPage(),
                    'last_page' => $events->lastPage(),
                ]
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return jsonResponse(false, [
                'message' => __('app.vehicle_not_found'),
            ], 404);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
            ], 500);
        }
    }
}
