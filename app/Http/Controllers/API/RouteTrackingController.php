<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\VehicleRouteAssignment;
use Carbon\Carbon;
use Illuminate\Http\Request;

class RouteTrackingController extends Controller
{
    /**
     * Handle route updates (start, stop, end).
     */
    public function updateRoute(Request $request)
    {
        $request->validate([
            'route_id'  => 'required|integer',
            'type'      => 'required|string|in:start_point,stop_point,end_point',
            'timestamp' => 'required',
            'stop_id'   => 'nullable|integer',
            'io_data'   => 'nullable'
        ]);

        $route = VehicleRouteAssignment::with('stops')->find($request->route_id);
        if (!$route) {
            return response()->json(['error' => 'Route not found'], 404);
        }

        $fuelConsuption = null;
        // fuel consumption
        if (isset($request->io_data['33'])) {
            $fuelConsuption = $request->io_data['33'] ?? 0;
        } elseif (isset($request->io_data['83'])) {
            $fuelConsuption = $request->io_data['83'] ?? 0;
        } else {
            $fuelConsuption = $request->io_data['12'] ?? 0;
        }

        if ($request->type === 'start_point') {
            $route->start_point_odometer = $request->io_data['16'];
            $route->start_point_status = 'completed';
            $route->status = 'ongoing';
            $route->start_point_completed_at = Carbon::parse($request->timestamp);
            $route->start_point_fuel = $fuelConsuption;
        } elseif ($request->type === 'end_point') {
            $route->end_point_odometer = $request->io_data['16'];
            $route->status = 'completed';
            $route->end_point_status = 'completed';
            $route->end_point_completed_at = Carbon::parse($request->timestamp);
            $route->end_point_fuel = $fuelConsuption;
        } elseif ($request->type === 'stop_point' && $request->stop_id) {
            $route->status = 'ongoing';
            $stop = $route->stops()->where('id', $request->stop_id)->first();
            if ($stop) {
                $stop->stop_odometer = $request->io_data['16'];
                $stop->stop_fuel = $fuelConsuption;
                $stop->status = 'completed';
                $stop->completed_at = Carbon::parse($request->timestamp);
                $stop->save();
            }
        }

        $route->save();

        return response()->json(['message' => 'Route updated successfully'], 200);
    }

    /**
     * Mark a route as fully completed.
     */
    public function completeRoute(Request $request)
    {
        $request->validate([
            'route_id'  => 'required|integer',
            'timestamp' => 'required',
        ]);

        $route = VehicleRouteAssignment::find($request->route_id);
        if (!$route) {
            return response()->json(['error' => 'Route not found'], 404);
        }

        // Ensure all stops are completed before marking route as complete
        if ($route->stops()->where('status', 'pending')->exists()) {
            return response()->json(['error' => 'Some stops are still pending'], 400);
        }

        $route->status = 'completed';
        $route->end_point_completed_at = Carbon::parse($request->timestamp);
        $route->save();

        return response()->json(['message' => 'Route completed successfully']);
    }
}
