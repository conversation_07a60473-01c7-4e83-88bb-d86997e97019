<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        // validation
        $validator = Validator::make($request->all(), [
            'email' => 'required|exists:users,email|max:255',
            'password' => 'required',
        ]);

        // if validation fails
        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return jsonResponse(false, [
                    'message' => __('app.account_not_exist')
                ]);
            }

            if ($user->is_active == 0) {
                return response()->json([
                    'status' => false,
                    'message' => __('app.account_not_exist'),
                ]);
            }

            $credentials = $request->only(['email', 'password']);

            $login = Auth::attempt($credentials);
            if ($login) {
                return jsonResponse(true, [
                    'message' => 'Logged in successfully!',
                    'token' => $user->createToken($user->name)->plainTextToken,
                    'user' => $user,
                ]);
            }

            return jsonResponse(false, [
                'message' => __('app.incorrect_password'),
            ]);
        } catch (\Exception $e) {
            // Handle the authentication exception

            return jsonResponse(false, [
                'message' => __('app.auth_failed'),
            ]);
        }
    }

    public function getProfile()
    {
        $user = Auth::user();

        return jsonResponse(true, [
            'user' => $user,
        ]);
    }

    public function logout()
    {
        $user = Auth::user();

        $user->fcm_token = null;
        $user->save();

        $user->tokens()->delete();

        return jsonResponse(true, [
            'message' => __('app.logout_success'),
        ]);
    }

    public function updateFcmToken(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'fcm_token' => 'required',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $user = Auth::user();
        $user->fcm_token = $request->fcm_token;
        $user->save();

        return jsonResponse(true, [
            'message' => 'FCM token updated successfully!',
        ]);
    }

    public function updateLanguage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'language' => 'required',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $user = Auth::user();
        $user->language = $request->language;
        $user->save();

        return jsonResponse(true, [
            'message' => __('app.language_updated',[], $request->language),
        ]);
    }

    public function getNotifications(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $perPage = $request->get('per_page', 15);

            $notifications = \App\Models\Notification::when(Auth::user()->role != 'admin', function ($query) {
                $vehicles = \App\Models\Vehicle::whereHas('vehicleUsers', function ($query) {
                    $query->where('user_id', Auth::user()->id);
                })->pluck('id');
                $query->whereIn('vehicle_id', $vehicles)->orWhere('user_id', Auth::user()->id);
            })
            ->latest()
            ->paginate($perPage);

            $formattedNotifications = collect($notifications->items())->map(function ($notification) {
                $params = $notification->params ? json_decode($notification->params, true) : [];

                return [
                    'id' => $notification->id,
                    'title' => __('translations.' . $notification->title, $params),
                    'body' => __('translations.' . $notification->notification, $params),
                    'created_at' => \Carbon\Carbon::parse($notification->created_at)->format('d/m/Y H:i:s'),
                    'created_at_human' => \Carbon\Carbon::parse($notification->created_at)->diffForHumans(),
                ];
            });

            return jsonResponse(true, [
                'message' => __('app.notifications_retrieved'),
                'notifications' => $formattedNotifications,
                'pagination' => [
                    'total' => $notifications->total(),
                    'per_page' => $notifications->perPage(),
                    'current_page' => $notifications->currentPage(),
                    'last_page' => $notifications->lastPage(),
                ]
            ]);

        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
            ], 500);
        }
    }
}
