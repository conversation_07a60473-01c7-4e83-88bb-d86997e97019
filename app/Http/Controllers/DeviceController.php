<?php

namespace App\Http\Controllers;

use App\Models\Alarm;
use App\Models\Geofence;
use App\Models\Notification;
use App\Models\Vehicle;
use App\Models\VehicleGeofence;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DeviceController extends Controller
{
    public function logDeviceEvent(Request $request)
    {
        $imei = $request->get('imei');
        $data = $request->get('data');
        // Ensure eventID exists
        if (!isset($data['eventID']) || !$imei) {
            return response()->json(['status' => 'error', 'message' => 'eventID or imei not found'], 400);
        }

        $eventID = $data['eventID'];
        $alarmType = null;
        $alarmValue = null;
        $notificationTitle = null;
        $notificationMessage = null;

        switch ($eventID) {
            // case 249: // Jamming
            //     $jammingStatus = $data[249] ?? null;
            //     if ($jammingStatus !== null) {

            //         $notificationTitle = $jammingStatus == 1 ? 'jamming_started' : 'jamming_ended';
            //         $notificationMessage = $jammingStatus == 1 ? 'jamming_started_message' : 'jamming_ended_message';

            //         $alarmType = 'jamming';
            //         $alarmValue = $jammingStatus;
            //     }
            //     break;

            case 247: // Crash Detection
                $crashStatus = $data[247] ?? null;
                $crashMessages = [
                    1 => 'crash_detected_calibrated',
                    2 => 'limited_crash_unCalibrated',
                    3 => 'limited_crash_calibrated',
                    4 => 'full_crash_unCalibrated',
                    5 => 'full_crash_calibrated',
                    6 => 'crash_detected_unCalibrated',
                ];
                $notificationTitle = 'crash_detected';
                $notificationMessage = $crashMessages[$crashStatus] ?? 'crash_detected_generic';

            //     $alarmType = 'crash';
            //     $alarmValue = $crashStatus;
            //     break;


            // case 246: // Towing
            //     $towingStatus = $data[246] ?? null;
            //     $notificationTitle = 'towing_alarm';
            //     $notificationMessage = $towingStatus == 1 ? 'towing_detected' : 'vehicle_stopped';
            //     $alarmType = 'towing';
            //     $alarmValue = $towingStatus;
            //     break;

            case 252: // Unplug
                $unplugStatus = $data[252] ?? null;
                $notificationTitle = 'battery_status';
                $notificationMessage = $unplugStatus == 1 ? 'battery_disconnected' : 'battery_present';
                $alarmType = 'unplug';
                $alarmValue = $unplugStatus;
                break;

            // case 318: // GNSS Jamming
            //     $gnssJammingState = $data[318] ?? null;
            //     $gnssMessages = [
            //         0 => 'gnss_signal_stable',
            //         1 => 'gnss_interference_detected',
            //         2 => 'gnss_signal_lost',
            //     ];
            //     $notificationTitle = 'gnss_interference_alarm';
            //     $notificationMessage = $gnssMessages[$gnssJammingState] ?? 'gnss_interference_unknown';
            //     $alarmType = 'signal_lost';
            //     $alarmValue = $gnssJammingState;
            //     break;

            // // Device-based geofence events (Zone 1-5)
            case 155: // Zone 1 geofence event
            case 156: // Zone 2 geofence event
            case 157: // Zone 3 geofence event
            case 158: // Zone 4 geofence event
            case 159: // Zone 5 geofence event
                \Log::info('Device-based geofence event', json_encode($data));
                return $this->handleDeviceGeofenceEvent($imei, $eventID, $data);

            default:
                return response()->json(['status' => 'error', 'message' => 'Unknown eventID'], 200);
        }


        // Save the event and notification if a valid message was generated
        if ($alarmType) {
            $address = null;


            if (isset($data['longitude'], $data['latitude'])) {
                $longitude = $data['longitude'];
                $latitude = $data['latitude'];
                
               $address = getAddressFromCoordinates($latitude, $longitude);
            }

            $vehicle = Vehicle::where('imei', $imei)->first();

            if ($vehicle) {

                // Save the event
                Alarm::create([
                    'imei' => $imei,
                    'vehicle_id' => $vehicle->id ?? null,
                    'alarm_type' => $alarmType,
                    'alarm_value' => $alarmValue,
                    'longitude' => $data['longitude'] ?? null,
                    'latitude' => $data['latitude'] ?? null,
                    'location' => $address ?? null,
                ]);


                Notification::create([
                    'title' => $notificationTitle,
                    'notification' => $notificationMessage,
                    'vehicle_id' => $vehicle->id ?? null,
                    'params' => json_encode(['vehicle' => $vehicle?->license_plate, 'location' => $address])
                ]);
            }



            // $userId = $device->client?->user_id ?? null;
            // $numberPlate = $device->license_plate ?? $imei;


            // // Check if the user has enabled the notification for this event type
            // $user = User::find($userId);
            // $notificationPermissionField = strtolower(str_replace(' ', '_', $alarmType)); // Example: 'Tampering Alert' -> 'tampering_alert'
            // $userPermission = $user->$notificationPermissionField ?? false;


            // if ($userPermission == 1) {
            //     $fcmToken = $user->fcm_token ?? null;

            //     $detailedContent = "{$eventMessage}. Dispositivo: {$numberPlate}. Posizione: " . ($address ?? 'Coordinate: ' . $data['latitude'] . ', ' . $data['longitude']);

            //     // Save the notification
            //     Notification::create([
            //         'title' => $notificationTitle,
            //         'content' => $detailedContent,
            //         'type' => $alarmType,
            //         'imei' => $imei,
            //         'user_id' => $userId,
            //     ]);

            //     if ($fcmToken) {
            //         FCMService::sendNotification($fcmToken, $notificationTitle, $detailedContent);
            //     }
            // }

            return response()->json(['status' => 'success', 'message' => $notificationTitle]);
        }

        return response()->json(['status' => 'error', 'message' => 'Invalid event data'], 400);
    }

    /**
     * Handle device-based geofence events (eventIDs 155-159)
     */
    private function handleDeviceGeofenceEvent($imei, $eventID, $data)
    {
        // Map eventID to zone number
        $zoneNumber = $eventID - 154; // 155 -> 1, 156 -> 2, etc.

        // Get the geofence event value (0 = exit, 1 = enter)
        $eventValue = $data[$eventID] ?? null;

        if ($eventValue === null) {
            return response()->json(['status' => 'error', 'message' => 'Event value not found'], 400);
        }

        // Find the vehicle and geofence assignment
        $vehicle = Vehicle::where('imei', $imei)->first();
        if (!$vehicle) {
            return response()->json(['status' => 'error', 'message' => 'Vehicle not found'], 404);
        }

        // Find the geofence assignment for this IMEI and zone
        $vehicleGeofence = VehicleGeofence::with('geofence')
            ->where('vehicle_imei', $imei)
            ->where('zone_number', $zoneNumber)
            ->first();

        if (!$vehicleGeofence || !$vehicleGeofence->geofence) {
            return response()->json(['status' => 'error', 'message' => 'Geofence assignment not found for zone ' . $zoneNumber], 404);
        }

        $geofence = $vehicleGeofence->geofence;

        // Determine event type
        $eventType = $eventValue == 1 ? 'geofence_in' : 'geofence_exit';
        $notificationTitle = $eventValue == 1 ? 'geofence_entry' : 'geofence_exit';
        $notificationMessage = $eventValue == 1 ? 'geofence_entry_message' : 'geofence_exit_message';

        // Get location address if coordinates are available
        $address = null;
        if (isset($data['longitude'], $data['latitude'])) {
            $longitude = $data['longitude'];
            $latitude = $data['latitude'];
            $address = getAddressFromCoordinates($latitude, $longitude);
        }

        // Create alarm record
        Alarm::create([
            'imei' => $imei,
            'vehicle_id' => $vehicle->id,
            'alarm_type' => $eventType,
            'alarm_value' => $eventValue,
            'longitude' => $data['longitude'] ?? null,
            'latitude' => $data['latitude'] ?? null,
            'location' => $address,
            'geofence_id' => $geofence->id,
        ]);

        // Create notification record
        Notification::create([
            'title' => $notificationTitle,
            'notification' => $notificationMessage,
            'vehicle_id' => $vehicle->id,
            'params' => json_encode([
                'vehicle' => $vehicle->license_plate,
                'location' => $address,
                'geofence' => $geofence->name,
                'zone' => $zoneNumber
            ])
        ]);

        return response()->json([
            'status' => 'success',
            'message' => "Device geofence event processed: {$eventType} for zone {$zoneNumber}",
            'data' => [
                'event_type' => $eventType,
                'zone_number' => $zoneNumber,
                'geofence_name' => $geofence->name,
                'vehicle' => $vehicle->license_plate
            ]
        ]);
    }

    public function logDeviceGeofenceEvent(Request $request)
    {
        // Extract data
        $imei = $request->get('imei');

        $location = null;

        if ($request->has('latitude') && $request->has('longitude')) {
            $longitude = $request->get('longitude');
            $latitude = $request->get('latitude');
            $googleApiKey = env('GOOGLE_API_KEY'); // Fetch API key from environment variables
            $url = "https://maps.googleapis.com/maps/api/geocode/json?latlng={$latitude},{$longitude}&key={$googleApiKey}";
            $response = @file_get_contents($url); // Suppress warnings
            if ($response !== false) {
                $data = json_decode($response, true); // Convert to an associative array
                if ($data && $data['status'] == 'OK' && isset($data['results'][0])) {
                    $location = $data['results'][0]['formatted_address'] ?? null;
                }
            }
        }

        $vehicle = Vehicle::where('imei', $imei)->first();

        if ($vehicle) {
            $geofence = Geofence::where('id', $request->get('geofence_id'))->first();

            // Save the event
            Alarm::create([
                'imei' => $imei,
                'vehicle_id' => $vehicle->id ?? null,
                'alarm_type' => $request->get('type') . '_event',
                'longitude' => $request->get('longitude') ?? null,
                'latitude' => $request->get('latitude') ?? null,
                'location' => $location ?? null,
                'geofence_id' => $geofence->id ?? null,
            ]);


            Notification::create([
                'title' => $request->get('type'),
                'notification' => $request->get('type') . '_message',
                'vehicle_id' => $vehicle->id ?? null,
                'params' => json_encode(['vehicle' => $vehicle?->license_plate, 'location' => $location, 'geofence' => $geofence->name])
            ]);
        }


        // if ($event && $imei) {

        //     if ($device) {
        //         $notificationType = 'Geofence Exit';
        //         $notificationTitle = 'Allarme Uscita Geofence';
        //         $notificationMessage = "Il tuo veicolo {$identifier} è uscito dal geofencing.";


        //         $user = User::find($device?->client?->user_id);

        //         if ($user && $user->geofence_exit == 1) {
        //             // Save the notification
        //             Notification::create([
        //                 'title' => $notificationTitle,
        //                 'content' => $notificationMessage,
        //                 'type' => $notificationType,
        //                 'imei' => $imei,
        //                 'user_id' => $user->id ?? null, // Assumes a method to get user_id by IMEI
        //                 'is_geofence_event' => 1,
        //                 'response_status' => 'pending',
        //             ]);

        //             $fcmToken = $user->fcm_token ?? null;

        //             if ($fcmToken) {
        //                 FCMService::sendNotification($fcmToken, $notificationTitle, $notificationMessage);
        //             }


        //             // $deviceId = $device->id;
        //             // $this->logAction($deviceId, $eventMessage);


        //             // if ($device->client && $device->client?->phone_number) {
        //             //     $this->triggerAutomatedCall($device->client);
        //             // }
        //         }
        //     }
        // }


        return response()->json(['status' => 'success']);
    }

    public function saveCommandResponse(Request $request)
    {
        Log::info($request->all());
    }
}
