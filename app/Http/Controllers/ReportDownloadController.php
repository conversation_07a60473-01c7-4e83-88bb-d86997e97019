<?php

namespace App\Http\Controllers;

use App\Models\ExportedReport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ReportDownloadController extends Controller
{
    /**
     * Download an exported report file.
     */
  public function download(Request $request, $reportId)
{
    $report = ExportedReport::forUser(auth()->id())->findOrFail($reportId);

    if ($report->status !== 'completed') {
        abort(404, 'Report is not ready for download.');
    }

    if (!$report->fileExists()) {
        abort(404, 'Report file not found.');
    }

    $filePath = storage_path('app/public/' . $report->file_path);
    $fileName = $report->file_name;

    return new StreamedResponse(function () use ($filePath) {
        $stream = fopen($filePath, 'rb');
        while (!feof($stream)) {
            echo fread($stream, 1024 * 8);
            flush();
        }
        fclose($stream);
    }, 200, [
        'Content-Type' => mime_content_type($filePath),
        'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
    ]);
}
    
    /**
     * Stream an exported report file for preview.
     */
    public function preview(Request $request, $reportId)
    {
        $report = ExportedReport::forUser(auth()->id())->findOrFail($reportId);
        
        // Check if the report is completed
        if ($report->status !== 'completed') {
            abort(404, 'Report is not ready for preview.');
        }
        
        // Check if the file exists
        if (!$report->fileExists()) {
            abort(404, 'Report file not found.');
        }
        
        // Only allow PDF preview
        if ($report->file_type !== 'pdf') {
            return redirect()->route('reports.download', $reportId);
        }
        
        // Get the file content
        $fileContent = Storage::disk('public')->get($report->file_path);
        
        // Return the PDF for inline viewing
        return response($fileContent, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $report->file_name . '"'
        ]);
    }
}
