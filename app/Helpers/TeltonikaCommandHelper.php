<?php

namespace App\Helpers;

use App\Models\Geofence;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class TeltonikaCommandHelper
{
    /**
     * Zone parameter mappings for Teltonika devices
     */
    private static $zoneParameters = [
        1 => [
            'priority' => 20100,
            'operand' => 20101,
            'eventual_records' => 20102,
            'frame_border' => 20103,
            'shape_type' => 20104,
            'radius' => 20105,
            'x1' => 20106,
            'y1' => 20107,
            'x2' => 20108,
            'y2' => 20109,
            'overspeed' => 20110,
            'max_speed' => 20111,
            'sms_to' => 7025,
            'sms_text' => 8025,
        ],
        2 => [
            'priority' => 20120,
            'operand' => 20121,
            'eventual_records' => 20122,
            'frame_border' => 20123,
            'shape_type' => 20124,
            'radius' => 20125,
            'x1' => 20126,
            'y1' => 20127,
            'x2' => 20128,
            'y2' => 20129,
            'overspeed' => 20130,
            'max_speed' => 20131,
            'sms_to' => 7026,
            'sms_text' => 8026,
        ],
        3 => [
            'priority' => 20140,
            'operand' => 20141,
            'eventual_records' => 20142,
            'frame_border' => 20143,
            'shape_type' => 20144,
            'radius' => 20145,
            'x1' => 20146,
            'y1' => 20147,
            'x2' => 20148,
            'y2' => 20149,
            'overspeed' => 20150,
            'max_speed' => 20151,
            'sms_to' => 7027,
            'sms_text' => 8027,
        ],
        4 => [
            'priority' => 20160,
            'operand' => 20161,
            'eventual_records' => 20162,
            'frame_border' => 20163,
            'shape_type' => 20164,
            'radius' => 20165,
            'x1' => 20166,
            'y1' => 20167,
            'x2' => 20168,
            'y2' => 20169,
            'overspeed' => 20170,
            'max_speed' => 20171,
            'sms_to' => 7028,
            'sms_text' => 8028,
        ],
        5 => [
            'priority' => 20180,
            'operand' => 20181,
            'eventual_records' => 20182,
            'frame_border' => 20183,
            'shape_type' => 20184,
            'radius' => 20185,
            'x1' => 20186,
            'y1' => 20187,
            'x2' => 20188,
            'y2' => 20189,
            'overspeed' => 20190,
            'max_speed' => 20191,
            'sms_to' => 7029,
            'sms_text' => 8029,
        ],
    ];

    /**
     * Generate Teltonika command for geofence configuration
     *
     * @param int $zoneNumber Zone number (1-5)
     * @param Geofence|null $geofence Geofence object or null to clear zone
     * @return string Command string
     */
    public static function generateGeofenceCommand($zoneNumber, $geofence = null)
    {
        if ($zoneNumber < 1 || $zoneNumber > 5) {
            throw new \InvalidArgumentException('Zone number must be between 1 and 5');
        }

        $params = self::$zoneParameters[$zoneNumber];
        $commandParts = [];

        if ($geofence === null) {
            // Clear zone with default values
            $commandParts[] = $params['priority'] . ':0';
            $commandParts[] = $params['operand'] . ':0';
            $commandParts[] = $params['eventual_records'] . ':0';
            $commandParts[] = $params['shape_type'] . ':0';
            $commandParts[] = $params['radius'] . ':0';
            $commandParts[] = $params['x1'] . ':0';
            $commandParts[] = $params['y1'] . ':0';
            $commandParts[] = $params['x2'] . ':0';
            $commandParts[] = $params['y2'] . ':0';
        } else {
            // Configure zone with geofence data
            $geofenceData = json_decode($geofence->geofence_data, true);

            $commandParts[] = $params['priority'] . ':2'; // High priority
            $commandParts[] = $params['operand'] . ':3'; // On both entering and exiting
            $commandParts[] = $params['eventual_records'] . ':1'; // Enable eventual records

            if ($geofenceData['type'] === 'circle') {
                $commandParts[] = $params['shape_type'] . ':0'; // Circle
                $commandParts[] = $params['radius'] . ':' . round($geofenceData['radius']);
                $commandParts[] = $params['x1'] . ':' . $geofenceData['geofence']['lat'];
                $commandParts[] = $params['y1'] . ':' . $geofenceData['geofence']['lng'];
                $commandParts[] = $params['x2'] . ':' . $geofenceData['geofence']['lat'];
                $commandParts[] = $params['y2'] . ':' . $geofenceData['geofence']['lng'];
            } elseif ($geofenceData['type'] === 'rectangle') {
                $commandParts[] = $params['shape_type'] . ':1'; // Rectangle
                $commandParts[] = $params['radius'] . ':500'; // Default radius for rectangle
                $commandParts[] = $params['x1'] . ':' . $geofenceData['geofence']['south'];
                $commandParts[] = $params['y1'] . ':' . $geofenceData['geofence']['west'];
                $commandParts[] = $params['x2'] . ':' . $geofenceData['geofence']['north'];
                $commandParts[] = $params['y2'] . ':' . $geofenceData['geofence']['east'];
            }

        }

        return 'setparam ' . implode(';', $commandParts);
    }

    /**
     * Add command to queue for specific IMEI
     *
     * @param string $imei Device IMEI
     * @param string $command Command string
     * @return bool Success status
     */
    public static function addCommandToQueue($imei, $command)
    {
        try {
            $queuePath = public_path('command/queue.json');

            // Ensure directory exists
            $directory = dirname($queuePath);
            if (!File::exists($directory)) {
                File::makeDirectory($directory, 0755, true);
            }

            // Load existing queue
            $queue = [];
            if (File::exists($queuePath)) {
                $content = File::get($queuePath);
                $queue = json_decode($content, true) ?: [];
            }

            // Initialize IMEI array if not exists
            if (!isset($queue[$imei])) {
                $queue[$imei] = [];
            }

            // check if same command is already then skip
            if (in_array($command, array_column($queue[$imei], 'command'))) {
                return true;
            }

            // Add command to queue
            $queue[$imei][] = ['command' => $command];

            // Save updated queue
            File::put($queuePath, json_encode($queue, JSON_PRETTY_PRINT));

            Log::info('Command added to queue', [
                'imei' => $imei,
                'command' => $command
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to add command to queue', [
                'imei' => $imei,
                'command' => $command,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Generate and queue all geofence commands for a specific IMEI
     *
     * @param string $imei Device IMEI
     * @param array $geofenceAssignment Array of geofence assignment with zone number
     * @return bool Success status
     */
    public static function syncGeofenceCommands($imei, $geofenceAssignment = null)
    {
        try {
            // Configure geofence assignment
            if ($geofenceAssignment) {
                if (isset($geofenceAssignment['zone_number']) && isset($geofenceAssignment['geofence'])) {
                    $command = self::generateGeofenceCommand(
                        $geofenceAssignment['zone_number'],
                        $geofenceAssignment['geofence']
                    );
                    self::addCommandToQueue($imei, $command);
                }
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to sync geofence commands', [
                'imei' => $imei,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
