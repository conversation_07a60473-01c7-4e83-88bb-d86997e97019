<?php

namespace App\Livewire\Auth;

use App\Models\PasswordResetToken;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Locked;
use Livewire\Attributes\Title;
use Livewire\Component;

class ForgotPassword extends Component
{
    #[Layout("layouts.auth")]
    #[Title("Forgot Password - ControllOne")]
    public $email;

    #[Locked]
    public $user_email_id;

    public $otp;

    public $is_otp_sent = false;

    public $reset_password = false;

    public $password;
    public $password_confirmation;

    public function mount()
    {
        if (auth()->check() && auth()->user()) {
            return redirect()->route("dashboard");
        }
    }

    public function forgotPassword()
    {
        $this->validate([
            "email" => "required|max:255|exists:users,email",
        ]);

        $user = User::where("email", $this->email)->first();

        if ($user) {
            $this->user_email_id = $user->email;
            // $otp = mt_rand(100000, 999999);
            do {
                $otp = mt_rand(1000, 9999);

                // Check if the generated $otp already exists in the database
                $existingOTP = PasswordResetToken::where(
                    "token",
                    $otp
                )->first();
            } while ($existingOTP !== null);

            try {
                PasswordResetToken::updateOrCreate(
                    ["email" => $user->email],
                    [
                        "token" => $otp,
                        "created_at" => now(),
                    ]
                );
                $title =
                    __("translations.forgot_password_email") . " - ControllOne";
                $data = [
                    "email" => $user->email,
                    "user_id" => $user->id,
                    "title" => $title,
                    "otp" => $otp,
                ];

                Mail::send(
                    "mails.forgot-password",
                    ["data" => $data],
                    function ($message) use ($data) {
                        $message->to($data["email"])->subject($data["title"]);
                    }
                );

                $this->is_otp_sent = true;
                $this->dispatch(
                    "notify",
                    variant: "sucess",
                    title: "Oops!",
                    message: __("translations.forgot_password_otp_sent")
                );
            } catch (\Exception $e) {
                $this->dispatch(
                    "notify",
                    variant: "danger",
                    title: "Oops!",
                    message: $e->getMessage()
                );
            }
        } else {
            $this->dispatch(
                "notify",
                variant: "danger",
                title: "Oops!",
                message: __("translations.incorrect_email")
            );
        }
    }

    public function verifyOTP()
    {
        $this->validate([
            "otp" => "required|min:4|max:4",
        ]);

        PasswordResetToken::where(
            "created_at",
            "<",
            now()->subMinutes(15)
        )->delete();

        // Check if the OTP exists for the user
        $passwordResetOtp = PasswordResetToken::where(
            "email",
            $this->user_email_id
        )
            ->where("token", $this->otp)
            ->where("created_at", ">=", now()->subMinutes(15))
            ->first();

        if ($passwordResetOtp) {
            // OTP is valid, update the account_verified_at field in the user table
            $user = User::where("email", $passwordResetOtp->email)->first();
            $user->save();
            $passwordResetOtp->delete();

            $this->is_otp_sent = false;
            $this->reset_password = true;

            $this->dispatch(
                "notify",
                variant: "success",
                title: "Oops!",
                message: __("translations.otp_verified_success")
            );
        } else {
            $this->dispatch(
                "notify",
                variant: "warning",
                title: "Oops!",
                message: __("translations.otp_invalid_expired")
            );
        }
    }

    public function resendOTP()
    {
        $user = User::where("email", $this->user_email_id)->first();

        PasswordResetToken::where("email", $user->email)->delete();

        // Generate a random OTP
        // $otp = mt_rand(100000, 999999);
        do {
            $otp = mt_rand(1000, 9999);

            // Check if the generated $otp already exists in the database
            $existingOTP = PasswordResetToken::where("token", $otp)->first();
        } while ($existingOTP !== null);

        // Insert OTP data into the verification_otp table
        PasswordResetToken::create([
            "email" => $user->email, // Assuming you have access to the user object
            "token" => $otp,
            "created_at" => now(), // OTP expires in 15 minutes (adjust as needed)
        ]);

        $data = [
            "email" => $user->email,
            "title" =>
                __("translations.forgot_password_email") . " - ControllOne",
            "otp" => $otp,
        ];

        Mail::send("mails.forgot-password", ["data" => $data], function (
            $message
        ) use ($data) {
            $message->to($data["email"])->subject($data["title"]);
        });
        $this->dispatch(
            "notify",
            variant: "success",
            title: "Oops!",
            message: __("translations.otp_resent_success")
        );
    }

    public function resetPassword()
    {
        $this->validate([
            "password" => "required|min:6|confirmed",
        ]);

        $user = User::where("email", $this->user_email_id)->first();
        if ($user) {
            $user->password = Hash::make($this->password);
            $user->save();

            return redirect()
                ->route("login")
                ->with("success", __("translations.password_update_success"));
        } else {
            $this->dispatch(
                "notify",
                variant: "warning",
                title: "Oops!",
                message: __("translations.incorrect_email")
            );
        }
    }

    public function render()
    {
        return view("livewire.auth.forgot-password");
    }
}
