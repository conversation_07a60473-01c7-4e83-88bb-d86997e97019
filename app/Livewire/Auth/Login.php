<?php

namespace App\Livewire\Auth;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Attributes\Validate;
use Livewire\Component;

class Login extends Component
{
    #[Layout('layouts.auth')]
    #[Title('Login - ControllOne')]


    #[Validate('required|max:255|exists:users,email')]
    public $email;

    #[Validate('required|max:100')]
    public $password;

    public $remember_me = false;

    public function mount()
    {
        if (Auth::check() && Auth::user()) {
            return redirect()->route('fleet-management');
        }
    }

    function login()
    {
        $attemptsKey = 'login_attempts_' . sha1(request()->ip());

        // Check if the number of login attempts has exceeded the limit
        if (cache()->has($attemptsKey) && cache($attemptsKey) >= 5) {
            $this->dispatch('notify',  variant: 'warning', title: 'Oops!',  message: 'Too many login attempts. Try again later.');
        } else {
            $this->validate();
            if (auth()->attempt(['email' => $this->email, 'password' => $this->password], $this->remember_me)) {
                return redirect()->route('fleet-management');
            } else {

                $this->dispatch('notify',  variant: 'danger', title: 'Oops!',  message: 'Wrong Password!');

                $this->incrementLoginAttempts($attemptsKey);
            }
        }
    }

    function incrementLoginAttempts($key)
    {
        $attempts = cache($key, 0) + 1;
        cache([$key => $attempts], now()->addMinutes(10)); // Lock for 10 minutes
    }



    public function render()
    {
        return view('livewire.auth.login');
    }
}
