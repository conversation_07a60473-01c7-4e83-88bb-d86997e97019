<?php

namespace App\Livewire\Panel;

use App\Models\Driver;
use Livewire\Component;
use Livewire\WithPagination;

class Drivers extends Component
{
    use WithPagination;

    public $recordId, $deleteRecordId, $search, $filter_status = null, $selectedRecord,         $employment_date, $plant_type, $license_number, $ibutton_code;
    public $name, $status = true, $address, $phone;


    public function addDriver()
    {
        $this->reset(
            'recordId',
            'deleteRecordId',
            'name',
            'status',
            'address',
            'phone',
            'employment_date',
            'plant_type',
            'license_number',
            'ibutton_code'
        );

        $this->dispatch('open-modal', name: 'manage-driver');
    }

    public function editRecord($recordId)
    {
        $this->recordId = $recordId;

        if ($recordId) {
            $this->recordId = $recordId;
            $driver = Driver::find($this->recordId);

            if ($driver) {
                // Set existing driver data when editing
                $this->name = $driver->name ?? '';
                $this->phone = $driver->phone ?? '';
                $this->address = $driver->address ?? '';
                $this->status = $driver->status ? true : false;
                $this->employment_date = $driver->employment_date ?? '';
                $this->plant_type = $driver->plant_type ?? '';
                $this->license_number = $driver->license_number ?? '';
                $this->ibutton_code = $driver->ibutton_code ?? '';

                $this->dispatch('open-modal', name: 'manage-driver');
            }
        }
    }

    // Add or Update Driver
    public function addUpdateDriver()
    {
        $this->validate([
            'name' => 'required|max:255',
            'phone' => 'nullable|max:255',
            'address' => 'nullable|max:255',
            'employment_date' => 'nullable|max:255',
            'plant_type' => 'nullable|max:255',
            'license_number' => 'nullable|max:255',
            'ibutton_code' => 'nullable|max:255',
        ]);


        if ($this->recordId) {
            // Update existing client
            $driver = Driver::find($this->recordId);
            if ($driver) {
                $driver->name = $this->name;
                $driver->phone = $this->phone;
                $driver->address = $this->address;
                $driver->employment_date = $this->employment_date;
                $driver->plant_type = $this->plant_type;
                $driver->license_number = $this->license_number;
                $driver->ibutton_code = $this->ibutton_code;
                $driver->status = $this->status ? true : false;
                $driver->save();
            }
        } else {
            // Create new client
            Driver::create([
                'user_id' => auth()->id(),
                'name' => $this->name,
                'phone' => $this->phone,
                'address' => $this->address,
                'employment_date' => $this->employment_date,
                'plant_type' => $this->plant_type,
                'license_number' => $this->license_number,
                'ibutton_code' => $this->ibutton_code,
                'status' => $this->status ? true : false
            ]);
        }

        $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: $this->recordId ? 'Driver updated successfully!' : 'Driver created successfully!');


        $this->dispatch('close-modal');

        $this->reset();
    }

    public function deleteRecordConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-record-modal');
    }

    public function deleteRecord()
    {
        if ($this->deleteRecordId) {
            Driver::find($this->deleteRecordId)->delete();


            $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: 'Driver deleted successfully');

            $this->dispatch('close-modal');
            $this->reset('deleteRecordId');
        } else {
            $this->dispatch('notify',  variant: 'danger', title: 'Error!',  message: 'Driver Not Found');
        }
    }

    public function showRecord($id)
    {
        $this->selectedRecord = Driver::find($id);
        $this->dispatch('open-modal', name: 'show-record');
    }

    public function render()
    {
        $user = auth()->user();
        $drivers = Driver::when(!$user->can('all_drivers_access'), function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->when(isset($this->search), function ($query) {
            $query->where(function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('phone', 'like', '%' . $this->search . '%')
                    ->orWhere('license_number', 'like', '%' . $this->search . '%')
                    ->orWhere('ibutton_code', 'like', '%' . $this->search . '%')
                    ->orWhere('address', 'like', '%' . $this->search . '%');
            });
        })
            ->when(!empty($this->filter_status) || $this->filter_status == 2, function ($query) {
                $query->where('status', $this->filter_status == 2 ? 0 : $this->filter_status);
            })->latest()->paginate(10);

        return view('livewire.panel.drivers', compact('drivers'));
    }
}
