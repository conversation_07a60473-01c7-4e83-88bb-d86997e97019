<?php

namespace App\Livewire\Panel;

use App\Models\Driver;
use App\Models\Notification;
use App\Models\Vehicle;
use App\Models\VehicleUser;
use Carbon\Carbon;
use Livewire\Component;

class Dashboard extends Component
{
    public $notifications = [];

    public function mount()
    {
        $user = auth()->user();
        // Fetch latest notifications
        $this->notifications = Notification::when($user->role != 'admin', function ($query) use ($user) {
            $vehicles = Vehicle::whereHas('vehicleUsers', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->pluck('id');
            $query->whereIn('vehicle_id', $vehicles)->orWhere('user_id', $user->id);
        })->latest()->take(10)->get()->map(function ($notification) {
            if ($notification->params) {
                $params = json_decode($notification->params ?? '', true);
            } else {
                $params = [];
            }
            return [
                'title' =>   __('translations.' . $notification->title, $params),
                'body' =>   __('translations.' . $notification->notification, $params),
                'created_at' => Carbon::parse($notification->created_at)->diffForHumans(),
            ];
        });
    }

    public function render()
    {
        $user = auth()->user();
        $deviceData = @file_get_contents(public_path('data/live.json'));
        $deviceData = json_decode($deviceData, true);

        $movingCount = 0;
        $parkedCount = 0;
        $stoppedCount = 0;
        $totalFuel = 0;
        $averageFuel = 0;



        $imeis = array_keys($deviceData);


        $vehicles = Vehicle::with(['driver:id,name,user_id'])
            ->where('status', 1)
            ->whereIn('imei', $imeis)
            ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
            })
            ->get();

        $validImeis = $vehicles->pluck('imei')->toArray();

        if (is_array($deviceData)) {
            foreach ($deviceData as $data) {
                $imei = (string) $data['device_IMEI'] ?? null;
                if (!in_array($imei, $validImeis)) {
                    continue;
                }

                $movingStatus = $data['240'] ?? null;
                $ignitionStatus = $data['239'] ?? null;
                $speed = $data['speed'] ?? null;
                $totalFuelData = getFuelConsumption($data);
                $averageFuelData = getAverageFuelValue($totalFuelData, getOdometerValue($data), 'l_per_100km');

                $totalFuel += $totalFuelData;
                $averageFuel += $averageFuelData;

                if ($movingStatus == 1 && $speed > 0) {
                    $movingCount++;
                } elseif ($movingStatus == 0 && $ignitionStatus == 0) {
                    $parkedCount++;
                    $stoppedCount++;
                } elseif ($movingStatus == 0 && $ignitionStatus == 1) {
                    $stoppedCount++;
                }
            }
        }


        $vehicles->each(function ($vehicle) use ($deviceData) {
            $imei = (string) $vehicle->imei; // Ensure IMEI is a string
            $eachDeviceData = $deviceData[$imei] ?? null;


            if ($eachDeviceData) {
                $vehicle->movement_status = $eachDeviceData['240'] ?? null;
                $vehicle->ignition_status = $eachDeviceData['239'] ?? null;
                $vehicle->speed = $eachDeviceData['speed'] ?? null;
                $vehicle->timestamp = $eachDeviceData['last_update'] ?? null;
            }
        });


        $totalVehicles = Vehicle::when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
            $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
        })->where('status', 1)->count();

        $drivers = Driver::when(!$user->can('all_drivers_access'), function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->where('status', 1)->count();

        $anomalyVehicles = Vehicle::when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
            $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
        })->where('icon', 'anomaly')->count();

        $vehiclesInMaintenance = Vehicle::when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
            $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
        })->whereHas('events', function ($query) {
            $query->where('is_under_maintenance', 1)
                ->whereIn('maintenance_status', ['pending', 'in progress']);
        })->count();

        return view('livewire.panel.dashboard', compact(
            'totalVehicles',
            'drivers',
            'movingCount',
            'parkedCount',
            'stoppedCount',
            'vehicles',
            'totalFuel',
            'averageFuel',
            'anomalyVehicles',
            'vehiclesInMaintenance'
        ));
    }

    public function getAverageFuelValue($data)
    {
        if (isset($data['18'])) {
            return (float) $data['18'];
        } elseif (isset($data['135'])) {
            return (float) $data['135'];
        } elseif (isset($data['110'])) {
            return (float) $data['110'];
        } elseif (isset($data['60'])) {
            return (float) $data['60'];
        } elseif (isset($data['13'])) {
            return (float) $data['13'];
        } else {
            return 0;
        }
    }
}
