<?php

namespace App\Livewire\Panel;

use App\Models\ExportedReport;
use Livewire\Component;
use Livewire\WithPagination;

class ExportHistory extends Component
{
    use WithPagination;

    public $show = false;
    public $perPage = 10;

    protected $listeners = ['toggleExportHistory', 'refreshExportHistory'];

    public function mount($show = false)
    {
        $this->show = $show;
    }

    public function toggleExportHistory()
    {
        $this->show = !$this->show;
        
        if ($this->show) {
            $this->resetPage();
        }
    }

    public function refreshExportHistory()
    {
        // This method will be called by the polling to refresh the data
        $this->resetPage();
    }

    public function deleteExportedReport($reportId)
    {
        try {
            $report = ExportedReport::where('user_id', auth()->id())->findOrFail($reportId);
            
            // Delete the file from storage
            $report->deleteFile();
            
            // Delete the database record
            $report->delete();
            
            session()->flash('message', __('translations.export_deleted_successfully'));
            
            // Refresh the list
            $this->resetPage();
            
        } catch (\Exception $e) {
            session()->flash('error', __('translations.error_deleting_export'));
        }
    }

    public function render()
    {
        $exportHistory = collect();
        
        if ($this->show) {
            $exportHistory = ExportedReport::where('user_id', auth()->id())
                ->orderBy('created_at', 'desc')
                ->paginate($this->perPage);
        }

        return view('livewire.panel.export-history', [
            'exportHistory' => $exportHistory
        ]);
    }
}
