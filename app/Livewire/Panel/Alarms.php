<?php

namespace App\Livewire\Panel;

use App\Models\Alarm;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;

class Alarms extends Component
{
    use WithPagination;
    
    public $search;
    public $vehicle;
    public $geofence;
    public $alarmType;
    public $start_date;
    public $end_date;

    #[On('selectedVehicleUpdated')]
    public function updateSelectedVehicle($vehicle = null)
    {
        $this->vehicle = $vehicle;
    }


    #[On('selectedGeofenceUpdated')]
    public function updateSelectedGeofence($geofence = null)
    {
        $this->geofence = $geofence;
    }

    public function render()
    {
        $alarms = Alarm::query()
            ->when($this->search, function ($query) {
                $query->where('alarm_type', 'like', '%' . $this->search . '%')
                    ->orWhere('location', 'like', '%' . $this->search . '%');
            })
            ->when($this->vehicle, function ($query) {
                $query->where('vehicle_id', $this->vehicle);
            })
            ->when($this->geofence, function ($query) {
                $query->where('geofence_id', $this->geofence);
            })
            ->when($this->alarmType, function ($query) {
                $query->where('alarm_type', $this->alarmType);
            })
            ->when($this->start_date, function ($query) {
                $query->whereDate('created_at', '>=', $this->start_date);
            })
            ->when($this->end_date, function ($query) {
                $query->whereDate('created_at', '<=', $this->end_date);
            })
            ->latest()
            ->paginate(10);

        return view('livewire.panel.alarms', compact('alarms'));
    }
}
