<?php

namespace App\Livewire\Panel;

use Livewire\Component;
use Livewire\WithPagination;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class Roles extends Component
{
    use WithPagination;

    public $name;
    public $search;
    public $permissions = [];

    public $recordId;
    public $deleteRecordId;

    public $allPermissions = [];

    public function mount()
    {
        if (!auth()->user()->can('roles_management')) {
            abort(401);
        }
    }

    public function addRole()
    {
        $this->reset();
        $this->allPermissions = Permission::latest()->get();

        $this->dispatch('open-modal', name: 'manage-role');
    }


    public function addUpdateRole()
    {
        $this->validate([
            'name' => 'required|max:255|unique:roles,name,' . $this->recordId,
            'permissions' => 'required|min:1',
        ]);

        if ($this->recordId) {
            // Update existing role
            $role = Role::findOrFail($this->recordId);
            $role->update(['name' => $this->name]);
        } else {
            // Create new role
            $role = Role::create(['guard_name' => 'web', 'name' => $this->name]);
        }

        // Sync permissions (add/update)
        $role->syncPermissions($this->permissions);

        // Dispatch notifications & reset
        $this->dispatch('close-modal');
        $this->dispatch('notify', variant: 'success', title: 'Alert!', message: $this->recordId ? 'Role updated successfully!' : 'Role added successfully!');
        $this->reset('name', 'permissions', 'recordId');
    }


    public function editRecord($id)
    {
        $this->reset();

        $role = Role::find($id);
        if ($role) {
            $this->recordId = $role->id;
            $this->name = $role->name;
            $this->permissions = $role->permissions->pluck('name');

            $this->allPermissions = Permission::latest()->get();


            $this->dispatch('open-modal', name: 'manage-role');
        } else {
            $this->dispatch('notify',  variant: 'danger', title: 'Alert!',  message: 'Role not found!');
        }
    }


    public function deleteRecord()
    {
        $role = Role::find($this->deleteRecordId);
        if ($role) {
            $role->delete();

            $this->dispatch('notify',  variant: 'success', title: 'Alert!',  message: 'Role deleted successfully!');
            $this->dispatch('close-modal');
        }
    }

    public function deleteRecordConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-record-modal');
    }

    public function render()
    {
        $roles = Role::with('permissions')->when($this->search, function ($q) {
            $q->where('name', 'like', '%' . $this->search . '%');
        })->latest()->paginate(10);

        return view('livewire.panel.roles', compact('roles'));
    }
}
