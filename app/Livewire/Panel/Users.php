<?php

namespace App\Livewire\Panel;

use App\Models\Permission;
use App\Models\User;
use App\Models\VehicleUser;
use Illuminate\Support\Facades\Hash;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;
use Spatie\Permission\Models\Role;

class Users extends Component
{
    use WithPagination;

    public $recordId, $deleteRecordId, $search, $status, $selectedRecord;
    public $name, $email, $password, $is_active = true, $role;

    public $roles = [];
    public $userRoles = [];

    public $userId, $userVehicles = [], $userVehicle;


    public function addUser()
    {
        $this->reset('recordId', 'deleteRecordId', 'name', 'email', 'password', 'is_active', 'userRoles');

        $this->dispatch('open-modal', name: 'manage-user');
    }

    public function editRecord($recordId)
    {
        $this->recordId = $recordId;

        if ($recordId) {
            $this->recordId = $recordId;
            $user = User::find($this->recordId);

            if ($user) {
                // Set existing user data when editing
                $this->name = $user->name ?? '';
                $this->email = $user->email ?? '';
                $this->role = $user->role ?? 'user';
                $this->is_active = $user->is_active ? true : false;

                $this->userRoles = $user->roles->pluck('name');



                $this->dispatch('open-modal', name: 'manage-user');
            }
        }
    }

    // Add or Update User
    public function addUpdateUser()
    {
        $this->validate([
            'name' => 'required|max:255',
            'email' => 'required|max:255|unique:users,email' . ($this->recordId ? ',' . $this->recordId : ''),
            'password' =>  $this->recordId ? ['nullable', 'min:5'] : ['required', 'min:5'],
        ]);

        // Prepare the user data
        $userData = [
            'name' => $this->name,
            'email' => $this->email,
            'role' => $this->role ?? 'user',
            'password' => $this->password ? Hash::make($this->password) : null,
            'is_active' => $this->is_active ? true : false,
        ];

        if ($this->recordId) {
            // Update existing client
            $user = User::find($this->recordId);
            if ($user) {
                // Update the existing user
                $user = User::findOrFail($this->recordId);
                $user->update(array_filter($userData)); // Avoid overwriting the password if null

                $user->syncRoles($this->userRoles);
            }
        } else {
            // Create a new user
            $user = User::create($userData);
            $user->assignRole($this->userRoles);
        }



        $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: $this->recordId ? 'User updated successfully!' : 'User created successfully!');


        $this->dispatch('close-modal');

        $this->reset();
    }

    public function deleteRecordConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-record-modal');
    }

    public function deleteRecord()
    {
        if ($this->deleteRecordId) {
            User::find($this->deleteRecordId)->delete();


            $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: 'User deleted successfully');

            $this->dispatch('close-modal');
            $this->reset('deleteRecordId');
        } else {
            $this->dispatch('notify',  variant: 'danger', title: 'Error!',  message: 'User Not Found');
        }
    }

    public function showRecord($id)
    {
        $this->selectedRecord = User::with('roles')->find($id);
        $this->dispatch('open-modal', name: 'show-record');
    }

    public function addUserVehicle()
    {
        $this->resetExcept('userId', 'userVehicle');

        $this->dispatch('open-modal', name: 'add-user-vehicle');
    }



    public function showAssignedVehicles($userId)
    {
        $this->userId = $userId;

        $this->userVehicles = VehicleUser::with('vehicle')->where('user_id', $userId)->latest()->get();
        $this->dispatch('open-modal', name: 'user-vehicles');
    }

    public function deleteUserVehicleConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('close-modal');
        $this->dispatch('open-modal', name: 'delete-user-vehicle-record-modal');
    }


    public function deleteVehicleUser()
    {
        VehicleUser::find($this->deleteRecordId)->delete();
        $this->dispatch('notify',  variant: 'success', title: 'Alert!',  message: 'Vehicle user deleted successfully!');
        $this->resetExcept('userId');
        $this->showAssignedVehicles($this->userId);
    }

    #[On('userVehicleUpdated')]
    public function userVehicleUpdated($option = null)
    {
        $this->userVehicle = $option;
    }

    public function assignUserVehicle()
    {
        $this->validate(['userId' => 'required', 'userVehicle' => 'required']);

        if (!VehicleUser::where('vehicle_id', $this->userVehicle)->where('user_id', $this->userId)->exists()) {
            VehicleUser::create([
                'vehicle_id' => $this->userVehicle,
                'user_id' => $this->userId,
            ]);
            $this->dispatch('notify',  variant: 'success', title: 'Alert!',  message: 'Vehicle user assigned successfully!');
        } else {
            $this->dispatch('notify',  variant: 'warning', title: 'Alert!',  message: 'The vehicle to this user is already assigned!');
            return;
        }
        $this->resetExcept('userId');
        $this->dispatch('close-modal');
        $this->showAssignedVehicles($this->userId);
    }


    public function render()
    {
        $users = User::when(isset($this->search), function ($query) {
            $query->where('name', 'like', '%' . $this->search . '%')
                ->orWhere('role', 'like', '%' . $this->search . '%')
                ->orWhere('email', 'like', '%' . $this->search . '%');
        })
            ->when((isset($this->status) && empty($this->status)), function ($query) {
                $query->where('is_active', $this->status);
            })->latest()->paginate(10);

        if (count($this->roles) <= 0) {
            $this->roles = Role::pluck('name');
        }


        return view('livewire.panel.users', compact('users'));
    }
}
