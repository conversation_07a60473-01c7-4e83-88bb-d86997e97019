<?php

namespace App\Livewire\Panel;

use App\Models\Vehicle;
use App\Models\VehicleEvent;
use App\Models\VehicleUser;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class Vehicles extends Component
{
    use WithPagination, WithFileUploads;

    public $recordId, $deleteRecordId, $search, $filter_status, $selectedRecord;
    public $license_plate, $model, $type, $year_of_registration, $vin, $status = true, $imei, $icon, $mileage, $current_odometer_reading;
    public $driver;

    public $vehicleEvents;

    public $currentMonth;
    public $currentYear;

    public $vehicleId;
    public $event_type, $other_event_type, $description, $start_date, $end_date, $is_under_maintenance = false, $maintenance_status, $alert_recipients = [], $alert_type = [], $has_alert = false, $attachments = [], $old_attachments = [];

    public $vehicleUsers;
    public $vehicleUser;

    public function mount()
    {
        $this->currentMonth = Carbon::now()->month;
        $this->currentYear = Carbon::now()->year;
    }

    public function addVehicle()
    {
        $this->reset('recordId', 'deleteRecordId', 'license_plate', 'model', 'type', 'year_of_registration', 'vin', 'imei', 'driver', 'icon', 'mileage', 'current_odometer_reading');

        $this->dispatch('valueUpdated', null, 'getDrivers');


        $this->dispatch('open-modal', name: 'manage-vehicle');
    }

    #[On('selectedDriverUpdated')]
    public function selectedDriverUpdated($option = null)
    {
        $this->driver = $option;
    }

    #[On('vehicleUserUpdated')]
    public function vehicleUserUpdated($option = null)
    {
        $this->vehicleUser = $option;
    }

    public function editRecord($recordId)
    {
        $this->recordId = $recordId;

        if ($recordId) {
            $this->recordId = $recordId;
            $vehicle = Vehicle::find($this->recordId);

            if ($vehicle) {
                // Set existing vehicle data when editing
                $this->imei = $vehicle->imei ?? '';
                $this->license_plate = $vehicle->license_plate ?? '';
                $this->model = $vehicle->model ?? '';
                $this->type = $vehicle->type ?? '';
                $this->icon = $vehicle->icon ?? '';
                $this->mileage = $vehicle->mileage ?? '';
                $this->current_odometer_reading = $vehicle->current_odometer_reading ?? 0;
                $this->year_of_registration = $vehicle->year_of_registration ?? '';
                $this->vin = $vehicle->vin ?? '';
                $this->status = $vehicle->status ? true : false;
                $this->driver = $vehicle->driver_id ?? null;

                $this->dispatch('valueUpdated', $vehicle->driver_id, 'getDrivers');

                $this->dispatch('open-modal', name: 'manage-vehicle');
            }
        }
    }

    // Add or Update Vehicle
    public function addUpdateVehicle()
    {
        $this->validate([
            'imei' => 'required|max:255|unique:vehicles,imei,' . $this->recordId,
            'license_plate' => 'required|max:255|unique:vehicles,license_plate,' . $this->recordId,
            'model' => 'required|max:255',
            'type' => 'required|max:255',
            'icon' => 'nullable|max:50',
            'mileage' => 'nullable',
            'current_odometer_reading' => 'nullable',
            'year_of_registration' => 'nullable|max:255',
            'vin' => 'nullable|max:255',
            'driver' => 'nullable|unique:vehicles,driver_id,' . $this->recordId,
        ], [
            'driver.unique' => __('translations.driver_assigned_to_another_vehicle')
        ]);


        if ($this->recordId) {
            // Update existing client
            $vehicle = Vehicle::find($this->recordId);
            if ($vehicle) {
                $vehicle->imei = $this->imei;
                $vehicle->license_plate = $this->license_plate;
                $vehicle->model = $this->model;
                $vehicle->type = $this->type;
                $vehicle->icon = $this->icon;
                $vehicle->mileage = $this->mileage;
                $vehicle->current_odometer_reading = $this->current_odometer_reading;
                $vehicle->year_of_registration = $this->year_of_registration;
                $vehicle->vin = $this->vin;
                $vehicle->driver_id = $this->driver;
                $vehicle->status = $this->status ? true : false;
                $vehicle->save();
            }
        } else {
            // Create new client
            Vehicle::create([
                'imei' => $this->imei,
                'license_plate' => $this->license_plate,
                'model' => $this->model,
                'type' => $this->type,
                'icon' => $this->icon,
                'mileage' => $this->mileage,
                'current_odometer_reading' => $this->current_odometer_reading,
                'year_of_registration' => $this->year_of_registration,
                'vin' => $this->vin,
                'driver_id' => $this->driver,
                'status' => $this->status ? true : false
            ]);
        }

        $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: $this->recordId ? 'Vehicle updated successfully!' : 'Vehicle created successfully!');


        $this->dispatch('close-modal');

        $this->reset();
    }



    public function deleteRecordConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-record-modal');
    }

    public function deleteRecord()
    {
        if ($this->deleteRecordId) {
            Vehicle::find($this->deleteRecordId)->delete();


            $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: 'Vehicle deleted successfully');

            $this->dispatch('close-modal');
            $this->reset('deleteRecordId');
        } else {
            $this->dispatch('notify',  variant: 'danger', title: 'Error!',  message: 'Vehicle Not Found');
        }
    }

    public function showRecord($id)
    {
        $this->selectedRecord = Vehicle::find($id);
        $this->dispatch('open-modal', name: 'show-record');
    }

    public function showCalendar($vehicleId = null)
    {
        if (!$vehicleId) {
            $vehicleId = $this->vehicleId;
        }
        $startOfMonth = Carbon::create($this->currentYear, $this->currentMonth, 1)->startOfDay();
        $endOfMonth = Carbon::create($this->currentYear, $this->currentMonth, 1)->endOfMonth()->endOfDay();

        $query = VehicleEvent::where('vehicle_id', $vehicleId)
            ->where(function ($query) use ($startOfMonth, $endOfMonth) {
                $query->whereBetween('start_date', [$startOfMonth, $endOfMonth])
                    ->orWhereBetween('end_date', [$startOfMonth, $endOfMonth])
                    ->orWhere(function ($query) use ($startOfMonth, $endOfMonth) {
                        $query->where('start_date', '<', $startOfMonth)
                            ->where('end_date', '>', $endOfMonth);
                    });
            });

        // Check user permissions
        if (!auth()->user()->can('all_vehicle_events_view')) {
            $currentUserId = auth()->id();

            $query->where(function ($query) use ($currentUserId) {
                $query->where('user_id', $currentUserId);
                $query->orWhere(function ($query) use ($currentUserId) {
                    $query->whereJsonContains('alert_recipients', $currentUserId)
                        ->orWhereJsonContains('alert_recipients', (string)$currentUserId); // Handle both integer and string IDs in JSON
                });
            });
        }

        $this->vehicleEvents = $query->get([
            'id',
            'event_type',
            'description',
            'other_event_type',
            'start_date',
            'end_date',
            'is_under_maintenance',
            'maintenance_status'
        ]);

        $this->vehicleId = $vehicleId;
        $this->dispatch('open-modal', name: 'show-vehicle-events');
    }

    public function updateSelectedIcon($icon)
    {
        $this->icon = $icon;
    }

    public function previousMonth()
    {
        $this->currentMonth--;

        if ($this->currentMonth == 0) {
            $this->currentMonth = 12;
            $this->currentYear--;
        }
    }

    public function nextMonth()
    {
        $this->currentMonth++;

        if ($this->currentMonth == 13) {
            $this->currentMonth = 1;
            $this->currentYear++;
        }
    }

    public function manageEvent($id = null)
    {
        $this->reset(
            'recordId',
            'deleteRecordId',

            'event_type',
            'other_event_type',
            'description',
            'start_date',
            'end_date',
            'is_under_maintenance',
            'maintenance_status',
            'alert_recipients',
            'alert_type',
        );

        if ($id) {
            $this->recordId = $id;
            $vehicleEvent = VehicleEvent::find($id);

            if ($vehicleEvent) {
                $this->event_type = $vehicleEvent->event_type;
                $this->description = $vehicleEvent->description;
                $this->start_date = $vehicleEvent->start_date;
                $this->end_date = $vehicleEvent->end_date;
                $this->other_event_type = $vehicleEvent->other_event_type;
                $this->is_under_maintenance = $vehicleEvent->is_under_maintenance ? true : false;
                $this->has_alert = $vehicleEvent->has_alert ? true : false;
                $this->maintenance_status = $vehicleEvent->maintenance_status;
                $this->alert_recipients = json_decode($vehicleEvent->alert_recipients, true) ?? [];
                $this->alert_type = json_decode($vehicleEvent->alert_type, true) ?? [];
                $this->old_attachments = json_decode($vehicleEvent->attachments, true) ?? [];

                $this->dispatch('multiValueUpdated', $this->alert_recipients, 'getUsers');
            } else {
                $this->dispatch('notify',  variant: 'danger', title: 'Error!',  message: 'Event Not Found');
            }
        }

        $this->dispatch('open-modal', name: 'manage-event');
    }

    public function addUpdateVehicleEvent()
    {
        $this->validate([
            'event_type' => 'required|max:255',
            'description' => 'nullable|max:500',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'alert_type' => 'nullable|max:255',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|mimes:png,jpg,jpeg,docx,pdf,xlsx,csv',
        ]);

        // Initialize an array for storing attachment details
        $attachmentsData = [];

        if (!empty($this->attachments)) {
            foreach ($this->attachments as $attachment) {
                // Store each file and generate its path
                $path = $attachment->store('vehicle_events', 'public');

                // Add the attachment details to the array
                $attachmentsData[] = [
                    'original_file_name' => $attachment->getClientOriginalName(),
                    'file_path' => $path,
                ];
            }
        }

        if ($this->recordId) {
            // Update existing event
            $vehicleEvent = VehicleEvent::find($this->recordId);
            if ($vehicleEvent) {
                $vehicleEvent->event_type = $this->event_type;
                $vehicleEvent->other_event_type = $this->other_event_type;
                $vehicleEvent->description = $this->description;
                $vehicleEvent->start_date = $this->start_date;
                $vehicleEvent->end_date = $this->end_date;
                $vehicleEvent->is_under_maintenance = $this->is_under_maintenance ? true : false;
                $vehicleEvent->maintenance_status = $this->maintenance_status;
                $vehicleEvent->alert_recipients = json_encode($this->alert_recipients ?? []);
                $vehicleEvent->alert_type = json_encode($this->alert_type);
                $vehicleEvent->has_alert = $this->has_alert ? true : false;

                $vehicleEvent->attachments = json_encode($attachmentsData);

                $vehicleEvent->save();
            }
        } else {
            // create new event
            VehicleEvent::create([
                'user_id' => auth()->id(),
                'event_type' => $this->event_type,
                'other_event_type' => $this->other_event_type,
                'description' => $this->description,
                'start_date' => $this->start_date,
                'end_date' => $this->end_date,
                'is_under_maintenance' => $this->is_under_maintenance ? true : false,
                'maintenance_status' => $this->maintenance_status,
                'alert_recipients' => json_encode($this->alert_recipients ?? []),
                'alert_type' => json_encode($this->alert_type),
                'vehicle_id' => $this->vehicleId,
                'has_alert' => $this->has_alert ? true : false,
                'attachments' => json_encode($attachmentsData),
            ]);
        }

        $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: $this->recordId ? 'Event updated successfully!' : 'Event created successfully!');
        $this->dispatch('close-modal');
        $this->resetExcept('vehicleId');
        $this->showCalendar();
    }

    public function deleteEvent()
    {
        if ($this->recordId) {
            $vehicleEvent = VehicleEvent::find($this->recordId);
            if ($vehicleEvent) {
                $vehicleEvent->delete();
            }
        }

        $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: 'Event deleted successfully!');
        $this->dispatch('close-modal');
        $this->resetExcept('vehicleId');
        $this->showCalendar();
    }


    #[On('selectedUsersUpdated')]
    public function updateSelectedUsers($selectedUsers = [])
    {
        $this->alert_recipients = $selectedUsers;
    }

    public function showVehicleUsers($vehicleId)
    {
        $this->vehicleId = $vehicleId;

        $this->vehicleUsers = Vehicle::with('vehicleUsers.user')->find($vehicleId);
        $this->dispatch('open-modal', name: 'vehicle-users');
    }


    public function addVehicleUser()
    {
        $this->resetExcept('vehicleId', 'vehicleUsers');

        $this->dispatch('open-modal', name: 'add-vehicle-user');
    }


    public function assignVehicleUser()
    {
        $this->validate(['vehicleId' => 'required', 'vehicleUser' => 'required']);

        if (!VehicleUser::where('vehicle_id', $this->vehicleId)->where('user_id', $this->vehicleUser)->exists()) {
            VehicleUser::create([
                'vehicle_id' => $this->vehicleId,
                'user_id' => $this->vehicleUser,
            ]);
            $this->dispatch('notify',  variant: 'success', title: 'Alert!',  message: 'Vehicle user assigned successfully!');
        } else {
            $this->dispatch('notify',  variant: 'warning', title: 'Alert!',  message: 'The vehicle to this user si already assigned!');
            return;
        }
        $this->resetExcept('vehicleId');
        $this->dispatch('close-modal');
        $this->showVehicleUsers($this->vehicleId);
    }


    public function deleteVehicleUserConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-vehicle-user-record-modal');
    }


    public function deleteVehicleUser()
    {
        VehicleUser::find($this->deleteRecordId)->delete();
        $this->dispatch('notify',  variant: 'success', title: 'Alert!',  message: 'Vehicle user deleted successfully!');
        $this->resetExcept('vehicleId');
        $this->showVehicleUsers($this->vehicleId);
    }

    public function deleteAttachmentConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-attachment-record-modal');
    }


    public function deleteAttachment()
    {
        $index = $this->deleteRecordId;
        // Ensure the index is valid
        if (!isset($this->old_attachments[$index])) {
            $this->dispatch('notify', variant: 'error', title: 'Error!', message: 'Invalid attachment selected.');
            return;
        }

        // Fetch the selected attachment
        $attachmentToDelete = $this->old_attachments[$index];

        // Remove the file from storage
        if (isset($attachmentToDelete['file_path'])) {
            Storage::disk('public')->delete($attachmentToDelete['file_path']);
        }

        // Remove the attachment from the array
        unset($this->old_attachments[$index]);

        // Re-index the array (optional, for consistent indexing)
        $this->old_attachments = array_values($this->old_attachments);

        // Save the updated JSON back to the database
        if ($this->recordId) {
            $vehicleEvent = VehicleEvent::find($this->recordId);
            if ($vehicleEvent) {
                $vehicleEvent->attachments = json_encode($this->old_attachments);
                $vehicleEvent->save();
            }
        }

        // Notify the user of success
        $this->dispatch('notify', variant: 'success', title: 'Success!', message: 'Attachment deleted successfully!');
        $this->dispatch('close-modal');
        $this->resetExcept('recordId');
        $this->manageEvent($this->recordId);
    }

    public function render()
    {
        $user = auth()->user();
        $vehicles = Vehicle::when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
            $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
        })
            ->when(isset($this->search), function ($query) {
                $query->where(function ($query) {
                    $query->where('license_plate', 'like', '%' . $this->search . '%')
                        ->orWhere('imei', 'like', '%' . $this->search . '%')
                        ->orWhere('model', 'like', '%' . $this->search . '%')
                        ->orWhere('type', 'like', '%' . $this->search . '%')
                        ->orWhere('icon', 'like', '%' . $this->search . '%')
                        ->orWhereHas('driver', function ($query) {
                            $query->where('name', 'like', '%' . $this->search . '%');
                        });
                });
            })
            ->when((isset($this->filter_status) && empty($this->filter_status)), function ($query) {
                $query->where('status', $this->filter_status);
            })->with(['events' => function ($query) {
                $query->where('is_under_maintenance', 1)
                    ->whereIn('maintenance_status', ['pending', 'in progress'])
                    ->select(['id', 'vehicle_id', 'maintenance_status']);
            }])->latest()->paginate(10);


        $daysInMonth = Carbon::createFromDate($this->currentYear, $this->currentMonth, 1)->daysInMonth;

        $firstDayOfMonth = Carbon::createFromDate($this->currentYear, $this->currentMonth, 1)->dayOfWeek;
        $startDay = $firstDayOfMonth === 0 ? 7 : $firstDayOfMonth;

        $endDay = 6 - Carbon::createFromDate($this->currentYear, $this->currentMonth, $daysInMonth)->dayOfWeek;

        return view('livewire.panel.vehicles', compact('vehicles', 'daysInMonth', 'startDay', 'endDay', 'user'));
    }
}
