<?php

namespace App\Livewire\Panel;

use App\Models\Vehicle;
use App\Models\VehicleUser;
use Livewire\Component;

class RemoteControl extends Component
{
    public $deviceData;
    public $devices = [];
    public $filter = 'all'; // Default filter
    public $search = null;
    public $selectedDevice = null;
    public $pin = null;
    public $local_lock_ibutton = false;
    public $can_lock_unlock = false;


    public function mount()
    {
        $deviceDataPath = public_path('data/live.json');
        $deviceData = @file_get_contents($deviceDataPath);
        $this->deviceData = json_decode($deviceData, true);
    }

    public function setFilter($filter)
    {
        $this->filter = $filter;
    }

    public function selectDevice($deviceId)
    {

        $this->selectedDevice = Vehicle::where('id', $deviceId)->with(['driver:id,name'])
            ->where('status', 1)->first();
        if (auth()->user()->can('remote_control_pin_manage')) {
            $this->can_lock_unlock = true;
            $this->pin = $this->selectedDevice?->pin;
            $this->local_lock_ibutton = $this->selectedDevice?->local_lock_ibutton ? true : false;
        }
    }



    public function updatePin()
    {
        $this->validate([
            'pin' => 'nullable|min:4',
            'local_lock_ibutton' => 'nullable|max:10',
        ]);

        Vehicle::findOrFail($this->selectedDevice?->id)->update([
            'pin' => $this->pin,
            'local_lock_ibutton' => $this->local_lock_ibutton,
        ]);

        $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: 'Settings updated successfully!');
    }

    public function checkPin()
    {
        $this->validate([
            'pin' => 'nullable|min:4',
        ]);

        $pin = Vehicle::findOrFail($this->selectedDevice?->id)->pin;
        if ($pin == $this->pin) {
            $this->can_lock_unlock = true;

            $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: 'Pin verification successfull!');
        } else {
            $this->can_lock_unlock = false;

            $this->dispatch('notify',  variant: 'danger', title: 'Error!',  message: 'Incorrect pin!');
        }
    }


    public function render()
    {
        // Extract IMEI keys from deviceData
        $imeis = array_keys($this->deviceData);

        // Fetch vehicles matching the IMEIs and load driver relation
        $vehicles = Vehicle::with(['driver:id,name'])
            ->where('status', 1)
            ->whereIn('imei', $imeis)
            ->when(!auth()->user()->can('all_vehicles_access'), function ($query) {
                $query->whereIn('id', VehicleUser::where('user_id', auth()->user()->id)->pluck('vehicle_id'));
            })
            ->when(isset($this->search), function ($query) {
                $query->where(function ($query) {
                    $query->where('license_plate', 'like', '%' . $this->search . '%')
                        ->orWhere('imei', 'like', '%' . $this->search . '%')
                        ->orWhere('model', 'like', '%' . $this->search . '%')
                        ->orWhere('type', 'like', '%' . $this->search . '%')
                        ->orWhereHas('driver', function ($query) {
                            $query->where('name', 'like', '%' . $this->search . '%');
                        });
                });
            })
            ->get();

        // Attach additional data to vehicles for map
        $vehicles->each(function ($vehicle) {
            $imei = $vehicle->imei;
            $deviceData = $this->deviceData[$imei] ?? null;
            if ($deviceData) {
                $vehicle->latitude = $deviceData['latitude'] ?? null;
                $vehicle->longitude = $deviceData['longitude'] ?? null;
                $vehicle->movement_status = $deviceData['240'] ?? null;
                $vehicle->ignition_status = $deviceData['239'] ?? null;
                $vehicle->timestamp = $deviceData['timestamp'] ?? null;
            }
        });

        // Use collection's groupBy to group by 'type'
        $this->devices = $vehicles->groupBy('type')->all();


        // Apply filter logic
        $filteredDevices = [];
        foreach ($this->devices as $type => $vehicles) {
            $filteredDevices[$type] = $vehicles->filter(function ($vehicle) {
                $device = $this->deviceData[$vehicle->imei] ?? null;
                if (!$device) return false;

                $movingStatus = $device['240'] ?? null;
                $ignitionStatus = $device['239'] ?? null;

                return match ($this->filter) {
                    'moving' => $movingStatus === 1,
                    'parked' => $movingStatus === 0 && $ignitionStatus !== 1,
                    'stopped' => $movingStatus === 0 && $ignitionStatus === 1,
                    default => true, // 'all'
                };
            });
        }
        $this->devices = $filteredDevices;

        return view('livewire.panel.remote-control');
    }
}
