<?php

namespace App\Livewire\Panel;

use App\Exports\ReportExport;
use App\Jobs\ProcessReportExport;
use App\Models\Alarm;
use App\Models\ExportedReport;
use App\Models\VehicleUser;
use App\Services\FuelReportService;
use App\Services\RouteReportService;
use App\Services\MultiDayReportService;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class Reporting extends Component
{
    use WithPagination;

    // Existing properties
    public $vehicle;
    public $date;
    public $geofences;
    public $alarmType;
    public $reporting_type = "routes";
    public $generate_report = false;

    // New properties for enhanced reporting
    public $period_type = "daily"; // daily, monthly, custom
    public $start_date;
    public $end_date;
    public $selected_month;
    public $selected_year;
    public $show_export_history = false;



    public function mount()
    {
        $this->date = now()->format('Y-m-d');
        $this->selected_month = now()->month;
        $this->selected_year = now()->year;
        $this->start_date = now()->startOfMonth()->format('Y-m-d');
        $this->end_date = now()->format('Y-m-d');
    }

    protected function rules()
    {
        return [
            'vehicle' => 'required',
            'period_type' => 'required|in:daily,monthly,custom',
            'date' => 'required_if:period_type,daily|date',
            'start_date' => 'required_if:period_type,custom|date',
            'end_date' => 'required_if:period_type,custom|date|after_or_equal:start_date',
            'selected_month' => 'required_if:period_type,monthly|integer|between:1,12',
            'selected_year' => 'required_if:period_type,monthly|integer|min:2020|max:' . (date('Y') + 1),
        ];
    }





    // Get Mileage based on vehicle type
    private function getMileageForVehicle($vehicleType)
    {
        return match ($vehicleType) {
            'truck', 'bus', 'tractor' => 3,
            'car', 'bike', 'scooter', 'van', 'ambulance' => 11,
            default => 0,
        };
    }


    #[On('selectedVehicleUpdated')]
    public function updateSelectedVehicle($vehicle = null)
    {
        $this->vehicle = $vehicle;
    }

    #[On('selectedGeofencesUpdated')]
    public function updateSelectedGeofences($geofences = null)
    {
        $this->geofences = $geofences;
    }

    public function updatedPeriodType()
    {
        // Reset generate_report when period type changes
        $this->generate_report = false;

        // Set default dates based on period type
        if ($this->period_type === 'monthly') {
            $this->start_date = Carbon::create($this->selected_year, $this->selected_month, 1)->format('Y-m-d');
            $this->end_date = Carbon::create($this->selected_year, $this->selected_month, 1)->endOfMonth()->format('Y-m-d');
        } elseif ($this->period_type === 'daily') {
            $this->start_date = $this->date;
            $this->end_date = $this->date;
        }
    }

    public function updatedSelectedMonth()
    {
        if ($this->period_type === 'monthly') {
            $this->start_date = Carbon::create($this->selected_year, $this->selected_month, 1)->format('Y-m-d');
            $this->end_date = Carbon::create($this->selected_year, $this->selected_month, 1)->endOfMonth()->format('Y-m-d');
        }
    }

    public function updatedSelectedYear()
    {
        if ($this->period_type === 'monthly') {
            $this->start_date = Carbon::create($this->selected_year, $this->selected_month, 1)->format('Y-m-d');
            $this->end_date = Carbon::create($this->selected_year, $this->selected_month, 1)->endOfMonth()->format('Y-m-d');
        }
    }

    public function updatedStartDate()
    {
        // Validate custom date range doesn't exceed 31 days
        if ($this->period_type === 'custom' && $this->start_date && $this->end_date) {
            $start = Carbon::parse($this->start_date);
            $end = Carbon::parse($this->end_date);

            if ($end->diffInDays($start) > 31) {
                $this->end_date = $start->copy()->addDays(31)->format('Y-m-d');
                $this->dispatch('notify', variant: 'warning', title: 'Warning!', message: 'Date range limited to 31 days maximum.');
            }
        }
    }

    public function updatedEndDate()
    {
        // Validate custom date range doesn't exceed 31 days
        if ($this->period_type === 'custom' && $this->start_date && $this->end_date) {
            $start = Carbon::parse($this->start_date);
            $end = Carbon::parse($this->end_date);

            if ($end->diffInDays($start) > 31) {
                $this->start_date = $end->copy()->subDays(31)->format('Y-m-d');
                $this->dispatch('notify', variant: 'warning', title: 'Warning!', message: 'Date range limited to 31 days maximum.');
            }
        }
    }

    public function generateReport()
    {
        $this->validate();

        // For monthly and custom range reports, queue for background processing
        if ($this->period_type !== 'daily') {
            $dateRange = $this->getDateRange();
            $this->createQueuedExport('pdf', $dateRange);
        }else{
            // For daily reports, generate immediately
            $this->generate_report = true;
            $this->show_export_history = false;
            $this->dispatch('toggleExportHistory');
        }

    }

    public function toggleExportHistory()
    {
        $this->show_export_history = !$this->show_export_history;
        $this->dispatch('toggleExportHistory');
    }


    public function render()
    {
        $user = auth()->user();
        $reports = collect();
        $alarms = collect();
        $fuelReports = collect();

        $reports = new LengthAwarePaginator($reports->forPage(1, 10), $reports->count(), 10);
        $alarms = new LengthAwarePaginator($alarms->forPage(1, 10), $alarms->count(), 10);
        $fuelReports = new LengthAwarePaginator($fuelReports->forPage(1, 10), $fuelReports->count(), 10);

        if ($this->generate_report == true) {
            // Determine the date range based on period type
            $dateRange = $this->getDateRange();

            if ($this->reporting_type == 'routes') {
                $reports = $this->generateRouteReportsForPeriod($dateRange);
            } elseif ($this->reporting_type == 'alarms') {
                $alarms = $this->generateAlarmsReportsForPeriod($user, $dateRange);
            } elseif ($this->reporting_type == 'fuel_consumption') {
                $fuelReports = $this->generateFuelReportsForPeriod($dateRange);
            }
            $this->generate_report = false;
        }

        return view('livewire.panel.reporting', compact('reports', 'alarms', 'fuelReports'));
    }

    /**
     * Get date range based on period type
     */
    private function getDateRange(): array
    {
        switch ($this->period_type) {
            case 'daily':
                return [
                    'start' => $this->date,
                    'end' => $this->date,
                    'type' => 'daily'
                ];

            case 'monthly':
                $start = Carbon::create($this->selected_year, $this->selected_month, 1);
                return [
                    'start' => $start->format('Y-m-d'),
                    'end' => $start->endOfMonth()->format('Y-m-d'),
                    'type' => 'monthly'
                ];

            case 'custom':
                return [
                    'start' => $this->start_date,
                    'end' => $this->end_date,
                    'type' => 'custom'
                ];

            default:
                return [
                    'start' => $this->date,
                    'end' => $this->date,
                    'type' => 'daily'
                ];
        }
    }

    public function exportToExcel()
    {
        return $this->queueExport('excel');
    }

    public function exportToPDF()
    {
        return $this->queueExport('pdf');
    }

    /**
     * Queue export job for background processing
     */
    private function queueExport($fileType)
    {
        $this->validate();

        $dateRange = $this->getDateRange();

        // Check if this is a large report that should be processed in background
        $shouldQueue = $this->shouldQueueExport($dateRange);

        if ($shouldQueue) {
            return $this->createQueuedExport($fileType, $dateRange);
        } else {
            return $this->createImmediateExport($fileType, $dateRange);
        }
    }

    /**
     * Determine if export should be queued based on date range
     */
    private function shouldQueueExport($dateRange): bool
    {
        $start = Carbon::parse($dateRange['start']);
        $end = Carbon::parse($dateRange['end']);
        $daysDiff = $end->diffInDays($start);

        // Queue if more than 1 day or if it's a monthly report
        return $daysDiff > 1 || $dateRange['type'] === 'monthly';
    }

    /**
     * Create queued export for large reports
     */
    private function createQueuedExport($fileType, $dateRange)
    {
        $fileName = $this->generateFileName($fileType, $dateRange);

        $exportedReport = ExportedReport::create([
            'user_id' => auth()->id(),
            'vehicle_id' => $this->vehicle,
            'report_type' => $this->reporting_type,
            'period_type' => $this->period_type,
            'start_date' => $dateRange['start'],
            'end_date' => $dateRange['end'],
            'file_name' => $fileName,
            'file_type' => $fileType,
            'status' => 'pending',
        ]);

        // Dispatch the job
        ProcessReportExport::dispatch($exportedReport);

        $this->dispatch('notify', variant: 'success', title: 'Success!', message: 'Report export has been queued. You will be able to download it once processing is complete.');

        $this->toggleExportHistory();
    }

    /**
     * Create immediate export for small reports
     */
    private function createImmediateExport($fileType, $dateRange)
    {
        $data = $this->prepareExportDataForRange($dateRange);
        $fileName = $this->generateFileName($fileType, $dateRange);

        $reportFile = $this->createReportFile($data, $fileType, $fileName, $dateRange);
    
        ExportedReport::create([
            'user_id' => auth()->id(),
            'vehicle_id' => $this->vehicle,
            'report_type' => $this->reporting_type,
            'period_type' => $this->period_type,
            'start_date' => $dateRange['start'],
            'end_date' => $dateRange['end'],
            'file_name' => $fileName,
            'file_type' => $fileType,
            'file_path' => $reportFile['file_path'],
            'file_size' => $reportFile['file_size'],
            'status' => 'completed',
        ]);

        if ($fileType === 'excel') {
            return Excel::download(new ReportExport($data, $this->reporting_type, $dateRange['start']), $fileName);
        } else {
            $pdf = Pdf::loadView('exports.report', [
                'data' => $data,
                'reporting_type' => $this->reporting_type,
                'period' => $dateRange['start'] . ' - ' . $dateRange['end']
            ]);
            return response()->streamDownload(fn() => print($pdf->stream()), $fileName);
        }
    }

    private function createReportFile($data, $fileType, $fileName, $dateRange){
        $directory = 'reports/' . date('Y/m');
        $filePath = $directory . '/' . $fileName;

        // Ensure directory exists
        Storage::disk('public')->makeDirectory($directory);

        if ($fileType === 'excel') {
            Excel::store(
                new ReportExport($data, $this->reporting_type, $dateRange['start']),
                $filePath,
                'public'
            );
        } else { // PDF
            $pdf = Pdf::loadView('exports.report', [
                'data' => $data,
                'reporting_type' => $this->reporting_type,
                'period' => $dateRange['start'] . ' - ' . $dateRange['end']
            ]);
            Storage::disk('public')->put($filePath, $pdf->output());
        }

        return [
            'file_path' => $filePath,
            'file_size' => Storage::disk('public')->size($filePath),
        ];
    }

    /**
     * Generate appropriate filename for export
     */
    private function generateFileName($fileType, $dateRange): string
    {
        $extension = $fileType === 'excel' ? 'xlsx' : 'pdf';
        $vehicle = $this->vehicle ? "vehicle_{$this->vehicle}" : 'all_vehicles';
        $period = $dateRange['start'];

        if ($dateRange['start'] !== $dateRange['end']) {
            $period = $dateRange['start'] . '_to_' . $dateRange['end'];
        }

        $uniqueNumber = mt_rand(100000, 999999);

        return "{$this->reporting_type}_{$vehicle}_{$period}_{$uniqueNumber}.{$extension}";
    }


    /**
     * Generate route reports for a date range
     */
    private function generateRouteReportsForPeriod($dateRange)
    {
        if ($dateRange['type'] === 'daily') {
            return RouteReportService::generateRouteReports($this->vehicle, $dateRange['start']);
        }

        // For multi-day periods, use the new MultiDayReportService
        return MultiDayReportService::generateMultiDayRouteReports(
            $this->vehicle,
            $dateRange['start'],
            $dateRange['end']
        );
    }

    /**
     * Generate fuel reports for a date range
     */
    private function generateFuelReportsForPeriod($dateRange)
    {
        if ($dateRange['type'] === 'daily') {
            return FuelReportService::generateFuelReports($this->vehicle, $dateRange['start']);
        }

        // For multi-day periods, use the new MultiDayReportService
        return MultiDayReportService::generateMultiDayFuelReports(
            $this->vehicle,
            $dateRange['start'],
            $dateRange['end']
        );
    }

    /**
     * Generate alarm reports for a date range
     */
    private function generateAlarmsReportsForPeriod($user, $dateRange)
    {
        return Alarm::query()
            ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                $query->whereIn('vehicle_id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
            })
            ->when($this->vehicle, function ($query) {
                $query->where('vehicle_id', $this->vehicle);
            })
            ->when($this->geofences, function ($query) {
                $query->whereIn('geofence_id', $this->geofences);
            })
            ->when($this->alarmType, function ($query) {
                $query->where('alarm_type', $this->alarmType);
            })
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end'] . ' 23:59:59'])
            ->latest()
            ->paginate(10);
    }

    /**
     * Aggregate route reports for multiple days
     */
    private function aggregateRouteReports($dateRange)
    {
        $allData = [];
        $currentDate = Carbon::parse($dateRange['start']);
        $endDate = Carbon::parse($dateRange['end']);

        while ($currentDate->lte($endDate)) {
            $dailyData = RouteReportService::generateRouteReports($this->vehicle, $currentDate->format('Y-m-d'));

            if (!empty($dailyData['details'])) {
                $allData[] = [
                    'date' => $currentDate->format('Y-m-d'),
                    'data' => $dailyData
                ];
            }

            $currentDate->addDay();
        }

        return $this->consolidateRouteData($allData, $dateRange);
    }

    /**
     * Aggregate fuel reports for multiple days
     */
    private function aggregateFuelReports($dateRange)
    {
        $allData = [];
        $currentDate = Carbon::parse($dateRange['start']);
        $endDate = Carbon::parse($dateRange['end']);

        while ($currentDate->lte($endDate)) {
            $dailyData = FuelReportService::generateFuelReports($this->vehicle, $currentDate->format('Y-m-d'));

            if (!empty($dailyData['details'])) {
                $allData[] = [
                    'date' => $currentDate->format('Y-m-d'),
                    'data' => $dailyData
                ];
            }

            $currentDate->addDay();
        }

        return $this->consolidateFuelData($allData, $dateRange);
    }


    /**
     * Consolidate route data from multiple days
     */
    private function consolidateRouteData($allData, $dateRange)
    {
        if (empty($allData)) {
            return ['summary' => [], 'details' => [], 'daily_summaries' => []];
        }

        $consolidatedDetails = [];
        $dailySummaries = [];
        $totalDistance = 0;
        $totalFuel = 0;
        $totalTrips = 0;
        $totalStops = 0;

        foreach ($allData as $dayData) {
            $date = $dayData['date'];
            $data = $dayData['data'];

            if (isset($data['summary'])) {
                $dailySummaries[] = array_merge($data['summary'], ['date' => $date]);

                $totalDistance += (float) str_replace(' km', '', $data['summary']['total_distance'] ?? '0');
                $totalFuel += (float) str_replace(' L', '', $data['summary']['total_fuel'] ?? '0');
                $totalTrips += (int) $data['summary']['total_trips'] ?? 0;
                $totalStops += (int) $data['summary']['total_stops'] ?? 0;
            }

            foreach ($data['details'] ?? [] as $detail) {
                $consolidatedDetails[] = array_merge($detail, ['date' => $date]);
            }
        }

        $firstDay = $allData[0]['data']['summary'] ?? [];
        $lastDay = end($allData)['data']['summary'] ?? [];

        $consolidatedSummary = [
            'vehicle' => $firstDay['vehicle'] ?? 'N/A',
            'period' => $dateRange['start'] . ' - ' . $dateRange['end'],
            'total_distance' => round($totalDistance, 2) . ' km',
            'total_fuel' => round($totalFuel, 2) . ' L',
            'total_trips' => $totalTrips,
            'total_stops' => $totalStops,
            'start_time' => $firstDay['start_time'] ?? 'N/A',
            'end_time' => $lastDay['end_time'] ?? 'N/A',
        ];

        return [
            'summary' => $consolidatedSummary,
            'details' => $consolidatedDetails,
            'daily_summaries' => $dailySummaries
        ];
    }

    /**
     * Consolidate fuel data from multiple days
     */
    private function consolidateFuelData($allData, $dateRange)
    {
        if (empty($allData)) {
            return ['summary' => [], 'details' => [], 'daily_summaries' => []];
        }

        $consolidatedDetails = [];
        $dailySummaries = [];
        $totalFuelUsed = 0;
        $totalDistance = 0;
        $totalTrips = 0;
        $totalStops = 0;

        foreach ($allData as $dayData) {
            $date = $dayData['date'];
            $data = $dayData['data'];

            if (isset($data['summary'])) {
                $dailySummaries[] = array_merge($data['summary'], ['date' => $date]);

                $totalFuelUsed += (float) $data['summary']['total_fuel_used'] ?? 0;
                $totalDistance += (float) $data['summary']['total_distance'] ?? 0;
                $totalTrips += (int) $data['summary']['total_trips'] ?? 0;
                $totalStops += (int) $data['summary']['total_stops'] ?? 0;
            }

            foreach ($data['details'] ?? [] as $detail) {
                $consolidatedDetails[] = array_merge($detail, ['date' => $date]);
            }
        }

        $firstDay = $allData[0]['data']['summary'] ?? [];
        $lastDay = end($allData)['data']['summary'] ?? [];

        $consolidatedSummary = [
            'vehicle' => $firstDay['vehicle'] ?? 'N/A',
            'period' => $dateRange['start'] . ' - ' . $dateRange['end'],
            'total_fuel_used' => round($totalFuelUsed, 2),
            'total_distance' => round($totalDistance, 2),
            'total_trips' => $totalTrips,
            'total_stops' => $totalStops,
            'start_time' => $firstDay['start_time'] ?? 'N/A',
            'end_time' => $lastDay['end_time'] ?? 'N/A',
        ];

        return [
            'summary' => $consolidatedSummary,
            'details' => $consolidatedDetails,
            'daily_summaries' => $dailySummaries
        ];
    }

    /**
     * Prepare export data for the given date range
     */
    private function prepareExportDataForRange($dateRange)
    {
        switch ($this->reporting_type) {
            case 'routes':
                return $this->generateRouteReportsForPeriod($dateRange);

            case 'fuel_consumption':
                return $this->generateFuelReportsForPeriod($dateRange);

            case 'alarms':
                $user = auth()->user();
                return Alarm::with('vehicle')
                    ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                        $query->whereIn('vehicle_id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
                    })
                    ->when($this->vehicle, fn($query) => $query->where('vehicle_id', $this->vehicle))
                    ->when($this->geofences, fn($query) => $query->whereIn('geofence_id', $this->geofences))
                    ->when($this->alarmType, fn($query) => $query->where('alarm_type', $this->alarmType))
                    ->whereBetween('created_at', [$dateRange['start'], $dateRange['end'] . ' 23:59:59'])
                    ->get()
                    ->map(fn($alarm) => [
                        'Vehicle' => $alarm->vehicle?->license_plate ?? 'N/A',
                        'Alarm Type' => __('translations.' . $alarm->alarm_type) ?? 'N/A',
                        'Triggered At' => $alarm->created_at->format('Y-m-d H:i:s'),
                        'Geofence' => $alarm->geofence?->name ?? 'N/A',
                        'Location' => $alarm->location ?? 'N/A',
                    ]);

            default:
                return [];
        }
    }

    /**
     * Delete an exported report
     */
    public function deleteExportedReport($reportId)
    {
        $report = ExportedReport::forUser(auth()->id())->findOrFail($reportId);

        // Delete the file from storage
        $report->deleteFile();

        // Delete the record
        $report->delete();

        $this->dispatch('notify', variant: 'success', title: 'Success!', message: 'Report deleted successfully.');
    }

    private function generateAlarmsReports($user)
    {
        return Alarm::query()
            ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                $query->whereIn('vehicle_id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
            })
            ->when($this->vehicle, function ($query) {
                $query->where('vehicle_id', $this->vehicle);
            })
            ->when($this->geofences, function ($query) {
                $query->whereIn('geofence_id', $this->geofences);
            })
            ->when($this->alarmType, function ($query) {
                $query->where('alarm_type', $this->alarmType);
            })
            ->when($this->date, function ($query) {
                $query->whereDate('created_at', $this->date);
            })
            ->latest()
            ->paginate(10);
    }
}
