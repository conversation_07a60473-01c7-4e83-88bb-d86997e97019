<?php

namespace App\Livewire\Panel;

use App\Models\RouteStop;
use App\Models\VehicleRouteAssignment;
use Illuminate\Support\Facades\Http;
use App\Services\RouteEncoder;
use Livewire\Component;
use Livewire\WithPagination;

class VehicleRoutes extends Component
{
    use WithPagination;

    public $vehicleId;
    public $recordId, $deleteRecordId, $search, $filter_status = null, $selectedRecord;
    public $start_point, $start_point_lat, $start_point_lng, $end_point, $end_point_lat, $end_point_lng, $start_date, $end_date, $is_repeat = false, $distance, $duration, $remarks, $status = 'pending', $departure_time;

    public $route = '';
    public $stops = [];

    public function mount($vehicleId)
    {
        $this->vehicleId = $vehicleId;
    }


    public function addDriver()
    {
        $this->resetExcept('vehicleId');

        $this->dispatch('remove-existing-routes');
        $this->dispatch('open-modal', name: 'manage-driver-route');
    }

    public function editRecord($recordId)
    {
        $this->recordId = $recordId;

        if ($recordId) {
            $this->recordId = $recordId;
            $driverRoute = VehicleRouteAssignment::find($this->recordId);

            if ($driverRoute) {
                // Set existing driverRoute data when editing
                $this->start_point = $driverRoute->start_point;
                $this->departure_time = $driverRoute->departure_time;
                $this->start_point_lat = $driverRoute->start_point_lat;
                $this->start_point_lng = $driverRoute->start_point_lng;
                $this->end_point = $driverRoute->end_point;
                $this->end_point_lat = $driverRoute->end_point_lat;
                $this->end_point_lng = $driverRoute->end_point_lng;
                $this->start_date = $driverRoute->start_date;
                $this->end_date = $driverRoute->end_date;
                $this->route = $driverRoute->route;
                $this->is_repeat = $driverRoute->is_repeat ? true : false;
                $this->status = $driverRoute->status;
                $this->distance = $driverRoute->distance;
                $this->duration = $driverRoute->duration;
                $this->remarks = $driverRoute->remarks;
                $this->stops = $driverRoute->stops?->toArray() ?? [];


                $this->dispatch('open-modal', name: 'manage-driver-route');
            }
        }
    }

    public function saveSelectedRoute($route)
    {
        // Convert the route path array to encoded polyline
        $encodedPath = RouteEncoder::encode($route['path']);

        // Store encoded path instead of full JSON
        $this->route = $encodedPath;
    }

    // Add or Update route
    public function addUpdateDriverRoute()
    {
        $this->validate([
            'start_point' => 'required|string|max:255',
            'departure_time' => 'nullable|string|max:255',
            'end_point' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'required|in:pending,completed,ongoing,cancelled',
            'remarks' => 'nullable|string|max:1000',

            'stops' => 'nullable|array',
            'stops.*.stop_point' => 'required|string|max:255',
        ]);


        $previousDistance = 0;
        $previousDuration = 0;

        if ($this->recordId) {
            // Fetch existing record
            $driverRoute = VehicleRouteAssignment::find($this->recordId);

            if ($driverRoute) {
                $previousDistance = $driverRoute->distance;
                $previousDuration = $driverRoute->duration;

                // Check if start and end points are unchanged
                if (
                    $driverRoute->start_point_lat == $this->start_point_lat &&
                    $driverRoute->start_point_lng == $this->start_point_lng &&
                    $driverRoute->end_point_lat == $this->end_point_lat &&
                    $driverRoute->end_point_lng == $this->end_point_lng
                ) {
                    $this->distance = $previousDistance;
                    $this->duration = $previousDuration;
                } else {
                    // Get distance and duration using Google API
                    $distanceAndDuration = $this->getDistanceAndDuration(
                        $this->start_point_lat,
                        $this->start_point_lng,
                        $this->end_point_lat,
                        $this->end_point_lng,
                        $this->stops
                    );

                    $this->distance = isset($distanceAndDuration['distance']) ? round($distanceAndDuration['distance'], 2) : 0;
                    $this->duration = isset($distanceAndDuration['duration']) ? round($distanceAndDuration['duration']) : 0;
                }

                // Update the existing record
                $driverRoute->update([
                    'start_point' => $this->start_point,
                    'start_point_lat' => $this->start_point_lat,
                    'start_point_lng' => $this->start_point_lng,
                    'end_point' => $this->end_point,
                    'end_point_lat' => $this->end_point_lat,
                    'end_point_lng' => $this->end_point_lng,
                    'start_date' => $this->start_date,
                    'end_date' => $this->end_date,
                    'departure_time' => $this->departure_time,
                    'route' => $this->route,
                    'is_repeat' => $this->is_repeat,
                    'status' => $this->status,
                    'distance' => $this->distance,
                    'duration' => $this->duration,
                    'remarks' => $this->remarks,
                ]);

                RouteStop::where('vehicle_route_assignment_id', $driverRoute->id)->delete();

                foreach ($this->stops as $stop) {
                    RouteStop::create([
                        'vehicle_route_assignment_id' => $driverRoute->id,
                        'stop_point' => $stop['stop_point'] ?? 'N/A',
                        'latitude' => $stop['latitude'],
                        'longitude' => $stop['longitude'],
                        'status' => 'pending'
                    ]);
                }
            }
        } else {
            // Get distance and duration using Google API
            $distanceAndDuration = $this->getDistanceAndDuration(
                $this->start_point_lat,
                $this->start_point_lng,
                $this->end_point_lat,
                $this->end_point_lng,
                $this->stops
            );

            $this->distance = isset($distanceAndDuration['distance']) ? round($distanceAndDuration['distance'], 2) : 0;
            $this->duration = isset($distanceAndDuration['duration']) ? round($distanceAndDuration['duration']) : 0;

            // Create a new record
            $driverRoute = VehicleRouteAssignment::create([
                'vehicle_id' => $this->vehicleId,
                'start_point' => $this->start_point,
                'start_point_lat' => $this->start_point_lat,
                'start_point_lng' => $this->start_point_lng,
                'end_point' => $this->end_point,
                'end_point_lat' => $this->end_point_lat,
                'end_point_lng' => $this->end_point_lng,
                'start_date' => $this->start_date,
                'end_date' => $this->end_date,
                'departure_time' => $this->departure_time,
                'route' => $this->route,
                'is_repeat' => $this->is_repeat,
                'status' => $this->status,
                'distance' => $this->distance,
                'duration' => $this->duration,
                'remarks' => $this->remarks,
            ]);

            foreach ($this->stops as $stop) {
                RouteStop::create([
                    'vehicle_route_assignment_id' => $driverRoute->id,
                    'stop_point' => $stop['stop_point'] ?? 'N/A',
                    'latitude' => $stop['latitude'],
                    'longitude' => $stop['longitude'],
                    'status' => 'pending'
                ]);
            }
        }

        $this->saveRouteToJson($driverRoute);


        $this->dispatch('notify', variant: 'success', title: 'Success!', message: $this->recordId ? 'Driver route updated successfully!' : 'Driver route created successfully!');
        $this->dispatch('close-modal');
        $this->resetExcept('vehicleId');
    }


    private function saveRouteToJson(VehicleRouteAssignment $route)
    {
        $vehicle = $route->vehicle;

        if (!$vehicle || !$vehicle->imei) {
            return; // Ensure the vehicle has an IMEI
        }

        $imei = $vehicle->imei;
        $date = $route->start_date; // Assuming start_date is in Y-m-d format

        $routeData = [
            'route_id' => $route->id,
            'imei' => $imei,
            'start_point' => [
                'latitude' => $route->start_point_lat,
                'longitude' => $route->start_point_lng,
                'status' => 'pending', // Initially pending
            ],
            'end_point' => [
                'latitude' => $route->end_point_lat,
                'longitude' => $route->end_point_lng,
                'status' => 'pending',
            ],
            'stops' => !empty($route->stops) ? $route->stops->map(function ($stop) {
                return [
                    'id' => $stop->id,
                    'latitude' => $stop->latitude,
                    'longitude' => $stop->longitude,
                    'status' => 'pending',
                ];
            })->toArray() : [],
            'status' => $route->status, // pending, ongoing, completed
        ];

        $filePath = public_path("routes/{$imei}/{$date}.json");

        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0777, true); // Create the folder if it doesn't exist
        }

        file_put_contents($filePath, json_encode($routeData, JSON_PRETTY_PRINT));
    }


    public function addStop()
    {
        $this->stops[] = [
            'latitude' => '',
            'longitude' => '',
            'stop_point' => '',
        ];
    }

    public function removeStop($index)
    {
        unset($this->stops[$index]);
        $this->stops = array_values($this->stops); // Reindex array
    }

    function getDistanceAndDuration($startLat, $startLng, $endLat, $endLng, $stops = [])
    {
        $apiKey = 'AIzaSyC8DHtH6KQlFbii460Aegpt25GER2Bhshk';

        // Base URL for Distance Matrix API
        $url = 'https://maps.googleapis.com/maps/api/distancematrix/json';

        // Build the origin and destination strings
        $origin = "$startLat,$startLng";
        $destination = "$endLat,$endLng";

        // Format stops (waypoints)
        $waypoints = '';
        if (!empty($stops)) {
            $waypoints = implode('|', array_map(fn($stop) => $stop['latitude'] . ',' . $stop['longitude'], $stops));
        }

        // Make the API request
        $response = Http::get($url, [
            'origins' => $origin,
            'destinations' => $destination,
            'waypoints' => $waypoints,
            'key' => $apiKey,
        ]);

        if ($response->successful()) {
            $data = $response->json();

            // Extract distance and duration
            $distance = $data['rows'][0]['elements'][0]['distance']['value'] ?? 0; // in meters
            $duration = $data['rows'][0]['elements'][0]['duration']['value'] ?? 0; // in seconds

            return [
                'distance' => $distance / 1000, // Convert to kilometers
                'duration' => $duration / 60,   // Convert to minutes
            ];
        }

        return [
            'distance' => 0,
            'duration' => 0,
        ];
    }



    public function deleteRecordConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-record-modal');
    }

    public function deleteRecord()
    {
        if ($this->deleteRecordId) {
            VehicleRouteAssignment::find($this->deleteRecordId)->delete();


            $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: 'Driver route deleted successfully');

            $this->dispatch('close-modal');
            $this->reset('deleteRecordId');
        } else {
            $this->dispatch('notify',  variant: 'danger', title: 'Error!',  message: 'Driver Not Found');
        }
    }

    public function showRecord($id)
    {
        $this->selectedRecord = VehicleRouteAssignment::find($id);

        // Decode path before sending to frontend
        $decodedPath = RouteEncoder::decode($this->selectedRecord->route);


        $this->dispatch('show-saved-route-view', [
            'start' => [
                'lat' => $this->selectedRecord->start_point_lat,
                'lng' => $this->selectedRecord->start_point_lng
            ],
            'end' => [
                'lat' => $this->selectedRecord->end_point_lat,
                'lng' => $this->selectedRecord->end_point_lng
            ],
            'stops' => $this->selectedRecord?->stops ?? [],
            'path' => $decodedPath
        ]);
        $this->dispatch('open-modal', name: 'show-record');
    }

    public function generateRoute()
    {
        if (empty($this->start_point_lat) || empty($this->start_point_lng)) {
            $this->dispatch('notify', variant: 'warning', title: 'Success!', message: 'Start point is empty! Please search and select a start point.');
            return;
        }

        if (empty($this->end_point_lat) || empty($this->end_point_lng)) {
            $this->dispatch('notify', variant: 'warning', title: 'Success!', message: 'End point is empty. Please search and select a end point.');
            return;
        }
        // Pass data to front-end
        $this->dispatch('load-route', [
            'start' => [
                'lat' => $this->start_point_lat,
                'lng' => $this->start_point_lng
            ],
            'end' => [
                'lat' => $this->end_point_lat,
                'lng' => $this->end_point_lng
            ],
            'stops' => $this->stops
        ]);
    }


    public function render()
    {
        $driverRoutes = VehicleRouteAssignment::where('vehicle_id', $this->vehicleId)
            ->when(isset($this->search), function ($query) {
                $query->where('start_point', 'like', '%' . $this->search . '%')
                    ->orWhere('end_point', 'like', '%' . $this->search . '%')
                    ->orWhere('remarks', 'like', '%' . $this->search . '%');
            })->when(isset($this->filter_status) && !empty($this->filter_status), function ($query) {
                $query->where('status', $this->filter_status);
            })->orderBy('start_date')->orderBy('departure_time')->orderByDesc('created_at')->paginate(10);

        return view('livewire.panel.vehicle-routes', compact('driverRoutes'));
    }
}
