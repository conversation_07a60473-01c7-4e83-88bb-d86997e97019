<?php

namespace App\Livewire\Components;

use App\Models\Notification;
use App\Models\Vehicle;
use Carbon\Carbon;
use Livewire\Component;

class Notifications extends Component
{
    public $notifications = [];

    public function mount()
    {
        // Fetch latest notifications
        $this->notifications = Notification::when(auth()->user()->role != 'admin', function ($query) {
            $vehicles = Vehicle::whereHas('vehicleUsers', function ($query) {
                $query->where('user_id', auth()->user()->id);
            })->pluck('id');
            $query->whereIn('vehicle_id', $vehicles)->orWhere('user_id', auth()->user()->id);
        })->latest()->take(10)->get()->map(function ($notification) {
            if ($notification->params) {
                $params = json_decode($notification->params ?? '', true);
            } else {
                $params = [];
            }
            return [
                'title' =>   __('translations.' . $notification->title, $params),
                'body' =>   __('translations.' . $notification->notification, $params),
                'created_at' => Carbon::parse($notification->created_at)->diffForHumans(),
            ];
        });
    }

    public function render()
    {
        return view('livewire.components.notifications');
    }
}
