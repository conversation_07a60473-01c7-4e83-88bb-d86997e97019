<?php

namespace App\Livewire\Components;

use Illuminate\Support\Facades\App;
use Livewire\Component;

class LangSwitcher extends Component
{
    public $languages = [
        'en' => ['flag' => 'uk.svg', 'name' => 'English'],
        'it' => ['flag' => 'italy.svg', 'name' => 'Italian'],
        'nl' => ['flag' => 'netherlands.svg', 'name' => 'Dutch'],
        'de' => ['flag' => 'germany.svg', 'name' => 'German'],
        'es' => ['flag' => 'spain.svg', 'name' => 'Spanish'],
        'fr' => ['flag' => 'france.svg', 'name' => 'French'],
        'pt' => ['flag' => 'portugal.svg', 'name' => 'Portuguese'],
        // 'zh' => ['flag' => 'china.svg', 'name' => 'Chinese'],
    ];

    public $currentUrl;
    public $currentLanguage = 'it';

    public function mount()
    {
        $this->currentUrl = url()->current();

        // Update the user's language preference
        if (auth()->check()) {
            $this->currentLanguage = auth()->user()->language ?? session('language', 'it');
        } elseif (session()->has('language')) {
            $this->currentLanguage = session('language', 'it');
        }
        App::setLocale($this->currentLanguage);
    }

    public function changeLanguage($shortCode)
    {
        // Update the user's language preference
        if (auth()->check()) {
            $user = auth()->user();
            $user->language = $shortCode; // Make sure you have a 'language' column in the users table
            $user->save();
            session()->put('language', $shortCode);
        } else {
            session()->put('language', $shortCode);
        }


        // Set the application locale
        App::setLocale($shortCode);

        $this->currentLanguage = $shortCode;

        // Emit an event to notify other components if needed
        $this->redirect($this->currentUrl ?? route('login'), navigate: true);
    }
    public function render()
    {
        return view('livewire.components.lang-switcher');
    }
}
