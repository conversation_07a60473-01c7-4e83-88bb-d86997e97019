<?php

namespace App\Services;

use App\Helpers\TripCalculationHelper;
use App\Models\Vehicle;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class OptimizedMultiDayReportService
{
    const CHUNK_SIZE = 3; // Process 3 days at a time
    const MEMORY_LIMIT_MB = 200; // Memory limit in MB
    const MAX_DETAILS_PER_CHUNK = 1000; // Limit details to prevent memory issues

    /**
     * Generate optimized route reports for multiple days
     */
    public static function generateOptimizedRouteReports($vehicleId, $startDate, $endDate, $callback = null)
    {
        $vehicle = Vehicle::find($vehicleId);
        if (!$vehicle) {
            return ['summary' => [], 'details' => [], 'daily_summaries' => []];
        }

        $consolidatedData = [
            'summary' => [],
            'details' => [],
            'daily_summaries' => []
        ];

        $totalMetrics = [
            'distance' => 0,
            'fuel' => 0,
            'trips' => 0,
            'stops' => 0,
            'trip_duration_minutes' => 0,
            'stop_duration_minutes' => 0,
            'first_start' => null,
            'last_end' => null,
        ];

        $currentDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);
        $processedDays = 0;
        $totalDays = $endDate->diffInDays($currentDate) + 1;

        while ($currentDate->lte($endDate)) {
            $chunkEndDate = $currentDate->copy()->addDays(self::CHUNK_SIZE - 1);
            if ($chunkEndDate->gt($endDate)) {
                $chunkEndDate = $endDate;
            }

            // Process chunk
            $chunkData = self::processDateChunk($vehicle->imei, $currentDate, $chunkEndDate, 'routes');
            
            // Merge chunk data
            self::mergeChunkData($consolidatedData, $chunkData, $totalMetrics);
            
            $processedDays += $chunkEndDate->diffInDays($currentDate) + 1;
            
            // Call progress callback if provided
            if ($callback) {
                $progress = ($processedDays / $totalDays) * 100;
                $callback($progress, "Processed {$processedDays}/{$totalDays} days");
            }

            // Memory cleanup
            unset($chunkData);
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }

            // Check memory usage
            $memoryUsage = memory_get_usage(true) / 1024 / 1024; // MB
            if ($memoryUsage > self::MEMORY_LIMIT_MB) {
                Log::warning("High memory usage detected: {$memoryUsage}MB");
            }

            $currentDate = $chunkEndDate->copy()->addDay();
        }

        // Finalize summary
        $consolidatedData['summary'] = self::createFinalSummary($totalMetrics, $vehicle, $startDate, $endDate);

        return $consolidatedData;
    }

    /**
     * Generate optimized fuel reports for multiple days
     */
    public static function generateOptimizedFuelReports($vehicleId, $startDate, $endDate, $callback = null)
    {
        $vehicle = Vehicle::find($vehicleId);
        if (!$vehicle) {
            return ['summary' => [], 'details' => [], 'daily_summaries' => []];
        }

        $consolidatedData = [
            'summary' => [],
            'details' => [],
            'daily_summaries' => []
        ];

        $totalMetrics = [
            'fuel_used' => 0,
            'distance' => 0,
            'trips' => 0,
            'stops' => 0,
            'trip_duration_minutes' => 0,
            'stop_duration_minutes' => 0,
            'first_start' => null,
            'last_end' => null,
        ];

        $currentDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);
        $processedDays = 0;
        $totalDays = $endDate->diffInDays($currentDate) + 1;

        while ($currentDate->lte($endDate)) {
            $chunkEndDate = $currentDate->copy()->addDays(self::CHUNK_SIZE - 1);
            if ($chunkEndDate->gt($endDate)) {
                $chunkEndDate = $endDate;
            }

            // Process chunk
            $chunkData = self::processDateChunk($vehicle->imei, $currentDate, $chunkEndDate, 'fuel');
            
            // Merge chunk data
            self::mergeChunkData($consolidatedData, $chunkData, $totalMetrics);
            
            $processedDays += $chunkEndDate->diffInDays($currentDate) + 1;
            
            // Call progress callback if provided
            if ($callback) {
                $progress = ($processedDays / $totalDays) * 100;
                $callback($progress, "Processed {$processedDays}/{$totalDays} days");
            }

            // Memory cleanup
            unset($chunkData);
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }

            $currentDate = $chunkEndDate->copy()->addDay();
        }

        // Finalize summary
        $consolidatedData['summary'] = self::createFinalFuelSummary($totalMetrics, $vehicle, $startDate, $endDate);

        return $consolidatedData;
    }

    /**
     * Process a chunk of dates (2-3 days)
     */
    private static function processDateChunk($imei, $startDate, $endDate, $reportType)
    {
        $chunkData = [
            'details' => [],
            'daily_summaries' => []
        ];

        $currentDate = $startDate->copy();
        
        while ($currentDate->lte($endDate)) {
            $dateStr = $currentDate->format('d-m-Y');
            $filePath = public_path("data/history/{$imei}/{$dateStr}.json");
            
            if (file_exists($filePath)) {
                try {
                    $historyData = json_decode(file_get_contents($filePath), true);
                    
                    if (!empty($historyData)) {
                        // Process the day's data
                        $calculationResult = TripCalculationHelper::calculateTripsAndStops($historyData, $dateStr);
                        
                        $dayData = self::processDayData($calculationResult, $currentDate, $reportType);
                        
                        // Add to chunk data
                        $chunkData['daily_summaries'][] = $dayData['daily_summary'];
                        $chunkData['details'] = array_merge($chunkData['details'], $dayData['details']);
                        
                        // Clear processed data from memory
                        unset($historyData, $calculationResult);
                    }
                } catch (\Exception $e) {
                    Log::warning("Failed to process data for {$imei} on {$dateStr}: " . $e->getMessage());
                }
            }
            
            $currentDate->addDay();
        }

        return $chunkData;
    }

    /**
     * Process individual day data
     */
    private static function processDayData($calculationResult, $date, $reportType)
    {
        $dateStr = $date->format('d-m-Y');
        $formattedDate = $date->format('Y-m-d');
        
        $stops = $calculationResult['stops'];
        $trips = $calculationResult['trips'];
        $sequence = $calculationResult['sequence'] ?? [];
        $firstValidData = $calculationResult['firstValidData'];
        $lastValidData = $calculationResult['lastValidData'];

        // Calculate daily metrics
        $dailyDistance = 0;
        $dailyFuel = 0;
        $dailyTripsCount = count($trips);
        $dailyStopsCount = count($stops);
        $dailyTripDuration = 0;
        $dailyStopDuration = 0;

        // Calculate metrics from trips and stops
        foreach ($trips as $trip) {
            $dailyDistance += $trip['distance'] ?? 0;
            $dailyFuel += $trip['fuel_consumption'] ?? 0;
            $dailyTripDuration += $trip['duration_minutes'] ?? 0;
        }

        foreach ($stops as $stop) {
            $dailyStopDuration += $stop['duration_minutes'] ?? 0;
        }

        // Create daily summary
        $dailySummary = [
            'date' => $formattedDate,
            'display_date' => $dateStr,
            'total_distance' => round($dailyDistance, 2) . ' km',
            'total_fuel' => round($dailyFuel, 2) . ' L',
            'total_fuel_used' => round($dailyFuel, 2), // For fuel reports
            'total_trips' => $dailyTripsCount,
            'total_stops' => $dailyStopsCount,
            'start_time' => $firstValidData ? parseFlexibleTimestamp($firstValidData['last_update'])->format('H:i') : 'N/A',
            'end_time' => $lastValidData ? parseFlexibleTimestamp($lastValidData['last_update'])->format('H:i') : 'N/A',
            'trip_duration_minutes' => $dailyTripDuration,
            'stop_duration_minutes' => $dailyStopDuration,
        ];

        // Format details for timeline
        $formattedDetails = [];
        foreach ($sequence as $detail) {
            $detailWithDate = $detail;
            $detailWithDate['date'] = $formattedDate;
            $detailWithDate['display_date'] = $dateStr;
            
            // Format the detail for display
            if ($detail['type'] === 'trip') {
                $detailWithDate['start_time_formatted'] = $detail['start_time']->format('H:i');
                $detailWithDate['end_time_formatted'] = $detail['end_time']->format('H:i');
                $detailWithDate['type'] = 'trip';
                $detailWithDate['location_text'] = isset($detail['start_location']['address']) ? 
                    $detail['start_location']['address'] : 
                    round($detail['start_location']['lat'], 6) . ', ' . round($detail['start_location']['lng'], 6);
                $detailWithDate['end_location_text'] = isset($detail['end_location']['address']) ? 
                    $detail['end_location']['address'] : 
                    round($detail['end_location']['lat'], 6) . ', ' . round($detail['end_location']['lng'], 6);
                $detailWithDate['distance_formatted'] = round($detail['distance'], 2) . ' km';
                $detailWithDate['fuel_formatted'] = round($detail['fuel_consumption'] ?? 0, 2) . ' L';
                
                // Keep original fields for backward compatibility
                $detailWithDate['start_location'] = $detailWithDate['location_text'];
                $detailWithDate['end_location'] = $detailWithDate['end_location_text'];
                $detailWithDate['trip_distance'] = $detailWithDate['distance_formatted'];
                $detailWithDate['trip_fuel'] = $detailWithDate['fuel_formatted'];
            } else {
                $detailWithDate['start_time_formatted'] = $detail['start_time']->format('H:i');
                $detailWithDate['end_time_formatted'] = $detail['end_time'] === 'Ongoing' ? 'Ongoing' : $detail['end_time']->format('H:i');
                $detailWithDate['type'] = 'stop';
                $detailWithDate['location_text'] = isset($detail['location']['address']) ? 
                    $detail['location']['address'] : 
                    round($detail['location']['lat'], 6) . ', ' . round($detail['location']['lng'], 6);
                $detailWithDate['distance_formatted'] = '0 km';
                $detailWithDate['fuel_formatted'] = '0 L';
                
                // Keep original fields for backward compatibility
                $detailWithDate['location'] = $detailWithDate['location_text'];
            }
            
            $formattedDetails[] = $detailWithDate;
        }

        return [
            'daily_summary' => $dailySummary,
            'details' => $formattedDetails,
            'metrics' => [
                'distance' => $dailyDistance,
                'fuel' => $dailyFuel,
                'trips' => $dailyTripsCount,
                'stops' => $dailyStopsCount,
                'trip_duration_minutes' => $dailyTripDuration,
                'stop_duration_minutes' => $dailyStopDuration,
                'first_start' => $firstValidData ? $firstValidData['last_update'] : null,
                'last_end' => $lastValidData ? $lastValidData['last_update'] : null,
            ]
        ];
    }

    /**
     * Merge chunk data into consolidated data
     */
    private static function mergeChunkData(&$consolidatedData, $chunkData, &$totalMetrics)
    {
        // Merge daily summaries
        $consolidatedData['daily_summaries'] = array_merge(
            $consolidatedData['daily_summaries'], 
            $chunkData['daily_summaries']
        );

        // Merge details
        $consolidatedData['details'] = array_merge(
            $consolidatedData['details'], 
            $chunkData['details']
        );

        // Update total metrics
        foreach ($chunkData['daily_summaries'] as $daily) {
            $totalMetrics['distance'] += floatval(str_replace(' km', '', $daily['total_distance']));
            $totalMetrics['fuel'] += floatval(str_replace(' L', '', $daily['total_fuel']));
            $totalMetrics['trips'] += $daily['total_trips'];
            $totalMetrics['stops'] += $daily['total_stops'];
            $totalMetrics['trip_duration_minutes'] += $daily['trip_duration_minutes'] ?? 0;
            $totalMetrics['stop_duration_minutes'] += $daily['stop_duration_minutes'] ?? 0;
        }
    }

    /**
     * Create final summary for route reports
     */
    private static function createFinalSummary($totalMetrics, $vehicle, $startDate, $endDate)
    {
        $totalTripHours = floor($totalMetrics['trip_duration_minutes'] / 60);
        $totalTripMinutes = $totalMetrics['trip_duration_minutes'] % 60;
        $totalStopHours = floor($totalMetrics['stop_duration_minutes'] / 60);
        $totalStopMinutes = $totalMetrics['stop_duration_minutes'] % 60;

        return [
            'vehicle' => $vehicle->license_plate,
            'period' => Carbon::parse($startDate)->format('d/m/Y') . ' - ' . Carbon::parse($endDate)->format('d/m/Y'),
            'total_distance' => round($totalMetrics['distance'], 2) . ' km',
            'total_fuel' => round($totalMetrics['fuel'], 2) . ' L',
            'total_trips' => $totalMetrics['trips'],
            'total_stops' => $totalMetrics['stops'],
            'start_time' => $totalMetrics['first_start'] ? parseFlexibleTimestamp($totalMetrics['first_start'])->format('d/m/Y H:i') : 'N/A',
            'end_time' => $totalMetrics['last_end'] ? parseFlexibleTimestamp($totalMetrics['last_end'])->format('d/m/Y H:i') : 'N/A',
            'total_days' => Carbon::parse($endDate)->diffInDays(Carbon::parse($startDate)) + 1,
            'total_trip_duration' => sprintf("%d hours %d minutes", $totalTripHours, $totalTripMinutes),
            'total_stop_duration' => sprintf("%d hours %d minutes", $totalStopHours, $totalStopMinutes),
        ];
    }

    /**
     * Create final summary for fuel reports
     */
    private static function createFinalFuelSummary($totalMetrics, $vehicle, $startDate, $endDate)
    {
        $totalDays = Carbon::parse($endDate)->diffInDays(Carbon::parse($startDate)) + 1;
        $totalTripHours = floor($totalMetrics['trip_duration_minutes'] / 60);
        $totalTripMinutes = $totalMetrics['trip_duration_minutes'] % 60;
        $totalStopHours = floor($totalMetrics['stop_duration_minutes'] / 60);
        $totalStopMinutes = $totalMetrics['stop_duration_minutes'] % 60;

        return [
            'vehicle' => $vehicle->license_plate,
            'period' => Carbon::parse($startDate)->format('d/m/Y') . ' - ' . Carbon::parse($endDate)->format('d/m/Y'),
            'total_fuel_used' => round($totalMetrics['fuel'], 2),
            'total_distance' => round($totalMetrics['distance'], 2),
            'total_trips' => $totalMetrics['trips'],
            'total_stops' => $totalMetrics['stops'],
            'start_time' => $totalMetrics['first_start'] ? parseFlexibleTimestamp($totalMetrics['first_start'])->format('d/m/Y H:i') : 'N/A',
            'end_time' => $totalMetrics['last_end'] ? parseFlexibleTimestamp($totalMetrics['last_end'])->format('d/m/Y H:i') : 'N/A',
            'total_days' => $totalDays,
            'average_daily_fuel' => $totalDays > 0 ? round($totalMetrics['fuel'] / $totalDays, 2) : 0,
            'fuel_efficiency' => $totalMetrics['distance'] > 0 ? round($totalMetrics['fuel'] / $totalMetrics['distance'] * 100, 2) : 0,
            'total_trip_duration' => sprintf("%d hours %d minutes", $totalTripHours, $totalTripMinutes),
            'total_stop_duration' => sprintf("%d hours %d minutes", $totalStopHours, $totalStopMinutes),
        ];
    }
}
