<?php

namespace App\Services;

use App\Models\Vehicle;
use App\Helpers\TripCalculationHelper;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class FuelReportService
{
    private const FUEL_REPORT_CACHE_KEY = 'fuel_report_';
    private const LIVE_REPORT_CACHE_DURATION = 300; // 5 minutes
    private const HISTORICAL_REPORT_CACHE_DURATION = 86400; // 24 hours


    public static function generateFuelReports($vehicle, $date)
    {
        $vehicle = Vehicle::where('id', $vehicle)->first();
        if (!$vehicle) return [];

        $cacheKey = self::FUEL_REPORT_CACHE_KEY . $vehicle->imei . '_' . ($date ?? now()->format('Y-m-d'));

        // For live data, use shorter cache duration
        $cacheDuration = $date ? self::HISTORICAL_REPORT_CACHE_DURATION : self::LIVE_REPORT_CACHE_DURATION;

        return cache()->remember($cacheKey, $cacheDuration, function () use ($vehicle, $date) {
            $result = [
                'summary' => null,
                'details' => []
            ];

            try {
                return self::generateHistoricalFuelReport($vehicle, $date);
            } catch (\Exception $e) {
                Log::error("Error generating fuel reports: " . $e->getMessage());
                return $result;
            }
        });
    }

    private static function generateHistoricalFuelReport($vehicle, $date)
    {
        $result = [
            'summary' => null,
            'details' => []
        ];

        $date = ($date ? Carbon::parse($date)->format('d-m-Y') : null) ?? now()->format('d-m-Y');
        $filePath = public_path("data/history/{$vehicle->imei}/{$date}.json");

        if (!file_exists($filePath)) return $result;

        $historyData = json_decode(@file_get_contents($filePath), true);
        if (empty($historyData)) return $result;

        // Use the TripCalculationHelper to get consistent trip and stop data
        $calculationResult = TripCalculationHelper::calculateTripsAndStops($historyData, $date);
        $stops = $calculationResult['stops'];
        $trips = $calculationResult['trips'];
        $firstValidData = $calculationResult['firstValidData'];
        $lastValidData = $calculationResult['lastValidData'];
        $tripStartOdometer = $calculationResult['tripStartOdometer'];
        $tripStartFuel = $calculationResult['tripStartFuel'];

        // Calculate overall trip metrics
        if (!$firstValidData || !$lastValidData) {
            return $result;
        }

        $startTime = parseFlexibleTimestamp($firstValidData['last_update']);
        $endTime = parseFlexibleTimestamp($lastValidData['last_update']);

        // Calculate total distance using GPS coordinates
        $totalDistance = self::calculateTotalGpsDistance($historyData);

        // If GPS distance is too small, fall back to odometer
        if ($totalDistance < 1) {
            $totalDistance = max(0, (getOdometerValue($lastValidData) - getOdometerValue($firstValidData))) / 1000;
        }

        // Distribute the total distance among stops proportionally to their duration
        if (!empty($stops) && $totalDistance > 0) {
            self::distributeDistanceToStops($stops, $totalDistance);
        }

        // Calculate fuel consumption for the entire trip
        // We need to analyze the entire history data to detect refueling events
        $fuelEvents = self::analyzeFuelEvents($historyData);

        // Get initial and final fuel levels
        $initialFuelLevel = getFuelConsumption($firstValidData);
        $finalFuelLevel = getFuelConsumption($lastValidData);

        // Calculate total fuel consumption and refueling
        $totalFuelUsed = $fuelEvents['consumption'];
        $refuelingAmount = $fuelEvents['refueling'];

        // If the final fuel level is higher than the initial fuel level,
        // it means there was refueling during the trip
        if ($finalFuelLevel > $initialFuelLevel) {
            // The refueling amount is at least the difference
            $additionalRefueling = $finalFuelLevel - $initialFuelLevel;
            $refuelingAmount = max($refuelingAmount, $additionalRefueling);

            // The consumption should be at least the refueling amount
            // (since we ended up with more fuel than we started with)
            $totalFuelUsed = max($totalFuelUsed, $refuelingAmount);
        } else {
            // Normal case: fuel was consumed
            $directConsumption = $initialFuelLevel - $finalFuelLevel;

            // If direct consumption calculation gives a higher value, use it
            $totalFuelUsed = max($totalFuelUsed, $directConsumption);
        }

        // If we still couldn't detect any meaningful fuel events, fall back to route report calculation
        if ($totalFuelUsed < 0.1) {
            // Try to get the fuel consumption from the main trip data
            $mainTripFuel = 0;
            foreach ($stops as $stop) {
                $mainTripFuel += isset($stop['trip_fuel']) ? (float)$stop['trip_fuel'] : 0;
            }

            // If we have meaningful trip fuel data, use it
            if ($mainTripFuel > 0.1) {
                $totalFuelUsed = $mainTripFuel;
            } else {
                // Last resort: Calculate expected fuel consumption based on distance and average consumption
                $vehicleType = $vehicle->type ?? 'car';
                $avgConsumption = self::getMileageForVehicle($vehicleType);
                $totalFuelUsed = $avgConsumption > 0 ? ($totalDistance / $avgConsumption) : 0;
            }
        }

        // Ensure we have reasonable values
        $totalFuelUsed = max($totalFuelUsed, 0.1);
        $refuelingAmount = max($refuelingAmount, 0);

        // Create a main trip structure similar to RouteReportService
        $mainTrip = [
            'start_time' => $startTime,
            'end_time' => $endTime,
            'start_location' => [
                'lat' => $firstValidData['latitude'],
                'lng' => $firstValidData['longitude'],
                'address' => getAddressFromCoordinates($firstValidData['latitude'], $firstValidData['longitude'])
            ],
            'end_location' => [
                'lat' => $lastValidData['latitude'],
                'lng' => $lastValidData['longitude'],
                'address' => getAddressFromCoordinates($lastValidData['latitude'], $lastValidData['longitude'])
            ],
            'start_odometer' => getOdometerValue($firstValidData),
            'end_odometer' => getOdometerValue($lastValidData),
            'start_fuel' => getFuelConsumption($firstValidData),
            'end_fuel' => getFuelConsumption($lastValidData),
            'stops' => $stops,
            'trips' => $trips,
            'duration' => $startTime->diffForHumans($endTime, true),
            'distance' => $totalDistance,
            'fuel_consumption' => $totalFuelUsed
        ];

        // Format the fuel report using the mainTrip structure
        $fuelReport = self::formatFuelReports([$mainTrip], $vehicle, $date, $totalFuelUsed, $refuelingAmount);

        return $fuelReport;
    }



    private static function formatFuelReports($trips, $vehicle = null, $date = null, $totalFuelUsed = 0, $refuelingAmount = 0)
    {
        if (empty($trips)) {
            return ['summary' => null, 'details' => []];
        }

        // Calculate overall summary
        $summary = [
            'vehicle' => $vehicle->license_plate,
            'date' => $date,
            'total_fuel_used' => number_format($totalFuelUsed, 2),
            'total_distance' => number_format(array_sum(array_column($trips, 'distance')), 2),
            'start_time' => $trips[0]['start_time']->format('H:i'),
            'end_time' => end($trips)['end_time']->format('H:i'),
            'total_duration' => $trips[0]['start_time']->diffForHumans(end($trips)['end_time'], true),
            'total_trips' => array_sum(array_map(fn($trip) => count($trip['trips']), $trips)), // Number of actual trips
            'total_stops' => array_sum(array_map(fn($trip) => count($trip['stops']), $trips)), // Number of stops
            'total_stop_duration' => self::calculateTotalStopDuration(array_merge(...array_column($trips, 'stops'))),
            'total_trip_duration' => self::calculateTotalTripDuration(array_merge(...array_column($trips, 'trips'))),
            'fuel_efficiency' => $trips[0]['distance'] > 0 && $totalFuelUsed > 0 ?
                number_format(($totalFuelUsed / $trips[0]['distance']) * 100, 2) . ' L/100km' : '0.00 L/100km',
            'initial_fuel_level' => number_format($trips[0]['start_fuel'], 2),
            'final_fuel_level' => number_format(end($trips)['end_fuel'], 2),
            'start_point_address' => $trips[0]['start_location']['address'],
            'end_point_address' => end($trips)['end_location']['address'],
            'refueling_amount' => number_format($refuelingAmount, 2)
        ];

        // Format detailed records
        $details = [];

        // First add trip details if available
        foreach ($trips as $trip) {
            foreach ($trip['trips'] as $tripItem) {
                // Handle end_time calculation
                if ($tripItem['end_time'] === 'Ongoing') {
                    // For historical data, show end of day time instead of "Ongoing"
                    $tripStartTime = $tripItem['start_time'] instanceof Carbon
                        ? $tripItem['start_time']
                        : Carbon::parse($tripItem['start_time']);

                    $isHistorical = !$tripStartTime->copy()->startOfDay()->isToday();
                    if ($isHistorical) {
                        $endTime = $tripStartTime->copy()->startOfDay()->endOfDay()->format('H:i');
                    } else {
                        $endTime = 'Ongoing';
                    }
                } else {
                    $endTime = $tripItem['end_time'] instanceof Carbon
                        ? $tripItem['end_time']->format('H:i')
                        : $tripItem['end_time'];
                }

                $startTime = $tripItem['start_time'] instanceof Carbon
                    ? $tripItem['start_time']->format('H:i')
                    : $tripItem['start_time'];

                // Format the trip data
                $tripData = [
                    'type' => 'trip',
                    'start_time' => $startTime,
                    'end_time' => $endTime,
                    'duration' => $tripItem['duration'],
                    'start_location' => $tripItem['start_location']['address'],
                    'end_location' => $tripItem['end_location']['address'],
                    'trip_distance' => number_format($tripItem['distance'], 2) . ' km',
                    'trip_fuel' => number_format($tripItem['fuel_consumption'], 2) . ' L'
                ];

                $details[] = $tripData;
            }
        }

        // Then add stop details
        foreach ($trips as $trip) {
            foreach ($trip['stops'] as $stop) {
                // Handle end_time calculation
                if ($stop['end_time'] === 'Ongoing') {
                    // For historical data, show end of day time instead of "Ongoing"
                    $stopStartTime = $stop['start_time'] instanceof Carbon
                        ? $stop['start_time']
                        : Carbon::parse($stop['time']);

                    $isHistorical = !$stopStartTime->copy()->startOfDay()->isToday();
                    if ($isHistorical) {
                        $endTime = $stopStartTime->copy()->startOfDay()->endOfDay()->format('H:i');
                    } else {
                        $endTime = 'Ongoing';
                    }
                } else {
                    $endTime = $stop['end_time'] instanceof Carbon
                        ? $stop['end_time']->format('H:i')
                        : $stop['end_time'];
                }

                // Format the stop data
                $stopData = [
                    'type' => 'stop',
                    'start_time' => $stop['time'],
                    'end_time' => $endTime,
                    'duration' => $stop['duration'],
                    'location' => $stop['location']['address'],
                    'odometer' => isset($stop['odometer']) ? number_format($stop['odometer'] / 1000, 2) . ' km' : '-',
                    'fuel' => isset($stop['fuel']) ? number_format($stop['fuel'], 2) . ' L' : '-',
                    'trip_distance' => number_format($stop['trip_distance'], 2) . ' km',
                    'trip_fuel' => number_format($stop['trip_fuel'], 2) . ' L'
                ];

                $details[] = $stopData;
            }
        }

        // Sort details by start_time
        usort($details, function ($a, $b) {
            $timeA = Carbon::createFromFormat('H:i', $a['start_time']);
            $timeB = Carbon::createFromFormat('H:i', $b['start_time']);
            return $timeA <=> $timeB;
        });

        return [
            'summary' => $summary,
            'details' => $details
        ];
    }

    /**
     * Analyze fuel events to detect consumption and refueling
     *
     * @param array $historyData The complete history data for the day
     * @return array Containing 'consumption' and 'refueling' values
     */
    private static function analyzeFuelEvents($historyData)
    {
        // Sort data by timestamp to ensure chronological order
        usort($historyData, function ($a, $b) {
            $timeA = parseFlexibleTimestamp($a['last_update'] ?? '01/01/2000 00:00:00');
            $timeB = parseFlexibleTimestamp($b['last_update'] ?? '01/01/2000 00:00:00');
            return $timeA <=> $timeB;
        });

        $totalConsumption = 0;
        $totalRefueling = 0;
        $lastFuelLevel = null;

        foreach ($historyData as $data) {
            $currentFuelLevel = getFuelConsumption($data);

            if ($lastFuelLevel !== null && $currentFuelLevel != $lastFuelLevel) {
                $fuelDifference = $currentFuelLevel - $lastFuelLevel;

                // If fuel level increased, it's a refueling event
                if ($fuelDifference > 0) {
                    // Only count significant refueling (more than 1 liter)
                    if ($fuelDifference > 1) {
                        $totalRefueling += $fuelDifference;
                    }
                } else {
                    // Fuel level decreased, it's consumption
                    // Only count reasonable consumption (less than 20 liters at once)
                    if (abs($fuelDifference) < 20) {
                        $totalConsumption += abs($fuelDifference);
                    }
                }
            }

            $lastFuelLevel = $currentFuelLevel;
        }

        // If we have refueling events but no consumption detected,
        // estimate consumption based on the total refueling amount
        if ($totalRefueling > 0 && $totalConsumption < 1) {
            $totalConsumption = $totalRefueling;
        }

        return [
            'consumption' => $totalConsumption,
            'refueling' => $totalRefueling
        ];
    }

    // Get Mileage based on vehicle type
    private static function getMileageForVehicle($vehicleType)
    {
        return match ($vehicleType) {
            'truck', 'bus', 'tractor' => 3,
            'car', 'bike', 'scooter', 'van', 'ambulance' => 11,
            default => 0,
        };
    }

    // Distribute total distance among stops proportionally to their duration
    private static function distributeDistanceToStops(&$stops, $totalDistance)
    {
        // Calculate total duration of all stops
        $totalDuration = 0;
        foreach ($stops as $stop) {
            if ($stop['end_time'] === 'Ongoing') {
                $endTime = now();
            } else {
                $endTime = $stop['end_time'];
            }

            $startTime = $stop['start_time'];
            $durationMinutes = $startTime->diffInMinutes($endTime);
            $totalDuration += $durationMinutes;
        }

        if ($totalDuration == 0) {
            return;
        }

        // Distribute distance proportionally
        $remainingDistance = $totalDistance;
        $lastStopDistance = 0;

        for ($i = 0; $i < count($stops); $i++) {
            if ($stops[$i]['end_time'] === 'Ongoing') {
                $endTime = now();
            } else {
                $endTime = $stops[$i]['end_time'];
            }

            $startTime = $stops[$i]['start_time'];
            $durationMinutes = $startTime->diffInMinutes($endTime);

            // Calculate proportion of total duration
            $proportion = $durationMinutes / $totalDuration;

            // Allocate distance based on proportion
            if ($i == count($stops) - 1) {
                // Last stop gets remaining distance to avoid rounding errors
                $stopDistance = $remainingDistance;
            } else {
                $stopDistance = $totalDistance * $proportion;
                $remainingDistance -= $stopDistance;
            }

            // Update stop with new distance
            $stops[$i]['trip_distance'] = $lastStopDistance + $stopDistance;
            $lastStopDistance = $stops[$i]['trip_distance'];
        }
    }



    // Calculate total distance using GPS coordinates
    private static function calculateTotalGpsDistance($historyData)
    {
        $totalDistance = 0;
        $lastLat = null;
        $lastLon = null;

        foreach ($historyData as $data) {
            if (!isset($data['latitude']) || !isset($data['longitude'])) {
                continue;
            }

            $currentLat = (float)$data['latitude'];
            $currentLon = (float)$data['longitude'];

            if ($lastLat !== null && $lastLon !== null) {
                // Calculate distance between consecutive points
                $segmentDistance = self::calculateGpsDistance($lastLat, $lastLon, $currentLat, $currentLon);

                // Only add if the distance is reasonable (not a GPS jump)
                if ($segmentDistance < 5) { // Less than 5km between consecutive points
                    $totalDistance += $segmentDistance;
                }
            }

            $lastLat = $currentLat;
            $lastLon = $currentLon;
        }

        return $totalDistance;
    }




    // Calculate distance between two GPS coordinates using Haversine formula
    private static function calculateGpsDistance($lat1, $lon1, $lat2, $lon2)
    {
        if (!$lat1 || !$lon1 || !$lat2 || !$lon2) {
            return 0;
        }

        // Convert latitude and longitude from degrees to radians
        $lat1 = deg2rad((float)$lat1);
        $lon1 = deg2rad((float)$lon1);
        $lat2 = deg2rad((float)$lat2);
        $lon2 = deg2rad((float)$lon2);

        // Haversine formula
        $dlat = $lat2 - $lat1;
        $dlon = $lon2 - $lon1;
        $a = sin($dlat / 2) * sin($dlat / 2) + cos($lat1) * cos($lat2) * sin($dlon / 2) * sin($dlon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $distance = 6371 * $c; // Earth radius in km

        return $distance;
    }




    // Calculate total stop duration
    private static function calculateTotalStopDuration($stops)
    {
        $totalMinutes = 0;

        foreach ($stops as $stop) {
            try {
                // If we have a duration_minutes field, use it directly
                if (isset($stop['duration_minutes'])) {
                    $totalMinutes += $stop['duration_minutes'];
                    continue;
                }

                // For ongoing stops
                if (isset($stop['end_time']) && $stop['end_time'] === 'Ongoing') {
                    $startTime = isset($stop['start_time']) ? $stop['start_time'] : Carbon::parse($stop['time']);
                    if (!($startTime instanceof Carbon)) {
                        $startTime = Carbon::parse($startTime);
                    }

                    // Determine if we're dealing with historical data
                    $isHistorical = !$startTime->copy()->startOfDay()->isToday();
                    $endTime = $isHistorical ? $startTime->copy()->startOfDay()->endOfDay() : now();

                    $totalMinutes += $startTime->diffInMinutes($endTime);
                    continue;
                }

                // For regular stops
                $startTime = isset($stop['start_time']) ? $stop['start_time'] : Carbon::parse($stop['time']);
                if (!($startTime instanceof Carbon)) {
                    $startTime = Carbon::parse($startTime);
                }

                $endTime = $stop['end_time'] instanceof Carbon ? $stop['end_time'] : Carbon::parse($stop['end_time']);

                // If end time is before start time, it means the stop went into the next day
                if ($endTime->lt($startTime)) {
                    $endTime->addDay();
                }

                $duration = $startTime->diffInMinutes($endTime);
                $totalMinutes += $duration;
            } catch (\Exception $e) {
                Log::warning("Stop duration calculation error: " . $e->getMessage(), [
                    'stop' => $stop,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        // Convert total minutes to hours and minutes
        $hours = floor(max(0, abs($totalMinutes)) / 60);
        $minutes = max(0, abs($totalMinutes)) % 60;

        return sprintf("%d hours %d minutes", $hours, $minutes);
    }

    // Calculate total trip duration
    private static function calculateTotalTripDuration($trips)
    {
        $totalMinutes = 0;

        foreach ($trips as $trip) {
            try {
                // If we have a duration_minutes field, use it directly
                if (isset($trip['duration_minutes'])) {
                    $totalMinutes += $trip['duration_minutes'];
                    continue;
                }

                // For ongoing trips
                if (isset($trip['end_time']) && $trip['end_time'] === 'Ongoing') {
                    $startTime = $trip['start_time'] instanceof Carbon ? $trip['start_time'] : Carbon::parse($trip['start_time']);

                    // Determine if we're dealing with historical data
                    $isHistorical = !$startTime->copy()->startOfDay()->isToday();
                    $endTime = $isHistorical ? $startTime->copy()->startOfDay()->endOfDay() : now();

                    $totalMinutes += $startTime->diffInMinutes($endTime);
                    continue;
                }

                // For regular trips
                $startTime = $trip['start_time'] instanceof Carbon ? $trip['start_time'] : Carbon::parse($trip['start_time']);
                $endTime = $trip['end_time'] instanceof Carbon ? $trip['end_time'] : Carbon::parse($trip['end_time']);

                // If end time is before start time, it means the trip went into the next day
                if ($endTime->lt($startTime)) {
                    $endTime->addDay();
                }

                $totalMinutes += $startTime->diffInMinutes($endTime);
            } catch (\Exception $e) {
                Log::warning("Trip duration calculation error: " . $e->getMessage(), [
                    'trip' => $trip,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        // Convert total minutes to hours and minutes
        $hours = floor(max(0, abs($totalMinutes)) / 60);
        $minutes = max(0, abs($totalMinutes)) % 60;

        return sprintf("%d hours %d minutes", $hours, $minutes);
    }
}
