<?php

namespace App\Services;

use App\Helpers\TripCalculationHelper;
use App\Models\Vehicle;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class MultiDayReportService
{
    /**
     * Generate route reports for multiple days from JSON dumps
     */
    public static function generateMultiDayRouteReports($vehicleId, $startDate, $endDate)
    {
        $vehicle = Vehicle::find($vehicleId);
        if (!$vehicle) {
            return ['summary' => [], 'details' => [], 'daily_summaries' => []];
        }

        $allDailyData = self::loadMultiDayData($vehicle->imei, $startDate, $endDate);
        
        if (empty($allDailyData)) {
            return ['summary' => [], 'details' => [], 'daily_summaries' => []];
        }

        return self::consolidateRouteData($allDailyData, $vehicle, $startDate, $endDate);
    }

    /**
     * Generate fuel reports for multiple days from JSON dumps
     */
    public static function generateMultiDayFuelReports($vehicleId, $startDate, $endDate)
    {
        $vehicle = Vehicle::find($vehicleId);
        if (!$vehicle) {
            return ['summary' => [], 'details' => [], 'daily_summaries' => []];
        }

        $allDailyData = self::loadMultiDayData($vehicle->imei, $startDate, $endDate);
        
        if (empty($allDailyData)) {
            return ['summary' => [], 'details' => [], 'daily_summaries' => []];
        }

        return self::consolidateFuelData($allDailyData, $vehicle, $startDate, $endDate);
    }

    /**
     * Load data for multiple days from JSON files
     */
    private static function loadMultiDayData($imei, $startDate, $endDate)
    {
        $allDailyData = [];
        $currentDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        while ($currentDate->lte($endDate)) {
            $dateStr = $currentDate->format('d-m-Y');
            $filePath = public_path("data/history/{$imei}/{$dateStr}.json");
            
            if (file_exists($filePath)) {
                try {
                    $historyData = json_decode(file_get_contents($filePath), true);
                    
                    if (!empty($historyData)) {
                        // Process the day's data using TripCalculationHelper
                        $calculationResult = TripCalculationHelper::calculateTripsAndStops($historyData, $dateStr);
                        
                        $allDailyData[] = [
                            'date' => $dateStr,
                            'formatted_date' => $currentDate->format('Y-m-d'),
                            'raw_data' => $historyData,
                            'calculation_result' => $calculationResult,
                            'stops' => $calculationResult['stops'],
                            'trips' => $calculationResult['trips'],
                            'sequence' => $calculationResult['sequence'] ?? [],
                            'first_valid_data' => $calculationResult['firstValidData'],
                            'last_valid_data' => $calculationResult['lastValidData'],
                        ];
                    }
                } catch (\Exception $e) {
                    Log::warning("Failed to load data for {$imei} on {$dateStr}: " . $e->getMessage());
                }
            }
            
            $currentDate->addDay();
        }

        return $allDailyData;
    }

    /**
     * Consolidate route data from multiple days
     */
    private static function consolidateRouteData($allDailyData, $vehicle, $startDate, $endDate)
    {
        $consolidatedDetails = [];
        $dailySummaries = [];
        $totalDistance = 0;
        $totalFuel = 0;
        $totalTrips = 0;
        $totalStops = 0;
        $firstDayStart = null;
        $lastDayEnd = null;

        foreach ($allDailyData as $dayData) {
            $date = $dayData['date'];
            $formattedDate = $dayData['formatted_date'];
            $stops = $dayData['stops'];
            $trips = $dayData['trips'];
            $sequence = $dayData['sequence'];
            $firstValidData = $dayData['first_valid_data'];
            $lastValidData = $dayData['last_valid_data'];

            // Calculate daily metrics
            $dailyDistance = 0;
            $dailyFuel = 0;
            $dailyTripsCount = count($trips);
            $dailyStopsCount = count($stops);

            // Calculate daily distance and fuel from trips
            foreach ($trips as $trip) {
                $dailyDistance += $trip['distance'] ?? 0;
                $dailyFuel += $trip['fuel_consumption'] ?? 0;
            }

            // Add to totals
            $totalDistance += $dailyDistance;
            $totalFuel += $dailyFuel;
            $totalTrips += $dailyTripsCount;
            $totalStops += $dailyStopsCount;

            // Track first and last times
            if ($firstValidData && (!$firstDayStart || $firstValidData['last_update'] < $firstDayStart)) {
                $firstDayStart = $firstValidData['last_update'];
            }
            if ($lastValidData && (!$lastDayEnd || $lastValidData['last_update'] > $lastDayEnd)) {
                $lastDayEnd = $lastValidData['last_update'];
            }

            // Create daily summary
            $dailySummary = [
                'date' => $formattedDate,
                'display_date' => $date,
                'vehicle' => $vehicle->license_plate,
                'total_distance' => round($dailyDistance, 2) . ' km',
                'total_fuel' => round($dailyFuel, 2) . ' L',
                'total_trips' => $dailyTripsCount,
                'total_stops' => $dailyStopsCount,
                'start_time' => $firstValidData ? parseFlexibleTimestamp($firstValidData['last_update'])->format('H:i') : 'N/A',
                'end_time' => $lastValidData ? parseFlexibleTimestamp($lastValidData['last_update'])->format('H:i') : 'N/A',
            ];
            $dailySummaries[] = $dailySummary;

            // Add details with date information
            foreach ($sequence as $detail) {
                $detailWithDate = $detail;
                $detailWithDate['date'] = $formattedDate;
                $detailWithDate['display_date'] = $date;
                
                // Format the detail for display
                if ($detail['type'] === 'trip') {
                    $detailWithDate['start_time_formatted'] = $detail['start_time']->format('H:i');
                    $detailWithDate['end_time_formatted'] = $detail['end_time']->format('H:i');
                    $detailWithDate['type'] = 'trip';
                    $detailWithDate['location_text'] = isset($detail['start_location']['address']) ?
                        $detail['start_location']['address'] :
                        round($detail['start_location']['lat'], 6) . ', ' . round($detail['start_location']['lng'], 6);
                    $detailWithDate['end_location_text'] = isset($detail['end_location']['address']) ?
                        $detail['end_location']['address'] :
                        round($detail['end_location']['lat'], 6) . ', ' . round($detail['end_location']['lng'], 6);
                    $detailWithDate['distance_formatted'] = round($detail['distance'], 2) . ' km';
                    $detailWithDate['fuel_formatted'] = round($detail['fuel_consumption'] ?? 0, 2) . ' L';

                    // Keep original fields for backward compatibility
                    $detailWithDate['start_location'] = $detailWithDate['location_text'];
                    $detailWithDate['end_location'] = $detailWithDate['end_location_text'];
                    $detailWithDate['trip_distance'] = $detailWithDate['distance_formatted'];
                    $detailWithDate['trip_fuel'] = $detailWithDate['fuel_formatted'];
                } else {
                    $detailWithDate['start_time_formatted'] = $detail['start_time']->format('H:i');
                    $detailWithDate['end_time_formatted'] = $detail['end_time'] === 'Ongoing' ? 'Ongoing' : $detail['end_time']->format('H:i');
                    $detailWithDate['type'] = 'stop';
                    $detailWithDate['location_text'] = isset($detail['location']['address']) ?
                        $detail['location']['address'] :
                        round($detail['location']['lat'], 6) . ', ' . round($detail['location']['lng'], 6);
                    $detailWithDate['distance_formatted'] = '0 km';
                    $detailWithDate['fuel_formatted'] = '0 L';

                    // Keep original fields for backward compatibility
                    $detailWithDate['location'] = $detailWithDate['location_text'];
                }
                
                $consolidatedDetails[] = $detailWithDate;
            }
        }

        // Calculate total durations
        $allStops = [];
        $allTrips = [];
        foreach ($allDailyData as $dayData) {
            $allStops = array_merge($allStops, $dayData['stops']);
            $allTrips = array_merge($allTrips, $dayData['trips']);
        }

        $totalTripDuration = self::calculateTotalTripDuration($allTrips);
        $totalStopDuration = self::calculateTotalStopDuration($allStops);

        // Create consolidated summary
        $consolidatedSummary = [
            'vehicle' => $vehicle->license_plate,
            'period' => Carbon::parse($startDate)->format('d/m/Y') . ' - ' . Carbon::parse($endDate)->format('d/m/Y'),
            'total_distance' => round($totalDistance, 2) . ' km',
            'total_fuel' => round($totalFuel, 2) . ' L',
            'total_trips' => $totalTrips,
            'total_stops' => $totalStops,
            'start_time' => $firstDayStart ? parseFlexibleTimestamp($firstDayStart)->format('d/m/Y H:i') : 'N/A',
            'end_time' => $lastDayEnd ? parseFlexibleTimestamp($lastDayEnd)->format('d/m/Y H:i') : 'N/A',
            'total_days' => count($allDailyData),
            'total_trip_duration' => $totalTripDuration,
            'total_stop_duration' => $totalStopDuration,
        ];

        return [
            'summary' => $consolidatedSummary,
            'details' => $consolidatedDetails,
            'daily_summaries' => $dailySummaries
        ];
    }

    /**
     * Consolidate fuel data from multiple days
     */
    private static function consolidateFuelData($allDailyData, $vehicle, $startDate, $endDate)
    {
        $consolidatedDetails = [];
        $dailySummaries = [];
        $totalFuelUsed = 0;
        $totalDistance = 0;
        $totalTrips = 0;
        $totalStops = 0;
        $firstDayStart = null;
        $lastDayEnd = null;

        foreach ($allDailyData as $dayData) {
            $date = $dayData['date'];
            $formattedDate = $dayData['formatted_date'];
            $stops = $dayData['stops'];
            $trips = $dayData['trips'];
            $sequence = $dayData['sequence'];
            $firstValidData = $dayData['first_valid_data'];
            $lastValidData = $dayData['last_valid_data'];
            $rawData = $dayData['raw_data'];

            // Calculate daily fuel consumption from raw data
            $dailyFuelUsed = self::calculateDailyFuelConsumption($rawData);
            $dailyDistance = 0;
            $dailyTripsCount = count($trips);
            $dailyStopsCount = count($stops);

            // Calculate daily distance from trips
            foreach ($trips as $trip) {
                $dailyDistance += $trip['distance'] ?? 0;
            }

            // Add to totals
            $totalFuelUsed += $dailyFuelUsed;
            $totalDistance += $dailyDistance;
            $totalTrips += $dailyTripsCount;
            $totalStops += $dailyStopsCount;

            // Track first and last times
            if ($firstValidData && (!$firstDayStart || $firstValidData['last_update'] < $firstDayStart)) {
                $firstDayStart = $firstValidData['last_update'];
            }
            if ($lastValidData && (!$lastDayEnd || $lastValidData['last_update'] > $lastDayEnd)) {
                $lastDayEnd = $lastValidData['last_update'];
            }

            // Create daily summary
            $dailySummary = [
                'date' => $formattedDate,
                'display_date' => $date,
                'vehicle' => $vehicle->license_plate,
                'total_fuel_used' => round($dailyFuelUsed, 2),
                'total_distance' => round($dailyDistance, 2),
                'total_trips' => $dailyTripsCount,
                'total_stops' => $dailyStopsCount,
                'start_time' => $firstValidData ? parseFlexibleTimestamp($firstValidData['last_update'])->format('H:i') : 'N/A',
                'end_time' => $lastValidData ? parseFlexibleTimestamp($lastValidData['last_update'])->format('H:i') : 'N/A',
            ];
            $dailySummaries[] = $dailySummary;

            // Add fuel consumption details
            foreach ($sequence as $detail) {
                $detailWithDate = $detail;
                $detailWithDate['date'] = $formattedDate;
                $detailWithDate['display_date'] = $date;

                // Format the detail for display
                if ($detail['type'] === 'trip') {
                    $detailWithDate['start_time_formatted'] = $detail['start_time']->format('H:i');
                    $detailWithDate['end_time_formatted'] = $detail['end_time']->format('H:i');
                    $detailWithDate['type'] = 'trip';
                    $detailWithDate['location_text'] = isset($detail['start_location']['address']) ?
                        $detail['start_location']['address'] :
                        round($detail['start_location']['lat'], 6) . ', ' . round($detail['start_location']['lng'], 6);
                    $detailWithDate['end_location_text'] = isset($detail['end_location']['address']) ?
                        $detail['end_location']['address'] :
                        round($detail['end_location']['lat'], 6) . ', ' . round($detail['end_location']['lng'], 6);
                    $detailWithDate['distance_formatted'] = round($detail['distance'], 2) . ' km';
                    $detailWithDate['fuel_formatted'] = round($detail['fuel_consumption'] ?? 0, 2) . ' L';

                    // Keep original fields for backward compatibility
                    $detailWithDate['start_location'] = $detailWithDate['location_text'];
                    $detailWithDate['end_location'] = $detailWithDate['end_location_text'];
                    $detailWithDate['trip_distance'] = $detailWithDate['distance_formatted'];
                    $detailWithDate['trip_fuel'] = $detailWithDate['fuel_formatted'];

                    $detailWithDate['fuel_consumption'] = $detail['fuel_consumption'] ?? 0;
                    $detailWithDate['distance'] = $detail['distance'] ?? 0;
                    $detailWithDate['efficiency'] = ($detail['distance'] ?? 0) > 0 ?
                        round(($detail['fuel_consumption'] ?? 0) / ($detail['distance'] ?? 1) * 100, 2) : 0;
                } else {
                    $detailWithDate['start_time_formatted'] = $detail['start_time']->format('H:i');
                    $detailWithDate['end_time_formatted'] = $detail['end_time'] === 'Ongoing' ? 'Ongoing' : $detail['end_time']->format('H:i');
                    $detailWithDate['type'] = 'stop';
                    $detailWithDate['location_text'] = isset($detail['location']['address']) ?
                        $detail['location']['address'] :
                        round($detail['location']['lat'], 6) . ', ' . round($detail['location']['lng'], 6);
                    $detailWithDate['distance_formatted'] = '0 km';
                    $detailWithDate['fuel_formatted'] = '0 L';

                    // Keep original fields for backward compatibility
                    $detailWithDate['location'] = $detailWithDate['location_text'];
                }

                $consolidatedDetails[] = $detailWithDate;
            }
        }

        // Calculate total durations
        $allStops = [];
        $allTrips = [];
        foreach ($allDailyData as $dayData) {
            $allStops = array_merge($allStops, $dayData['stops']);
            $allTrips = array_merge($allTrips, $dayData['trips']);
        }

        $totalTripDuration = self::calculateTotalTripDuration($allTrips);
        $totalStopDuration = self::calculateTotalStopDuration($allStops);

        // Create consolidated summary
        $consolidatedSummary = [
            'vehicle' => $vehicle->license_plate,
            'period' => Carbon::parse($startDate)->format('d/m/Y') . ' - ' . Carbon::parse($endDate)->format('d/m/Y'),
            'total_fuel_used' => round($totalFuelUsed, 2),
            'total_distance' => round($totalDistance, 2),
            'total_trips' => $totalTrips,
            'total_stops' => $totalStops,
            'start_time' => $firstDayStart ? parseFlexibleTimestamp($firstDayStart)->format('d/m/Y H:i') : 'N/A',
            'end_time' => $lastDayEnd ? parseFlexibleTimestamp($lastDayEnd)->format('d/m/Y H:i') : 'N/A',
            'total_days' => count($allDailyData),
            'average_daily_fuel' => count($allDailyData) > 0 ? round($totalFuelUsed / count($allDailyData), 2) : 0,
            'fuel_efficiency' => $totalDistance > 0 ? round($totalFuelUsed / $totalDistance * 100, 2) : 0,
            'total_trip_duration' => $totalTripDuration,
            'total_stop_duration' => $totalStopDuration,
        ];

        return [
            'summary' => $consolidatedSummary,
            'details' => $consolidatedDetails,
            'daily_summaries' => $dailySummaries
        ];
    }

    /**
     * Calculate daily fuel consumption from raw GPS data
     */
    private static function calculateDailyFuelConsumption($historyData)
    {
        if (empty($historyData)) {
            return 0;
        }

        $totalConsumption = 0;
        $lastFuelLevel = null;

        foreach ($historyData as $data) {
            $currentFuelLevel = getFuelConsumption($data);

            if ($lastFuelLevel !== null && $currentFuelLevel != $lastFuelLevel) {
                $fuelDifference = $currentFuelLevel - $lastFuelLevel;

                // If fuel level decreased, it's consumption
                if ($fuelDifference < 0) {
                    // Only count reasonable consumption (less than 20 liters at once)
                    if (abs($fuelDifference) < 20) {
                        $totalConsumption += abs($fuelDifference);
                    }
                }
                // Ignore refueling events (positive differences)
            }

            $lastFuelLevel = $currentFuelLevel;
        }

        return $totalConsumption;
    }

    /**
     * Calculate total trip duration from multiple trips
     */
    private static function calculateTotalTripDuration($trips)
    {
        $totalMinutes = 0;

        foreach ($trips as $trip) {
            try {
                // If we have a duration_minutes field, use it directly
                if (isset($trip['duration_minutes'])) {
                    $totalMinutes += $trip['duration_minutes'];
                    continue;
                }

                // For ongoing trips
                if (isset($trip['end_time']) && $trip['end_time'] === 'Ongoing') {
                    $startTime = $trip['start_time'] instanceof Carbon ? $trip['start_time'] : Carbon::parse($trip['start_time']);

                    // Determine if we're dealing with historical data
                    $isHistorical = !$startTime->copy()->startOfDay()->isToday();
                    $endTime = $isHistorical ? $startTime->copy()->startOfDay()->endOfDay() : now();

                    $totalMinutes += $startTime->diffInMinutes($endTime);
                    continue;
                }

                // For completed trips
                if (isset($trip['start_time']) && isset($trip['end_time'])) {
                    $startTime = $trip['start_time'] instanceof Carbon ? $trip['start_time'] : Carbon::parse($trip['start_time']);
                    $endTime = $trip['end_time'] instanceof Carbon ? $trip['end_time'] : Carbon::parse($trip['end_time']);

                    $totalMinutes += $startTime->diffInMinutes($endTime);
                }
            } catch (\Exception $e) {
                Log::warning("Trip duration calculation error: " . $e->getMessage(), [
                    'trip' => $trip,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        // Convert total minutes to hours and minutes
        $hours = floor($totalMinutes / 60);
        $minutes = $totalMinutes % 60;

        return sprintf("%d hours %d minutes", $hours, $minutes);
    }

    /**
     * Calculate total stop duration from multiple stops
     */
    private static function calculateTotalStopDuration($stops)
    {
        $totalMinutes = 0;

        foreach ($stops as $stop) {
            try {
                // If we have a duration_minutes field, use it directly
                if (isset($stop['duration_minutes'])) {
                    $totalMinutes += $stop['duration_minutes'];
                    continue;
                }

                // For ongoing stops
                if (isset($stop['end_time']) && $stop['end_time'] === 'Ongoing') {
                    $startTime = isset($stop['start_time']) ? $stop['start_time'] : Carbon::parse($stop['time']);
                    if (!($startTime instanceof Carbon)) {
                        $startTime = Carbon::parse($startTime);
                    }

                    // Determine if we're dealing with historical data
                    $isHistorical = !$startTime->copy()->startOfDay()->isToday();
                    $endTime = $isHistorical ? $startTime->copy()->startOfDay()->endOfDay() : now();

                    $totalMinutes += $startTime->diffInMinutes($endTime);
                    continue;
                }

                // For completed stops
                if (isset($stop['start_time']) && isset($stop['end_time'])) {
                    $startTime = $stop['start_time'] instanceof Carbon ? $stop['start_time'] : Carbon::parse($stop['start_time']);
                    $endTime = $stop['end_time'] instanceof Carbon ? $stop['end_time'] : Carbon::parse($stop['end_time']);

                    // If end time is before start time, it means the stop went into the next day
                    if ($endTime->lt($startTime)) {
                        $endTime->addDay();
                    }

                    $duration = $startTime->diffInMinutes($endTime);
                    $totalMinutes += $duration;
                }
            } catch (\Exception $e) {
                Log::warning("Stop duration calculation error: " . $e->getMessage(), [
                    'stop' => $stop,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        // Convert total minutes to hours and minutes
        $hours = floor(max(0, abs($totalMinutes)) / 60);
        $minutes = max(0, abs($totalMinutes)) % 60;

        return sprintf("%d hours %d minutes", $hours, $minutes);
    }
}
