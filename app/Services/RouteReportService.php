<?php

namespace App\Services;

use App\Models\Alarm;
use App\Models\Vehicle;
use App\Helpers\TripCalculationHelper;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class RouteReportService
{
    public static function generateRouteReports($vehicle,$date)
    {
        $vehicle = Vehicle::where('id', $vehicle)->first();
        if (!$vehicle) return [];


        $date = ($date ? Carbon::parse($date)->format('d-m-Y') : null) ?? now()->format('d-m-Y');

        // Create a unique cache key
        $cacheKey = "route_reports_{$vehicle->id}_{$date}";

        // Try to get from cache first
        if ($cachedData = cache()->get($cacheKey)) {
            return $cachedData;
        }

        $filePath = public_path("data/history/{$vehicle->imei}/{$date}.json");

        if (!file_exists($filePath)) return [];

        $historyData = json_decode(@file_get_contents($filePath), true);
        if (empty($historyData)) return [];

        // Use the TripCalculationHelper to get consistent trip and stop data
        $calculationResult = TripCalculationHelper::calculateTripsAndStops($historyData, $date);
        $stops = $calculationResult['stops'];
        $trips = $calculationResult['trips'];
        $firstValidData = $calculationResult['firstValidData'];
        $lastValidData = $calculationResult['lastValidData'];
        $tripStartOdometer = $calculationResult['tripStartOdometer'];
        $tripStartFuel = $calculationResult['tripStartFuel'];

        // Calculate overall trip metrics
        $result = ['summary' => null, 'details' => []];

        if ($firstValidData && $lastValidData) {
            $startTime = parseFlexibleTimestamp($firstValidData['last_update']);
            $endTime = parseFlexibleTimestamp($lastValidData['last_update']);

            // Calculate total distance using GPS coordinates
            $totalDistance = self::calculateTotalGpsDistance($historyData);

            // If GPS distance is too small, fall back to odometer
            if ($totalDistance < 1) {
                $totalDistance = max(0, (getOdometerValue($lastValidData) - getOdometerValue($firstValidData))) / 1000;
            }

            // Distribute the total distance among stops proportionally to their duration
            if (!empty($stops) && $totalDistance > 0) {
                self::distributeDistanceToStops($stops, $totalDistance);
            }

            // Fallback method 3: If no trips detected but first and last coordinates are different, create a trip
            if (
                empty($trips) &&
                ($firstValidData['latitude'] != $lastValidData['latitude'] ||
                    $firstValidData['longitude'] != $lastValidData['longitude'])
            ) {

                $tripDuration = $startTime->diffForHumans($endTime, true);
                $tripDurationMinutes = $startTime->diffInMinutes($endTime);

                if ($tripDurationMinutes > 1) {
                    // Calculate distance using GPS coordinates
                    $tripDistance = self::calculateGpsDistance(
                        $firstValidData['latitude'],
                        $firstValidData['longitude'],
                        $lastValidData['latitude'],
                        $lastValidData['longitude']
                    );

                    // If GPS distance is too small, calculate based on odometer
                    if ($tripDistance < 0.1) {
                        $startOdo = getOdometerValue($firstValidData);
                        $endOdo = getOdometerValue($lastValidData);
                        $tripDistance = self::calculateTripDistance($startOdo, $endOdo);
                    }

                    // Calculate fuel consumption
                    $startFuel = getFuelConsumption($firstValidData);
                    $endFuel = getFuelConsumption($lastValidData);

                    // Calculate fuel consumption - if end fuel is less than start fuel,
                    // it means fuel was consumed
                    if ($endFuel < $startFuel) {
                        $fuelConsumption = $startFuel - $endFuel;
                    } else {
                        // If end fuel is greater, it might be a refill or sensor error
                        // In this case, we'll use a very small consumption value based on distance
                        $fuelConsumption = $tripDistance * 0.05; // Estimate 0.05L per km
                    }

                    $trips[] = [
                        'start_time' => $startTime,
                        'end_time' => $endTime,
                        'duration' => $tripDuration,
                        'duration_minutes' => $tripDurationMinutes,
                        'start_location' => [
                            'lat' => $firstValidData['latitude'],
                            'lng' => $firstValidData['longitude'],
                            'address' => getAddressFromCoordinates($firstValidData['latitude'], $firstValidData['longitude'])
                        ],
                        'end_location' => [
                            'lat' => $lastValidData['latitude'],
                            'lng' => $lastValidData['longitude'],
                            'address' => getAddressFromCoordinates($lastValidData['latitude'], $lastValidData['longitude'])
                        ],
                        'distance' => $tripDistance,
                        'fuel_consumption' => $fuelConsumption
                    ];
                }
            }

            $mainTrip = [
                'start_time' => $startTime,
                'end_time' => $endTime,
                'start_location' => [
                    'lat' => $firstValidData['latitude'],
                    'lng' => $firstValidData['longitude'],
                    'address' => getAddressFromCoordinates($firstValidData['latitude'], $firstValidData['longitude'])
                ],
                'end_location' => [
                    'lat' => $lastValidData['latitude'],
                    'lng' => $lastValidData['longitude'],
                    'address' => getAddressFromCoordinates($lastValidData['latitude'], $lastValidData['longitude'])
                ],
                'start_odometer' => getOdometerValue($firstValidData),
                'end_odometer' => getOdometerValue($lastValidData),
                'start_fuel' => getFuelConsumption($firstValidData),
                'end_fuel' => getFuelConsumption($lastValidData),
                'stops' => $stops,
                'trips' => $trips,
                'duration' => $startTime->diffForHumans($endTime, true),
                'distance' => $totalDistance,
                'fuel_consumption' => self::calculateTotalFuelConsumption($firstValidData, $lastValidData, $totalDistance)
            ];

            $result = self::formatReports([$mainTrip], $vehicle, $date);
        }

        // Cache the result for 5 minutes
        cache()->put($cacheKey, $result, 300); // 300 seconds = 5 minutes

        return $result;
    }


    private static function formatReports($trips, $vehicle = null, $date = null)
    {
        if (empty($trips)) {
            return ['summary' => null, 'details' => []];
        }

        // Fetch relevant alarms for the vehicle and date
        $alarms = [];
        if ($vehicle && $date) {
            $alarms = Alarm::query()
                ->where('vehicle_id', $vehicle->id)
                ->whereDate('created_at', $date)
                ->whereIn('alarm_type', ['geofence_exit_event', 'geofence_entry_event'])
                ->get();
        }

        // Calculate overall summary
        $summary = [
            'vehicle' => $vehicle?->license_plate,
            'type' => 'summary',
            'total_trips' => count($trips),
            'start_time' => $trips[0]['start_time']->format('d-m-Y H:i') ?? '',
            'end_time' =>  end($trips)['end_time'] ? end($trips)['end_time']->format('d-m-Y H:i') : 'N/A',
            'total_distance' => number_format(array_sum(array_column($trips, 'distance')), 2) . ' km',
            'total_duration' => self::calculateTotalDuration($trips),
            'total_fuel' => number_format(array_sum(array_column($trips, 'fuel_consumption')), 2) . ' L',
            'total_stops' => array_sum(array_map(fn($trip) => count($trip['stops']), $trips)),
            'total_stop_duration' => self::calculateTotalStopDuration(array_merge(...array_column($trips, 'stops'))),
            'total_trip_duration' => self::calculateTotalTripDuration($trips[0]['trips'] ?? $trips[0]['stops'] ?? []),
            'start_point_address' => $trips[0]['start_location']['address'] ?? 'N/A',
            'end_point_address' => end($trips)['end_location']['address'] ?? 'N/A',
            // Add alarm information
            'geofence_events' => collect($alarms)->map(function ($alarm) {
                return [
                    'type' => $alarm->alarm_type,
                    'geofence' => $alarm->geofence?->name ?? 'N/A',
                    'time' => $alarm->created_at->format('H:i'),
                    'location' => $alarm->location ?? 'N/A'
                ];
            })
        ];

        // Format detailed records
        $details = [];

        // First add trip details if available
        if (!empty($trips[0]['trips'])) {
            foreach ($trips[0]['trips'] as $trip) {
                // Handle end_time calculation
                $endTime = $trip['end_time'];
                if ($endTime !== 'Ongoing') {
                    $endTime = $trip['end_time'] instanceof Carbon
                        ? $trip['end_time']->format('H:i')
                        : $trip['end_time'];
                }

                $startTime = $trip['start_time'] instanceof Carbon
                    ? $trip['start_time']->format('H:i')
                    : $trip['start_time'];

                // Calculate trip distance and fuel consumption if not already set
                $tripDistance = '-';
                $tripFuel = '-';

                if (isset($trip['distance'])) {
                    $tripDistance = number_format($trip['distance'], 2) . ' km';
                } elseif (isset($trip['start_location']['lat']) && isset($trip['end_location']['lat'])) {
                    // Calculate distance between start and end points
                    $distance = self::calculateGpsDistance(
                        $trip['start_location']['lat'],
                        $trip['start_location']['lng'],
                        $trip['end_location']['lat'],
                        $trip['end_location']['lng']
                    );
                    $tripDistance = number_format($distance, 2) . ' km';
                }

                if (isset($trip['fuel_consumption'])) {
                    $tripFuel = number_format($trip['fuel_consumption'], 2) . ' L';
                }

                $details[] = [
                    'type' => 'trip',
                    'start_time' => $startTime,
                    'end_time' => $endTime,
                    'duration' => $trip['duration'],
                    'start_location' => $trip['start_location']['address'] ?? 'N/A',
                    'end_location' => $trip['end_location']['address'] ?? 'N/A',
                    'trip_distance' => $tripDistance,
                    'trip_fuel' => $tripFuel,
                ];
            }
        }

        // Then add stop details
        foreach ($trips as $trip) {
            $lastStopDistance = 0;
            $lastStopFuel = 0;
            foreach ($trip['stops'] as $stop) {
                // Handle end_time calculation
                $endTime = $stop['end_time'];
                if ($endTime !== 'Ongoing') {
                    $endTime = $stop['end_time'] instanceof Carbon
                        ? $stop['end_time']->format('H:i')
                        : $stop['end_time'];
                }

                $details[] = [
                    'type' => 'stop',
                    'start_time' => $stop['time'],
                    'end_time' => $endTime,
                    'duration' => $stop['duration'],
                    'location' => $stop['location']['address'],
                    'odometer' => isset($stop['odometer']) ? number_format($stop['odometer'] / 1000, 2) . ' km' : '-',
                    'fuel' => isset($stop['fuel']) ? number_format($stop['fuel'], 2) . ' L' : '-',
                    'trip_distance' => number_format($stop['trip_distance'], 2) . ' km',
                    'trip_fuel' => number_format($stop['trip_fuel'], 2) . ' L',
                    'stop_distance' => number_format(max(0, self::calculateSegmentDistance($lastStopDistance, $stop['trip_distance'])), 2) . ' km',
                    'stop_fuel' => number_format($stop['trip_fuel'] - $lastStopFuel, 2) . ' L',
                ];
                $lastStopDistance = $stop['trip_distance'];
                $lastStopFuel = $stop['trip_fuel'];
            }
        }

        // Sort details by start_time
        usort($details, function ($a, $b) {
            $timeA = Carbon::createFromFormat('H:i', $a['start_time']);
            $timeB = Carbon::createFromFormat('H:i', $b['start_time']);
            return $timeA <=> $timeB;
        });

        // Merge consecutive stops at the same location
        $mergedDetails = [];
        $lastDetail = null;

        foreach ($details as $detail) {
            // If this is the first detail or it's a trip (not a stop), just add it
            if ($lastDetail === null || $detail['type'] === 'trip' || $lastDetail['type'] === 'trip') {
                $mergedDetails[] = $detail;
                $lastDetail = $detail;
                continue;
            }

            // Check if this is a stop at the same location as the previous stop
            if ($detail['type'] === 'stop' && $lastDetail['type'] === 'stop' &&
                $detail['location'] === $lastDetail['location']) {

                // Get the last index
                $lastIndex = count($mergedDetails) - 1;

                // Merge the stops by updating the end time and duration
                $startTime = Carbon::createFromFormat('H:i', $lastDetail['start_time']);
                $endTime = $detail['end_time'] === 'Ongoing' ?
                    ($startTime->copy()->startOfDay()->isToday() ? now() : $startTime->copy()->startOfDay()->endOfDay()) :
                    Carbon::createFromFormat('H:i', $detail['end_time']);

                $duration = $startTime->diffForHumans($endTime, true);

                $mergedDetails[$lastIndex]['end_time'] = $detail['end_time'];
                $mergedDetails[$lastIndex]['duration'] = $duration;

                // Keep the latest values for other fields
                if (isset($detail['trip_distance'])) {
                    $mergedDetails[$lastIndex]['trip_distance'] = $detail['trip_distance'];
                }
                if (isset($detail['trip_fuel'])) {
                    $mergedDetails[$lastIndex]['trip_fuel'] = $detail['trip_fuel'];
                }
            } else {
                // Different location or type, add as a new detail
                $mergedDetails[] = $detail;
                $lastDetail = $detail;
            }
        }

        // Replace the details with the merged version
        $details = $mergedDetails;

        return [
            'summary' => $summary,
            'details' => $details
        ];
    }

    private static function calculateTotalDuration($trips)
    {
        $totalMinutes = 0;

        // Determine if we're dealing with historical data
        $isHistorical = false;
        $reportDate = null;

        if (!empty($trips) && isset($trips[0]['start_time']) && $trips[0]['start_time'] instanceof \Carbon\Carbon) {
            $reportDate = $trips[0]['start_time']->copy()->startOfDay();
            $isHistorical = !$reportDate->isToday();
        }

        // Calculate end time for ongoing trips
        $endTimeForOngoing = $isHistorical ? $reportDate->copy()->endOfDay() : now();

        foreach ($trips as $trip) {
            // Check if end_time is a string (like 'Ongoing') or a Carbon instance
            if ($trip['end_time'] instanceof \Carbon\Carbon) {
                $totalMinutes += $trip['start_time']->diffInMinutes($trip['end_time']);
            } elseif ($trip['end_time'] === 'Ongoing') {
                // For ongoing trips, calculate duration until end of day for historical data
                $totalMinutes += $trip['start_time']->diffInMinutes($endTimeForOngoing);
            }
        }

        $hours = floor($totalMinutes / 60);
        $minutes = $totalMinutes % 60;
        return sprintf("%d hours %d minutes", $hours, $minutes);
    }

    private static function calculateTotalStopDuration($stops)
    {
        $totalMinutes = 0;

        foreach ($stops as $stop) {
            try {
                // If we have a duration_minutes field, use it directly
                if (isset($stop['duration_minutes'])) {
                    $totalMinutes += $stop['duration_minutes'];
                    continue;
                }

                // For ongoing stops
                if (isset($stop['end_time']) && $stop['end_time'] === 'Ongoing') {
                    $startTime = isset($stop['start_time']) ? $stop['start_time'] : Carbon::parse($stop['time']);
                    if (!($startTime instanceof Carbon)) {
                        $startTime = Carbon::parse($startTime);
                    }

                    // Determine if we're dealing with historical data
                    $isHistorical = !$startTime->copy()->startOfDay()->isToday();
                    $endTime = $isHistorical ? $startTime->copy()->startOfDay()->endOfDay() : now();

                    $totalMinutes += $startTime->diffInMinutes($endTime);
                    continue;
                }

                // Get start time
                $startTime = isset($stop['start_time']) ? $stop['start_time'] : Carbon::parse($stop['time']);
                if (!($startTime instanceof Carbon)) {
                    $startTime = Carbon::parse($startTime);
                }

                // Get end time
                $endTime = null;
                if (isset($stop['end_time'])) {
                    $endTime = $stop['end_time'] instanceof Carbon
                        ? clone $stop['end_time']
                        : Carbon::parse($stop['end_time']);
                }

                if (!$endTime) {
                    // If we have a duration string, parse it
                    if (isset($stop['duration'])) {
                        $totalMinutes += self::durationToMinutes($stop['duration']);
                    }
                    continue;
                }

                // Ensure both times are on the same date
                $endTime->setDate(
                    $startTime->year,
                    $startTime->month,
                    $startTime->day
                );

                // If end time is before start time, it means the stop went into the next day
                if ($endTime->lt($startTime)) {
                    $endTime->addDay();
                }

                $duration = $startTime->diffInMinutes($endTime);
                $totalMinutes += $duration; // Ensure we don't add negative minutes

            } catch (\Exception $e) {
                Log::warning("Stop duration calculation error: " . $e->getMessage(), [
                    'stop' => $stop,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        // Convert total minutes to hours and minutes
        $hours = floor(max(0, abs($totalMinutes)) / 60);
        $minutes = max(0, abs($totalMinutes)) % 60;

        return sprintf("%d hours %d minutes", $hours, $minutes);
    }

    private static function calculateTotalTripDuration($trips)
    {
        $totalTripMinutes = 0;

        // If trips is an array of trips with duration_minutes
        if (!empty($trips) && isset($trips[0]['duration_minutes'])) {
            foreach ($trips as $trip) {
                // Use the duration_minutes directly if available
                $totalTripMinutes += $trip['duration_minutes'];
            }
        }
        // Fallback to calculating from stops if no trip data
        else {
            $lastTripEnd = null;

            // Process all stops to extract trip durations
            foreach ($trips as $index => $stop) {
                // Skip if we don't have proper timestamps
                if (!isset($stop['start_time']) || !isset($stop['end_time'])) {
                    continue;
                }

                // Get the current stop's start and end times
                $currentStopStart = $stop['start_time'];

                // Handle ongoing stops for historical data
                if ($stop['end_time'] === 'Ongoing') {
                    $isHistorical = !$currentStopStart->copy()->startOfDay()->isToday();
                    $currentStopEnd = $isHistorical ? $currentStopStart->copy()->startOfDay()->endOfDay() : now();
                } else {
                    $currentStopEnd = $stop['end_time'];
                }

                // If this is the first stop, there's no trip before it
                if ($index === 0) {
                    $lastTripEnd = $currentStopStart;
                    continue;
                }

                // Calculate trip duration between previous stop end and current stop start
                if ($lastTripEnd instanceof \Carbon\Carbon && $currentStopStart instanceof \Carbon\Carbon) {
                    $tripMinutes = $lastTripEnd->diffInMinutes($currentStopStart);
                    $totalTripMinutes += $tripMinutes;
                }

                // Update last trip end time for next iteration
                $lastTripEnd = $currentStopEnd;
            }
        }

        // If we still don't have any trip duration, use the total duration minus stop duration
        if ($totalTripMinutes == 0 && !empty($trips) && isset($trips[0]['start_time']) && isset($trips[0]['end_time'])) {
            // Get the first trip's start time and last trip's end time
            $startTime = $trips[0]['start_time'];
            $endTime = $trips[count($trips) - 1]['end_time'];

            if ($startTime instanceof \Carbon\Carbon && ($endTime instanceof \Carbon\Carbon || $endTime === 'Ongoing')) {
                // Handle ongoing trips for historical data
                if ($endTime === 'Ongoing') {
                    $isHistorical = !$startTime->copy()->startOfDay()->isToday();
                    $endTimeCarbon = $isHistorical ? $startTime->copy()->startOfDay()->endOfDay() : now();
                } else {
                    $endTimeCarbon = $endTime;
                }

                $totalDurationMinutes = $startTime->diffInMinutes($endTimeCarbon);

                // Subtract stop durations if available
                $stopDurationMinutes = 0;
                foreach ($trips as $trip) {
                    if (isset($trip['duration'])) {
                        $stopDurationMinutes += self::durationToMinutes($trip['duration']);
                    }
                }

                $totalTripMinutes = max(0, $totalDurationMinutes - $stopDurationMinutes);
            }
        }

        // Convert total minutes to hours and minutes
        $hours = floor($totalTripMinutes / 60);
        $minutes = $totalTripMinutes % 60;

        return sprintf("%d hours %d minutes", $hours, $minutes);
    }




    private static function durationToMinutes($duration)
    {
        // Convert duration string like "2 hours 30 minutes" to minutes
        preg_match('/(\d+)\s*hours?\s*(\d+)\s*minutes?/', $duration, $matches);
        if (count($matches) === 3) {
            return ($matches[1] * 60) + $matches[2];
        }
        return 0;
    }




    // Calculate distance between segments
    private static function calculateSegmentDistance($lastDistance, $currentDistance)
    {
        if ($lastDistance == 0) {
            return $currentDistance;
        }
        return $currentDistance - $lastDistance;
    }


    // Calculate distance between two GPS coordinates using Haversine formula
    private static function calculateGpsDistance($lat1, $lon1, $lat2, $lon2)
    {
        if (!$lat1 || !$lon1 || !$lat2 || !$lon2) {
            return 0;
        }

        // Convert latitude and longitude from degrees to radians
        $lat1 = deg2rad((float)$lat1);
        $lon1 = deg2rad((float)$lon1);
        $lat2 = deg2rad((float)$lat2);
        $lon2 = deg2rad((float)$lon2);

        // Haversine formula
        $dlat = $lat2 - $lat1;
        $dlon = $lon2 - $lon1;
        $a = sin($dlat / 2) * sin($dlat / 2) + cos($lat1) * cos($lat2) * sin($dlon / 2) * sin($dlon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $distance = 6371 * $c; // Earth radius in km

        return $distance;
    }

    // Calculate trip distance in km (kept for backward compatibility)
    private static function calculateTripDistance($startOdometer, $endOdometer)
    {
        if (!$startOdometer || !$endOdometer) {
            return 0;
        }

        // Calculate the difference and convert to km
        $distanceMeters = max(0, $endOdometer - $startOdometer);

        // If the distance is unreasonably large (over 1000 km), it might be an odometer reset
        if ($distanceMeters > 1000000) { // 1000 km in meters
            return 0;
        }

        return $distanceMeters / 1000; // Convert to km
    }

    // Calculate total fuel consumption with improved logic
    private static function calculateTotalFuelConsumption($firstData, $lastData, $totalDistance)
    {
        $startFuel = getFuelConsumption($firstData);
        $endFuel = getFuelConsumption($lastData);

        // If end fuel is less than start fuel, fuel was consumed
        if ($endFuel < $startFuel) {
            return $startFuel - $endFuel;
        }
        // If end fuel is greater, it might be a refill or sensor error
        else {
            // Use a reasonable estimate based on distance
            return $totalDistance * 0.05; // Estimate 0.05L per km
        }
    }

    // Calculate total distance using GPS coordinates
    private static function calculateTotalGpsDistance($historyData)
    {
        $totalDistance = 0;
        $lastLat = null;
        $lastLon = null;

        foreach ($historyData as $data) {
            if (!isset($data['latitude']) || !isset($data['longitude'])) {
                continue;
            }

            $currentLat = (float)$data['latitude'];
            $currentLon = (float)$data['longitude'];

            if ($lastLat !== null && $lastLon !== null) {
                // Calculate distance between consecutive points
                $segmentDistance = self::calculateGpsDistance($lastLat, $lastLon, $currentLat, $currentLon);

                // Only add if the distance is reasonable (not a GPS jump)
                if ($segmentDistance < 5) { // Less than 5km between consecutive points
                    $totalDistance += $segmentDistance;
                }
            }

            $lastLat = $currentLat;
            $lastLon = $currentLon;
        }

        return $totalDistance;
    }


    // Distribute total distance among stops proportionally to their duration
    private static function distributeDistanceToStops(&$stops, $totalDistance)
    {
        // Calculate total duration of all stops
        $totalDuration = 0;
        foreach ($stops as $stop) {
            if ($stop['end_time'] === 'Ongoing') {
                $endTime = now();
            } else {
                $endTime = $stop['end_time'];
            }

            $startTime = $stop['start_time'];
            $durationMinutes = $startTime->diffInMinutes($endTime);
            $totalDuration += $durationMinutes;
        }

        if ($totalDuration == 0) {
            return;
        }

        // Distribute distance proportionally
        $remainingDistance = $totalDistance;
        $lastStopDistance = 0;

        for ($i = 0; $i < count($stops); $i++) {
            if ($stops[$i]['end_time'] === 'Ongoing') {
                $endTime = now();
            } else {
                $endTime = $stops[$i]['end_time'];
            }

            $startTime = $stops[$i]['start_time'];
            $durationMinutes = $startTime->diffInMinutes($endTime);

            // Calculate proportion of total duration
            $proportion = $durationMinutes / $totalDuration;

            // Allocate distance based on proportion
            if ($i == count($stops) - 1) {
                // Last stop gets remaining distance to avoid rounding errors
                $stopDistance = $remainingDistance;
            } else {
                $stopDistance = $totalDistance * $proportion;
                $remainingDistance -= $stopDistance;
            }

            // Update stop with new distance
            $stops[$i]['trip_distance'] = $lastStopDistance + $stopDistance;
            $lastStopDistance = $stops[$i]['trip_distance'];
        }
    }
}
