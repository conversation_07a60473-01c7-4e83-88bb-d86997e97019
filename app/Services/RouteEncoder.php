<?php

namespace App\Services;

class RouteEncoder
{
    public static function encode($points)
    {
        $encodedString = '';
        $previousLat = 0;
        $previousLng = 0;

        foreach ($points as $point) {
            $lat = $point['lat'];
            $lng = $point['lng'];
            
            $encodedString .= self::encodeNumber($lat - $previousLat);
            $encodedString .= self::encodeNumber($lng - $previousLng);
            
            $previousLat = $lat;
            $previousLng = $lng;
        }

        return $encodedString;
    }

    public static function decode($encodedString)
    {
        $points = [];
        $index = 0;
        $len = strlen($encodedString);
        $lat = 0;
        $lng = 0;

        while ($index < $len) {
            $shift = 0;
            $result = 0;
            
            do {
                $byte = ord($encodedString[$index++]) - 63;
                $result |= ($byte & 0x1f) << $shift;
                $shift += 5;
            } while ($byte >= 0x20);
            
            $lat += (($result & 1) ? ~($result >> 1) : ($result >> 1));

            $shift = 0;
            $result = 0;
            
            do {
                $byte = ord($encodedString[$index++]) - 63;
                $result |= ($byte & 0x1f) << $shift;
                $shift += 5;
            } while ($byte >= 0x20);
            
            $lng += (($result & 1) ? ~($result >> 1) : ($result >> 1));

            $points[] = [
                'lat' => $lat * 1e-5,
                'lng' => $lng * 1e-5
            ];
        }

        return $points;
    }

    private static function encodeNumber($num)
    {
        $num = round($num * 1e5);
        $num = ($num < 0) ? ~($num << 1) : ($num << 1);
        $encoded = '';
        
        while ($num >= 0x20) {
            $encoded .= chr((0x20 | ($num & 0x1f)) + 63);
            $num >>= 5;
        }
        
        $encoded .= chr($num + 63);
        return $encoded;
    }
}