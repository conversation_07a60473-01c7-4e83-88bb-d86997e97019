<?php

namespace App\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Blade::directive('formatDuration', function ($expression) {
            return "<?php
                \$minutes = $expression;
                if (\$minutes >= 1440) {
                    echo floor(\$minutes / 1440) . ' ' . __('ttranslations.day(s)');
                } elseif (\$minutes >= 60) {
                    echo floor(\$minutes / 60) . ' ' . __('translations.hour(s)');
                } else {
                    echo \$minutes . ' ' . __('translations.minute(s)');
                }
            ?>";
        });
        
        Blade::directive('formatDate', function ($expression) {
            return "<?php echo \Carbon\Carbon::parse($expression)->format('d-m-Y'); ?>";
        });
    }
}
