<?php

namespace App\Console\Commands;

use App\Models\ExportedReport;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CleanupOldReports extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reports:cleanup {--days=90 : Number of days to keep reports}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old exported reports and their files';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $cutoffDate = Carbon::now()->subDays($days);
        
        $this->info("Cleaning up reports older than {$days} days (before {$cutoffDate->format('Y-m-d')})...");
        
        // Get old reports
        $oldReports = ExportedReport::where('created_at', '<', $cutoffDate)->get();
        
        if ($oldReports->isEmpty()) {
            $this->info('No old reports found to clean up.');
            return;
        }
        
        $deletedCount = 0;
        $failedCount = 0;
        
        foreach ($oldReports as $report) {
            try {
                // Delete the file from storage
                $report->deleteFile();
                
                // Delete the database record
                $report->delete();
                
                $deletedCount++;
                
                if ($deletedCount % 10 == 0) {
                    $this->info("Deleted {$deletedCount} reports...");
                }
                
            } catch (\Exception $e) {
                $this->error("Failed to delete report {$report->id}: " . $e->getMessage());
                $failedCount++;
            }
        }
        
        $this->info("Cleanup completed:");
        $this->info("- Deleted: {$deletedCount} reports");
        
        if ($failedCount > 0) {
            $this->warn("- Failed: {$failedCount} reports");
        }
        
        // Also clean up empty directories
        $this->cleanupEmptyDirectories();
    }
    
    /**
     * Clean up empty report directories
     */
    private function cleanupEmptyDirectories()
    {
        $this->info('Cleaning up empty directories...');
        
        $reportsPath = storage_path('app/public/reports');
        
        if (!is_dir($reportsPath)) {
            return;
        }
        
        // Get all year directories
        $yearDirs = glob($reportsPath . '/*', GLOB_ONLYDIR);
        
        foreach ($yearDirs as $yearDir) {
            // Get all month directories in this year
            $monthDirs = glob($yearDir . '/*', GLOB_ONLYDIR);
            
            foreach ($monthDirs as $monthDir) {
                // Check if month directory is empty
                if ($this->isDirectoryEmpty($monthDir)) {
                    rmdir($monthDir);
                    $this->info("Removed empty directory: " . basename($monthDir));
                }
            }
            
            // Check if year directory is empty after cleaning months
            if ($this->isDirectoryEmpty($yearDir)) {
                rmdir($yearDir);
                $this->info("Removed empty directory: " . basename($yearDir));
            }
        }
    }
    
    /**
     * Check if directory is empty
     */
    private function isDirectoryEmpty($dir)
    {
        $handle = opendir($dir);
        while (false !== ($entry = readdir($handle))) {
            if ($entry != "." && $entry != "..") {
                closedir($handle);
                return false;
            }
        }
        closedir($handle);
        return true;
    }
}
