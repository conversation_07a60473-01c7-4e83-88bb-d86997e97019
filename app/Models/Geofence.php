<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Geofence extends Model
{
    protected $fillable = [
        'name',
        'location',
        'push_notification',
        'email_notification',
        'is_active',
        'geofence_data',
        'user_id',
    ];

    public function vehicles()
    {
        return $this->belongsToMany(Vehicle::class, 'vehicle_geofences');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
