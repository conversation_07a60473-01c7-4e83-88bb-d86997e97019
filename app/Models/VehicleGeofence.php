<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VehicleGeofence extends Model
{
    protected $fillable = [
        'vehicle_id',
        'geofence_id',
        'vehicle_imei',
        'zone_number',
        'push_notification',
        'email_notification',
        'geofence_in_notification',
        'geofence_out_notification',
    ];

    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class);
    }

    public function geofence()
    {
        return $this->belongsTo(Geofence::class);
    }
}
