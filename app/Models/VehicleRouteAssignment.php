<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VehicleRouteAssignment extends Model
{
    protected $fillable = [
        'vehicle_id',
        'start_point',
        'start_point_lat',
        'start_point_lng',
        'start_point_odometer',
        'departure_time',
        'end_point',
        'end_point_lat',
        'end_point_lng',
        'end_point_odometer',
        'start_date',
        'end_date',
        'is_repeat',
        'status',
        'distance',
        'duration',
        'remarks',
        'route',
        'start_point_status',
        'start_point_completed_at',
        'end_point_status',
        'end_point_completed_at',
        'start_point_fuel',
        'end_point_fuel',
        
    ];

    // Relationships
    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class);
    }

    public function stops()
    {
        return $this->hasMany(RouteStop::class, 'vehicle_route_assignment_id');
    }
}
