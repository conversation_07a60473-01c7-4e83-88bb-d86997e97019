<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Alarm extends Model
{
    protected $fillable = [
        'alarm_type',
        'longitude',
        'latitude',
        'location',
        'geofence_id',
        'vehicle_id',
        'imei',
        'alarm_value',
    ];

    // Relationship with Vehicle
    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class);
    }

    // Relationship with Geofence
    public function geofence()
    {
        return $this->belongsTo(Geofence::class);
    }
}
