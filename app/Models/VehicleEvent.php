<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VehicleEvent extends Model
{
    protected $fillable = [
        'user_id',
        'vehicle_id',
        'event_type',
        'other_event_type',
        'description',
        'start_date',
        'end_date',
        'is_under_maintenance',
        'maintenance_status',
        'has_alert',
        'alert_recipients',
        'attachments',
        'alert_type',
    ];

    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class);
    }
}
