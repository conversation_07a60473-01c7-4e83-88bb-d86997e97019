<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Vehicle extends Model
{
    protected $fillable = [
        'imei',
        'license_plate',
        'driver_id',
        'model',
        'type',
        'icon',
        'mileage',
        'current_odometer_reading',
        'year_of_registration',
        'vin',
        'status',
        'pin',
        'local_lock_ibutton',
    ];

    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }

    public function geofences()
    {
        return $this->belongsToMany(Geofence::class, 'vehicle_geofences');
    }

    public function events()
    {
        return $this->hasMany(VehicleEvent::class);
    }

    public function vehicleUsers()
    {
        return $this->hasMany(VehicleUser::class);
    }

    public function currentRoute()
    {
        return $this->hasOne(VehicleRouteAssignment::class)
            ->whereDate('start_date', now()->toDateString()) // Start date is today
            // ->where(function ($query) {
            //     $query->whereDate('end_date', '>=', now()->toDateString()) // End date is today or later
            //         ->orWhereNull('end_date'); // OR end_date is NULL (ongoing)
            // })
            ->orderByRaw('CASE WHEN departure_time IS NULL THEN 1 ELSE 0 END, departure_time ASC'); // NULL departure_time last
    }
}
