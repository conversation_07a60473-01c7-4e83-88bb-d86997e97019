<?php

namespace App\Jobs;

use App\Exports\ReportExport;
use App\Models\ExportedReport;
use App\Services\FuelReportService;
use App\Services\RouteReportService;
use App\Services\MultiDayReportService;
use App\Services\OptimizedMultiDayReportService;
use App\Models\Alarm;
use App\Models\VehicleUser;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;
use Exception;

class ProcessReportExport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected ExportedReport $exportedReport;

    /**
     * Create a new job instance.
     */
    public function __construct(ExportedReport $exportedReport)
    {
        $this->exportedReport = $exportedReport;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Update status to processing
            $this->exportedReport->update([
                'status' => 'processing',
                'started_at' => now(),
            ]);

            // Generate the report data with progress tracking
            $data = $this->generateOptimizedReportData();

            // Create the file
            $filePath = $this->createReportFile($data);

            // Get file size
            $fileSize = Storage::disk('public')->size($filePath);

            // Update the exported report with completion details
            $this->exportedReport->update([
                'status' => 'completed',
                'file_path' => $filePath,
                'file_size' => $fileSize,
                'report_stats' => $this->extractReportStats($data),
                'completed_at' => now(),
            ]);

        } catch (Exception $e) {
            // Update status to failed with error message
            $this->exportedReport->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'completed_at' => now(),
            ]);

            // Re-throw the exception to mark the job as failed
            throw $e;
        }
    }

    /**
     * Generate optimized report data with progress tracking.
     */
    private function generateOptimizedReportData(): array
    {
        $reportType = $this->exportedReport->report_type;
        $vehicleId = $this->exportedReport->vehicle_id;
        $startDate = $this->exportedReport->start_date->format('Y-m-d');
        $endDate = $this->exportedReport->end_date->format('Y-m-d');

        // Progress callback to update status
        $progressCallback = function($progress, $message) {
            \Log::info("Report progress: {$progress}% - {$message}", [
                'report_id' => $this->exportedReport->id
            ]);
        };

        switch ($reportType) {
            case 'routes':
                return $this->generateOptimizedRouteReportData($vehicleId, $startDate, $endDate, $progressCallback);

            case 'fuel_consumption':
                return $this->generateOptimizedFuelReportData($vehicleId, $startDate, $endDate, $progressCallback);

            case 'alarms':
                return $this->generateAlarmReportData($vehicleId, $startDate, $endDate);

            default:
                throw new Exception("Unsupported report type: {$reportType}");
        }
    }

    /**
     * Generate report data based on the report configuration (fallback).
     */
    private function generateReportData(): array
    {
        return $this->generateOptimizedReportData();
    }

    /**
     * Generate optimized route report data for date range.
     */
    private function generateOptimizedRouteReportData($vehicleId, $startDate, $endDate, $progressCallback = null): array
    {
        // Check if it's a single day
        if ($startDate === $endDate) {
            return RouteReportService::generateRouteReports($vehicleId, $startDate);
        }

        // Use OptimizedMultiDayReportService for multi-day reports
        return OptimizedMultiDayReportService::generateOptimizedRouteReports($vehicleId, $startDate, $endDate, $progressCallback);
    }

    /**
     * Generate route report data for date range (fallback).
     */
    private function generateRouteReportData($vehicleId, $startDate, $endDate): array
    {
        return $this->generateOptimizedRouteReportData($vehicleId, $startDate, $endDate);
    }

    /**
     * Generate optimized fuel report data for date range.
     */
    private function generateOptimizedFuelReportData($vehicleId, $startDate, $endDate, $progressCallback = null): array
    {
        // Check if it's a single day
        if ($startDate === $endDate) {
            return FuelReportService::generateFuelReports($vehicleId, $startDate);
        }

        // Use OptimizedMultiDayReportService for multi-day reports
        return OptimizedMultiDayReportService::generateOptimizedFuelReports($vehicleId, $startDate, $endDate, $progressCallback);
    }

    /**
     * Generate fuel report data for date range (fallback).
     */
    private function generateFuelReportData($vehicleId, $startDate, $endDate): array
    {
        return $this->generateOptimizedFuelReportData($vehicleId, $startDate, $endDate);
    }

    /**
     * Generate alarm report data for date range.
     */
    private function generateAlarmReportData($vehicleId, $startDate, $endDate): array
    {
        $user = $this->exportedReport->user;
        
        $query = Alarm::query()
            ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                $query->whereIn('vehicle_id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
            })
            ->when($vehicleId, function ($query) use ($vehicleId) {
                $query->where('vehicle_id', $vehicleId);
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with(['vehicle', 'geofence'])
            ->orderBy('created_at', 'desc')
            ->get();

        return $query->map(function ($alarm) {
            return [
                'Vehicle' => $alarm->vehicle?->license_plate ?? 'N/A',
                'Alarm Type' => __('translations.' . $alarm->alarm_type) ?? 'N/A',
                'Triggered At' => $alarm->created_at->format('Y-m-d H:i:s'),
                'Geofence' => $alarm->geofence?->name ?? 'N/A',
                'Location' => $alarm->location ?? 'N/A',
            ];
        })->toArray();
    }

    /**
     * Consolidate route data from multiple days.
     */
    private function consolidateRouteData($allData): array
    {
        if (empty($allData)) {
            return ['summary' => [], 'details' => [], 'daily_summaries' => []];
        }

        $consolidatedDetails = [];
        $dailySummaries = [];
        $totalDistance = 0;
        $totalFuel = 0;
        $totalTrips = 0;
        $totalStops = 0;
        $totalTripDuration = 0;
        $totalStopDuration = 0;

        foreach ($allData as $dayData) {
            $date = $dayData['date'];
            $data = $dayData['data'];
            
            // Add daily summary
            if (isset($data['summary'])) {
                $dailySummaries[] = array_merge($data['summary'], ['date' => $date]);
                
                // Accumulate totals
                $totalDistance += (float) str_replace(' km', '', $data['summary']['total_distance'] ?? '0');
                $totalFuel += (float) str_replace(' L', '', $data['summary']['total_fuel'] ?? '0');
                $totalTrips += (int) $data['summary']['total_trips'] ?? 0;
                $totalStops += (int) $data['summary']['total_stops'] ?? 0;
            }
            
            // Add details with date prefix
            foreach ($data['details'] ?? [] as $detail) {
                $consolidatedDetails[] = array_merge($detail, ['date' => $date]);
            }
        }

        // Create consolidated summary
        $firstDay = $allData[0]['data']['summary'] ?? [];
        $lastDay = end($allData)['data']['summary'] ?? [];
        
        $consolidatedSummary = [
            'vehicle' => $firstDay['vehicle'] ?? 'N/A',
            'period' => $this->exportedReport->start_date->format('Y-m-d') . ' to ' . $this->exportedReport->end_date->format('Y-m-d'),
            'total_distance' => round($totalDistance, 2) . ' km',
            'total_fuel' => round($totalFuel, 2) . ' L',
            'total_trips' => $totalTrips,
            'total_stops' => $totalStops,
            'start_time' => $firstDay['start_time'] ?? 'N/A',
            'end_time' => $lastDay['end_time'] ?? 'N/A',
        ];

        return [
            'summary' => $consolidatedSummary,
            'details' => $consolidatedDetails,
            'daily_summaries' => $dailySummaries
        ];
    }

    /**
     * Consolidate fuel data from multiple days.
     */
    private function consolidateFuelData($allData): array
    {
        // Similar logic to consolidateRouteData but for fuel reports
        if (empty($allData)) {
            return ['summary' => [], 'details' => [], 'daily_summaries' => []];
        }

        $consolidatedDetails = [];
        $dailySummaries = [];
        $totalFuelUsed = 0;
        $totalDistance = 0;
        $totalTrips = 0;
        $totalStops = 0;

        foreach ($allData as $dayData) {
            $date = $dayData['date'];
            $data = $dayData['data'];
            
            if (isset($data['summary'])) {
                $dailySummaries[] = array_merge($data['summary'], ['date' => $date]);
                
                $totalFuelUsed += (float) $data['summary']['total_fuel_used'] ?? 0;
                $totalDistance += (float) $data['summary']['total_distance'] ?? 0;
                $totalTrips += (int) $data['summary']['total_trips'] ?? 0;
                $totalStops += (int) $data['summary']['total_stops'] ?? 0;
            }
            
            foreach ($data['details'] ?? [] as $detail) {
                $consolidatedDetails[] = array_merge($detail, ['date' => $date]);
            }
        }

        $firstDay = $allData[0]['data']['summary'] ?? [];
        $lastDay = end($allData)['data']['summary'] ?? [];
        
        $consolidatedSummary = [
            'vehicle' => $firstDay['vehicle'] ?? 'N/A',
            'period' => $this->exportedReport->start_date->format('Y-m-d') . ' to ' . $this->exportedReport->end_date->format('Y-m-d'),
            'total_fuel_used' => round($totalFuelUsed, 2),
            'total_distance' => round($totalDistance, 2),
            'total_trips' => $totalTrips,
            'total_stops' => $totalStops,
            'start_time' => $firstDay['start_time'] ?? 'N/A',
            'end_time' => $lastDay['end_time'] ?? 'N/A',
        ];

        return [
            'summary' => $consolidatedSummary,
            'details' => $consolidatedDetails,
            'daily_summaries' => $dailySummaries
        ];
    }

    /**
     * Create the report file (PDF or Excel).
     */
    private function createReportFile($data): string
    {
        $fileName = $this->exportedReport->file_name;
        $fileType = $this->exportedReport->file_type;
        $reportType = $this->exportedReport->report_type;

        $directory = 'reports/' . date('Y/m');
        $filePath = $directory . '/' . $fileName;

        // Ensure directory exists
        Storage::disk('public')->makeDirectory($directory);

        if ($fileType === 'excel') {
            Excel::store(
                new ReportExport($data, $reportType, $this->exportedReport->start_date->format('Y-m-d')),
                $filePath,
                'public'
            );
        } else { // PDF
            $pdf = Pdf::loadView('exports.report', [
                'data' => $data,
                'reporting_type' => $reportType,
                'period' => $this->exportedReport->start_date->format('Y-m-d') . ' to ' . $this->exportedReport->end_date->format('Y-m-d')
            ]);
            
            Storage::disk('public')->put($filePath, $pdf->output());
        }

        return $filePath;
    }

    /**
     * Extract statistics from report data.
     */
    private function extractReportStats($data): array
    {
        $stats = [];

        if (isset($data['summary'])) {
            $summary = $data['summary'];
            $stats = [
                'total_records' => count($data['details'] ?? []),
                'period' => $summary['period'] ?? 'N/A',
                'vehicle' => $summary['vehicle'] ?? 'N/A',
            ];

            // Add type-specific stats
            if (isset($summary['total_distance'])) {
                $stats['total_distance'] = $summary['total_distance'];
            }
            if (isset($summary['total_fuel']) || isset($summary['total_fuel_used'])) {
                $stats['total_fuel'] = $summary['total_fuel'] ?? $summary['total_fuel_used'];
            }
            if (isset($summary['total_trips'])) {
                $stats['total_trips'] = $summary['total_trips'];
            }
            if (isset($summary['total_stops'])) {
                $stats['total_stops'] = $summary['total_stops'];
            }
        } else {
            $stats = [
                'total_records' => is_array($data) ? count($data) : 0,
            ];
        }

        return $stats;
    }
}
